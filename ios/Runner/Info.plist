<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Pagos</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string></string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>El permiso de la cámara es requerido para tomar una foto de perfil.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>El permiso de ubicación es requerido para calcular la distancia de un pedido</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>El permiso de ubicación es requerido para calcular la distancia de un pedido</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>YourPurposeKey</key>
		<string>El permiso de ubicación temporal es requerido para calcular la distancia de un pedido</string>
	</dict>
	<key>NSLocationUsageDescription</key>
	<string>El permiso de ubicación es requerido por dispositivos viejos</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>El permiso de ubicación es requerido para calcular la distancia de un pedido</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>El permiso de microfono es requerido para la funcionalidad de voz a texto</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>El permiso de la galería es requerido para seleccionar una imagen de perfil.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Funcionalidad de voz a texto</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
