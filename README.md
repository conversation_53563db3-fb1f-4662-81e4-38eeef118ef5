# pagosmovil

# v1.0.0 // dapackages v2.9.26

Execute:

- flutter packages get
- flutter packages pub run flutter_launcher_icons:main
- flutter run --release

Flutter Doctor:

- Flutter (Channel stable, 3.24.0, on macOS 15.0 24A5309e darwin-arm64, locale en-MX)
- Android toolchain - develop for Android devices (Android SDK version 34.0.0)
- Xcode - develop for iOS and macOS (Xcode 15.3)
- Chrome - develop for the web
- Visual Studio - develop for Windows (Visual Studio Community 2022 17.5.0)
- Android Studio (version 2024.1)
- VS Code (version 1.92.0)

Build

- https://esflutter.dev/docs/deployment/android
- flutter build apk --build-name=1.0.0 --build-number=1
- flutter build appbundle --build-name=1.0.0 --build-number=1
