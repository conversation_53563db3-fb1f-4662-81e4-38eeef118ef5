// Flutter Core
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Third-Party Packages
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:no_context_navigation/no_context_navigation.dart';

// Project Imports (same package)
import 'package:pagosmovil/src/controllers/ticket_edc_controller.dart';

// Local Configurations & Utilities
import 'firebase_options.dart';
import 'src/DAPackagesRef.dart';
import 'src/app_config/app_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final session = DASessionProvider();
  await session.initPrefs();
  await session.loadSession();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  runApp(MyApp());
  configLoading();
  initControllers();
}

void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.circle
    ..loadingStyle = EasyLoadingStyle.light
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..maskType = EasyLoadingMaskType.black
    ..userInteractions = false
    ..dismissOnTap = false;
}

void initControllers() {
  Get.lazyPut(() => TicketEdcController());
}

// ignore: must_be_immutable
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    String _initial = (DASessionProvider.toLogin) ? 'home' : 'login';

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.white,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.grey[900],
      ),
    );

    Color _primaryColor = AppConfig.options.primaryColor;
    Color _accentColor = (AppConfig.options.accentColor) ??
        Theme.of(context).colorScheme.secondary;

    Widget app = MaterialApp(
      debugShowCheckedModeBanner: false,
      title: AppConfig.appName,
      initialRoute: _initial,
      navigatorKey: NavigationService.navigationKey,
      onGenerateRoute: (RouteSettings settings) =>
          AppRoutes.onGenerateRoute(context, settings),
      routes: {
        'login': (BuildContext context) => DALoginPagosPage(
              appName: AppConfig.appName,
              licence: AppConfig.licence,
              idUsuarioTipo: AppConfig.idUsuarioTipo,
              loginLogo: AppConfig.loginLogo,
            ),
        'home': (BuildContext context) => HomePage(),
      },
      theme: ThemeData(
        appBarTheme: AppBarTheme(
          systemOverlayStyle: SystemUiOverlayStyle.dark, // 2
        ),
        primaryColor: _primaryColor,
        textSelectionTheme: TextSelectionThemeData(
          cursorColor: _primaryColor,
          selectionColor: _primaryColor,
          selectionHandleColor: _primaryColor,
        ),
        colorScheme:
            ColorScheme.light(primary: _primaryColor, secondary: _accentColor)
                .copyWith(surface: Colors.grey[900]),
        useMaterial3: false,
      ),
      builder: EasyLoading.init(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('es', ''), // Spanish, no country code
      ],
    );

    return DAMainProvider.multiProvider(app);
  }
}
