// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC4l4IShgedHStmOb_V2sU5E2fdgMY-1s4',
    appId: '1:540841514436:web:9bc6752e81f55ed1440091',
    messagingSenderId: '540841514436',
    projectId: 'intelisis-tuakfe',
    authDomain: 'intelisis-tuakfe.firebaseapp.com',
    databaseURL: 'https://intelisis-tuakfe.firebaseio.com',
    storageBucket: 'intelisis-tuakfe.firebasestorage.app',
    measurementId: 'G-WF2NC8W0HY',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo',
    appId: '1:540841514436:android:6d01546def36b3d9440091',
    messagingSenderId: '540841514436',
    projectId: 'intelisis-tuakfe',
    databaseURL: 'https://intelisis-tuakfe.firebaseio.com',
    storageBucket: 'intelisis-tuakfe.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD0QHikG1NzEFl-AIgEzU5Ihfb_qcOBuss',
    appId: '1:540841514436:ios:db32179a583d1400440091',
    messagingSenderId: '540841514436',
    projectId: 'intelisis-tuakfe',
    databaseURL: 'https://intelisis-tuakfe.firebaseio.com',
    storageBucket: 'intelisis-tuakfe.firebasestorage.app',
    iosBundleId: 'com.intelisis.pagosmovil',
  );
}
