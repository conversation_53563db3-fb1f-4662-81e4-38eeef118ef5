import 'package:http_interceptor/http_interceptor.dart';
import '../main_DAPackages.dart';

class Interceptor implements InterceptorContract {
  DASessionProvider _prov = DASessionProvider();

  @override
  Future<RequestData> interceptRequest({required RequestData data}) async {
    try {
      if (await _prov.validaRefresh()) {
        //AGREGAR ACCESS TOKEN EN CADA PETICION
        String? accessToken = _prov.session.access;
        data.headers["Authorization"] = "Bearer $accessToken";
        return data;
      } else {
        return data;
      }
    } catch (e) {
      throw e;
    }
  }

  @override
  Future<ResponseData> interceptResponse({required ResponseData data}) async {
    return data;
  }
}
