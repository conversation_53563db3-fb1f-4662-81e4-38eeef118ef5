import 'package:http_interceptor/http_interceptor.dart';
import '../main_DAPackages.dart';
import 'http_interceptor.dart';
import 'http_retry_policy.dart';
// import 'http_routes.dart';

class HttpProvider {
  DASessionProvider prefs = DASessionProvider();

  final _client = InterceptedClient.build(interceptors: [
    // Interceptor(),
  ]);

  final _clientInterceptor = InterceptedClient.build(
    interceptors: [
      Interceptor(),
    ],
    retryPolicy: ExpiredTokenRetryPolicy(),
  );

  Future test() async {
    var res;
    try {
      final String loginUri = prefs.apiUri + "/IVersion";
      res = await _client.get(loginUri.toUri()).timeout(Duration(seconds: 10));
    } catch (e) {
      print(e);
      throw "Uri Inválida.";
    }
    return res;
  }

  Future login(SessionModel credenciales, String licence) async {
    var res;
    try {
      final String loginUri = prefs.apiUri + "/Login";
      final String data = grantPassword(credenciales, licence);

      res = await _client.post(
        loginUri.toUri(),
        body: data,
        headers: {
          "Accept": "application/json",
          "Content-Type": "application/x-www-form-urlencoded"
        },
      ).timeout(Duration(seconds: 25));
    } catch (e) {
      throw "Servidor no válido.";
    }
    return res;
  }

  Future refresh(SessionModel credenciales) async {
    var res;
    try {
      final String loginUri = prefs.apiUri + "/Login";
      final String data = grantRefresh(credenciales);

      res = await _client.post(
        loginUri.toUri(),
        body: data,
        headers: {
          "Accept": "application/json",
          "Content-Type": "application/x-www-form-urlencoded"
        },
      ).timeout(Duration(seconds: 25));
    } catch (e) {
      throw e;
    }
    return res;
  }

  Future logOut() async {
    var res;
    try {
      String logOutUri = prefs.apiUri + "/LogOut";
      res = await _clientInterceptor.post(logOutUri.toUri(),
          body: json.encode({"Estacion": prefs.session.estacion}),
          headers: {
            'Content-Type': 'application/json'
          }).timeout(Duration(seconds: 5));
    } catch (e) {
      return false;
    }
    return (res.statusCode == 200) ? true : false;
  }

  _invalidToken() {
    DASessionProvider _sessionProv = DASessionProvider();
    if (!_sessionProv.appIsOffline) {
      prefs.reset();
      _sessionProv.notifyLogOut();
      throw "SESIÓN EXPIRADA";
    } else {
      print('app is offline');
    }
  }

  Future<dynamic> getImage(String uri) async {
    try {
      var res;
      String sUri = prefs.apiUri + uri;
      res = await _clientInterceptor.get(sUri.toUri(), headers: {
        "Content-Type": "application/json"
      }).timeout(Duration(seconds: 360));

      if (res.statusCode == 401) {
        _invalidToken();
      }
      final bytes = res?.bodyBytes;
      return bytes;
    } catch (e) {
      throw e;
    }
  }

  Future<dynamic> set(String uri, dynamic data) async {
    var res;
    try {
      String sUri = prefs.apiUri + uri;
      res = await _clientInterceptor
          .put(sUri.toUri(),
              headers: {"Content-Type": "application/json"}, body: data)
          .timeout(Duration(seconds: 360));

      if (res.statusCode == 401) {
        _invalidToken();
      }
    } catch (e) {
      throw e;
    }

    return jsonDecode(res.body);
  }

  Future<dynamic> get(String uri) async {
    //, BuildContext context
    var res;
    String sUri = prefs.apiUri + uri;
    try {
      res = await _clientInterceptor.get(sUri.toUri(), headers: {
        "Content-Type": "application/json"
      }).timeout(Duration(seconds: 360));

      if (res.statusCode == 401) {
        _invalidToken();
      }
    } catch (e) {
      throw e;
    }

    if (res.body.toString().toUpperCase().indexOf('"OKREF"') >= 0) {
      String _okRef = res.body
          .substring(res.body.toString().toUpperCase().indexOf('OKREF') + 6);
      _okRef = _okRef.substring(_okRef.indexOf('"') + 1);
      _okRef = _okRef.substring(0, _okRef.indexOf('"'));
      if (_okRef != "") {
        throw _okRef;
      }
    }

    return jsonDecode(res.body);
  }

  Future<dynamic> post(String uri, dynamic data) async {
    var res;
    try {
      String sUri = prefs.apiUri + uri;
      res = await _clientInterceptor
          .post(
            sUri.toUri(),
            headers: {"Content-Type": "application/json"},
            body: jsonEncode(data),
          )
          .timeout(Duration(seconds: 360));

      if (res.statusCode == 401) {
        _invalidToken();
      }
    } catch (e) {
      throw e;
    }

    if (res.body.toString().toUpperCase().indexOf('"OKREF"') >= 0) {
      String _okRef = res.body
          .substring(res.body.toString().toUpperCase().indexOf('OKREF') + 6);
      if (_okRef.toLowerCase().indexOf('null') >= 0) {
        _okRef = '';
      } else {
        _okRef = _okRef.substring(_okRef.indexOf('"') + 1);
        _okRef = _okRef.substring(0, _okRef.indexOf('"'));
      }

      if (_okRef != "") {
        throw _okRef;
      }
    }

    return jsonDecode(res.body);
  }

  Future<dynamic> put(String uri, dynamic data) async {
    var res;
    try {
      String sUri = prefs.apiUri + uri;
      res = await _clientInterceptor
          .put(
            sUri.toUri(),
            headers: {"Content-Type": "application/json"},
            body: jsonEncode(data),
          )
          .timeout(Duration(seconds: 360));

      if (res.statusCode == 401) {
        _invalidToken();
      }
    } catch (e) {
      throw e;
    }

    if (res.body.toString().toUpperCase().indexOf('"OKREF"') >= 0) {
      String _okRef = res.body
          .substring(res.body.toString().toUpperCase().indexOf('OKREF') + 6);
      if (_okRef.toLowerCase().indexOf('null') >= 0) {
        _okRef = '';
      } else {
        _okRef = _okRef.substring(_okRef.indexOf('"') + 1);
        _okRef = _okRef.substring(0, _okRef.indexOf('"'));
      }

      if (_okRef != "") {
        throw _okRef;
      }
    }

    return jsonDecode(res.body);
  }

  Future<dynamic> delete(String uri, dynamic data) async {
    var res;
    try {
      String sUri = prefs.apiUri + uri;
      res = await _clientInterceptor
          .delete(
            sUri.toUri(),
            headers: {"Content-Type": "application/json"},
            body: jsonEncode(data),
          )
          .timeout(Duration(seconds: 360));

      if (res.statusCode == 401) {
        _invalidToken();
      }
    } catch (e) {
      throw e;
    }

    if (res.body.toString().toUpperCase().indexOf('"OKREF"') >= 0) {
      String _okRef = res.body
          .substring(res.body.toString().toUpperCase().indexOf('OKREF') + 6);
      if (_okRef.toLowerCase().indexOf('null') >= 0) {
        _okRef = '';
      } else {
        _okRef = _okRef.substring(_okRef.indexOf('"') + 1);
        _okRef = _okRef.substring(0, _okRef.indexOf('"'));
      }

      if (_okRef != "") {
        throw _okRef;
      }
    }

    return jsonDecode(res.body);
  }
}
