import 'package:http_interceptor/http_interceptor.dart';
import '../main_DAPackages.dart';

class ExpiredTokenRetryPolicy extends RetryPolicy {
  final DASessionProvider _prov = DASessionProvider();

  final _client = InterceptedClient.build(interceptors: [
    // nterceptor(),
  ]);

  // ignore: avoid_init_to_null
  Future<dynamic>? cachRequest = null;

  @override
  Future<bool> shouldAttemptRetryOnResponse(ResponseData response) async {
    if (response.statusCode == 401) {
      if (cachRequest == null) {
        cachRequest = _client.post(
          Uri.parse(_prov.apiUri + '/Login'),
          body: grantRefresh(_prov.session),
          headers: {"Content-Type": "application/json"},
        );
      }
      var res = await cachRequest;
      cachRequest = null;
      if (res.statusCode == 200) {
        var decoded = json.decode(res.body);
        _prov.session.access = decoded["access_token"];
        _prov.session.refresh = decoded["refresh_token"];
        return true;
      } else {
        return false;
      }
    }
    return false;
  }
}
