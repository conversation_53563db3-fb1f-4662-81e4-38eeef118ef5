import 'package:flutter/material.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALayoutListMapPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutListMapPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutListMapPage createState() => _DALayoutListMapPage();
}

class _DALayoutListMapPage extends State<DALayoutListMapPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  GlobalKey<RefreshIndicatorState> _fresh =
      new GlobalKey<RefreshIndicatorState>();

  DALayoutListMapModel? _modelLayout;
  List<dynamic> _dataListWidget = [];

  bool _isLoading = true;
  bool _validID = false;

  @override
  void initState() {
    super.initState();
    DAController.refAvatar(context, () {
      setState(() {});
    });
    setState(() {
      _isLoading = true;
    });
  }

  @override
  void dispose() {
    FocusScope.of(context).dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    try {
      if (_modelLayout == null) {
        _modelLayout =
            ModalRoute.of(context)!.settings.arguments as DALayoutListMapModel;
        _dataListWidget = [];
        _isLoading = true;
      }

      // Validamos provider
      Provider.of<DALayoutListMapQueueProv>(context);
      final DALayoutListMapProvider? _layoutProv =
          DALayoutListMapQueueProv.getByRefID(_modelLayout!.refID);
      _validID = (_modelLayout!.refID == DALayoutListMapQueueProv.refID);

      if (_validID) {
        if (_layoutProv!.error != '') {
          throw _layoutProv.error;
        }

        if (!_layoutProv.isLoading) {
          _isLoading = _layoutProv.isLoading;
          _dataListWidget = _layoutProv.data;
          //moveCurrent();
          setState(() {});
        }

        if (_layoutProv.refreshing && _modelLayout!.onRefresh != null) {
          _fresh.currentState!.show();
          _layoutProv.forceRefresh = false;
        }
      }

      layoutMovs() {
        try {
          return DaMapList(
            config: _modelLayout,
            data: _dataListWidget,
            target: _modelLayout!.currPosition,
            onCardTap: _modelLayout!.cardConfig.onCardTap,
          );
        } catch (e) {
          print(e.toString());
          //DAToast(context, 'mapBody: ' + e.toString());
        }
      }

      Widget _builder = LoadingOverlay(
        isLoading: _isLoading,
        child: Scaffold(
          key: _scaffoldKey,
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
          floatingActionButton: _modelLayout!.floatingActionButton,
          body: layoutMovs(),
          backgroundColor: Color.fromRGBO(244, 243, 243, 1),
          bottomNavigationBar: _modelLayout!.bottomNavigationBar,
        ),
      );

      return _builder;
    } catch (e) {
      DAToast(context, e.toString());
      return Container();
    }
  }
}

class DALayoutListMapQueueProv with ChangeNotifier {
  static DALayoutListMapQueueProv _instancia =
      new DALayoutListMapQueueProv._internal();
  DALayoutListMapQueueProv._internal();

  factory DALayoutListMapQueueProv() {
    return _instancia;
  }

  static String _refID = '';

  static get refID {
    return _refID;
  }

  static List<DALayoutListMapProvider> _mainQueue = [];

  static bool exists(String refID) {
    bool exists = false;

    for (final DALayoutListMapProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return true;
      }
    }

    return exists;
  }

  static add(DALayoutListMapProvider prov) {
    if (exists(prov.refID)) {
      removeByRefID(prov.refID);
    }
    _mainQueue.add(prov);
  }

  static DALayoutListMapProvider getByRefID(String refID) {
    for (final DALayoutListMapProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return _currProv;
      }
    }

    DALayoutListMapProvider _newProv =
        new DALayoutListMapProvider(refID: refID);
    _mainQueue.add(_newProv);
    return _newProv;
  }

  static removeByRefID(String refID) {
    if (exists(refID)) {
      _mainQueue.removeWhere((item) => item.refID == refID);
    }
  }

  notify(String refID) {
    _refID = refID;
    notifyListeners();
  }
}

class DALayoutListMapProvider {
  final String refID;

  @override
  DALayoutListMapProvider({
    required this.refID,
  });

  DALayoutListMapQueueProv queue = DALayoutListMapQueueProv();
  static List<Widget> _bodyWidgets = [];

  // ignore: avoid_init_to_null
  static bool? _isLoading = null; // NO QUITAR = null
  static String _error = '';
  static dynamic _data;
  static bool? _submiting;
  static bool? _forceRefresh = false;

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    queue.notify(this.refID);
  }

  bool get submiting {
    return _submiting ?? false;
  }

  bool get refreshing {
    return _forceRefresh ?? false;
  }

  dynamic get data {
    return _data;
  }

  set data(dynamic value) {
    _data = value;
    queue.notify(this.refID);
  }

  List<Widget> get bodyWidgets {
    return _bodyWidgets;
  }

  set forceRefresh(dynamic value) {
    _forceRefresh = value;
  }

  pageRefresh() async {
    _forceRefresh = true;
    queue.notify(this.refID);
  }

  _resetProv() {
    if (DALayoutListMapQueueProv.exists(this.refID)) {
      DALayoutListMapQueueProv.removeByRefID(this.refID);
    }

    DALayoutListMapQueueProv.add(this);
  }

  set bodyWidgets(List<Widget>? value) {
    _bodyWidgets = (value) ?? [];

    _resetProv();
    queue.notify(this.refID);
  }

  notify() async {
    queue.notify(this.refID);
  }
}
