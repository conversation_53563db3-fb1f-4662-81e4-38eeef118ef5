import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALoginPage extends StatefulWidget {
  final Widget loginLogo;
  final String appName;
  final String licence;
  final int idUsuarioTipo;

  DALoginPage({
    required this.loginLogo,
    required this.appName,
    required this.licence,
    required this.idUsuarioTipo,
  });

  @override
  _DALoginPageState createState() => _DALoginPageState();
}

class _DALoginPageState extends State<DALoginPage> {
  // Para agilizar snackbar y loading
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  DASessionProvider _prov = DASessionProvider();
  SessionModel _modelForm = new SessionModel();
  bool _saving = false;
  late SharedPreferences _prefs;

  // Form controllers
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController _emailController = new TextEditingController();
  TextEditingController _passController = new TextEditingController();

  // Auxiliares
  final focus = FocusNode();

  // API
  TextEditingController _apiController = new TextEditingController();
  void onValueChange() {
    setState(() {
      _apiController.text;
    });
  }

  @override
  void initState() {
    super.initState();
    _initPrefs();
    _apiController.text = _prov.apiUri;
    _apiController.addListener(onValueChange);
  }

  Future<void> _initPrefs() async {
    _prefs = await SharedPreferences.getInstance();
  }

  @override
  Widget build(BuildContext context) {
    var login = DAPageLogin(
      isLoading: _saving,
      keyLoading: _scaffoldKey,
      formKey: _formKey,
      floatingActionButton: _configAPI(),
      body: _loginForm(), // cargamelo BD
    );

    // Widget noPop = WillPopScope(
    //   onWillPop: () async => false,
    //   child: login,
    // );

    return login;
  }

  Widget _configAPI() {
    return DAFloatingActionButton(
      heroTag: 'login',
      icon: _prov.connected ? Icons.cloud_done : Icons.cloud_off,
      onPressed: () => _apiDialog(),
    );
  }

  void _apiDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return DAInputDialog(
          title: 'Configurar app',
          subtitle: 'Ingrese Uri de API First',
          okText: 'Conectar',
          input: <Widget>[
            DAInput(
              refID: 'apiCfg',
              padding: 0.0,
              tipo: DAInputType.url,
              controller: _apiController,
              onSaved: (value) {
                _prov.apiUri = _apiController.text;
              },
            ),
          ],
          onPressed: _validaAPI,
        );
      },
    );
  }

  Future<void> _validaAPI() async {
    try {
      if (_prov.apiUri != _apiController.text.trim()) {
        _prov.reset();
        _prov.clear();
      }
      Navigator.of(context).pop();
      setState(() {
        _prov.connected = false;
        _prov.apiUri = _apiController.text.trim();
        _saving = true;
      }); // Loading start

      final httpProv = new HttpProvider();
      var res = await httpProv.test();
      _prov.connected = (res.statusCode == 200);

      String msg = (res.statusCode == 200)
          ? 'Conexión Exitosa'
          : 'Error de servidor: ${res.reasonPhrase.toString()}';
      DAToast(context, msg);

      setState(() => _saving = false); // L
    } catch (e) {
      // ignore: unused_element
      setState(() {
        _saving = false; // Loading end
        _prov.connected = false;
        DAToast(context, e.toString());
      });
    }
  }

  // Login
  Widget _loginForm() {
    var form = Container(
      margin: EdgeInsets.symmetric(horizontal: 15.0),
      child: ListView(
        children: [
          SizedBox(height: 100.0),
          widget.loginLogo,
          SizedBox(height: 30.0),
          Center(
            child: Text(
              widget.appName,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 20.0,
                  fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(height: 30.0),
          DAInput(
            refID: 'user',
            tipo: DAInputType.email,
            label: 'Usuario',
            textInputAction: TextInputAction.next,
            controller: _emailController,
            onFieldSubmitted: (v) {
              FocusScope.of(context).requestFocus(focus);
            },
            onSaved: (value) {
              _modelForm.username = value;
            },
          ),
          SizedBox(height: 30.0),
          DAInput(
            refID: 'password',
            tipo: DAInputType.password,
            label: 'Contraseña',
            focusNode: focus,
            textInputAction: TextInputAction.go,
            controller: _passController,
            onFieldSubmitted: (value) {
              _login();
            },
            onSaved: (value) {
              _modelForm.password = value;
            },
          ),
          SizedBox(height: 60.0),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: DAButton(
              label: 'Ingresar',
              onPressed: () async => await _login(),
            ),
          ),
          SizedBox(height: 30.0),
        ],
      ),
    );

    return form;
  }

  Future<void> _login() async {
    // setState(() async {
    try {
      final keepKeys = {'apiconn', 'apiUri'};
      final keys = _prefs.getKeys();
      for (var key in keys) {
        if (!keepKeys.contains(key)) {
          await _prefs.remove(key);
        }
      }

      final form = _formKey.currentState;
      form!.save();

      if (!_prov.connected) {
        DAToast(context, 'Requiere configurar un API válida');
        return;
      }

      if (form.validate()) {
        setState(() => _saving = true); // Loading start

        if (_prov.session.estacion != null) {
          _modelForm.estacion = _prov.session.estacion;
        }

        final httpProv = new HttpProvider();
        var res = await httpProv.login(_modelForm, widget.licence);

        if (res.statusCode == 200) {
          //almacenar refresh token y access token
          var respuesta = json.decode(res.body);
          var profiles = json.decode(respuesta['profiles']);

          if (profiles.length > 0) {
            profiles.forEach((profile) {
              if (profile['IDUsuarioTipo'].toString() !=
                      respuesta['IDUsuarioTipo'].toString() &&
                  profile['IDUsuarioTipo'].toString() ==
                      widget.idUsuarioTipo.toString()) {
                respuesta['IDUsuario'] = profile['IDUsuario'];
                respuesta['Usuario'] = profile['Usuario'];
                respuesta['IDUsuarioTipo'] = profile['IDUsuarioTipo'];
                respuesta['IDRole'] = profile['IDRole'];
                respuesta['Empresa'] = profile['Empresa'];
                respuesta['Sucursal'] = profile['Sucursal'];
              }
            });
          }

          if (respuesta['IDUsuarioTipo'].toString() !=
              widget.idUsuarioTipo.toString()) {
            await httpProv.logOut();
            throw ('Tipo de usuario inválido');
          }

          _prov.session = sessionModelFromLogin(respuesta);
          _formKey.currentState!.reset();
          Navigator.pushNamed(context, '/home');
        } else if (res.statusCode == 400) {
          var respuesta = json.decode(res.body);
          String msg = respuesta["error_description"].toString();
          // Valida si la estación en sesión es valida
          if (msg == 'Estacion invalida.') {
            _prov.reset();
            _prov.resetEstacion();
            _modelForm.estacion = _prov.session.estacion;
            msg = '$msg Favor de intentar nuevamente.';
          }
          DAToast(context, msg);
          //_snakeBar(msg);
        } else {
          //ERROR NO CONTROLADO DESDE EL API
          String msg = res.reasonPhrase.toString();
          DAToast(context, 'Error de servidor: $msg');
        }

        setState(() => _saving = false); // Loading end
      }
    } catch (e) {
      setState(() => _saving = false); // Loading end
      DAToast(context, e.toString());
    }
  }
}
