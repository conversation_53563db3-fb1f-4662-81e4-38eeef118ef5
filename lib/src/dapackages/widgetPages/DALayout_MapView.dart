import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALayoutMapViewPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutMapViewPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutMapViewPage createState() => _DALayoutMapViewPage();
}

class _DALayoutMapViewPage extends State<DALayoutMapViewPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  DALayoutMapViewModel _modelLayoutMapPage =
      new DALayoutMapViewModel(title: 'Cargando...');
  List<Widget> _bodyWidgets = [];
  bool _isLoading = true;

  // Map Controllers
  late Position currPosition;

  @override
  void initState() {
    super.initState();
    DAController.refAvatar(context, () {
      setState(() {});
    });
    DALayoutMapViewProvider _layoutProvL = DALayoutMapViewProvider();
    _layoutProvL.isLoading = (_layoutProvL.isLoading) ?? true;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    try {
      final _layoutProv = Provider.of<DALayoutMapViewProvider>(context);
      _isLoading = _layoutProv.isLoading;

      if (!_isLoading) {
        _isLoading = _layoutProv.isLoading;
        _bodyWidgets = _layoutProv.bodyWidgets;
        setState(() {});
      }

      layoutMapView() {
        try {
          if (_layoutProv.error != '') {
            throw _layoutProv.error;
          } else if (_modelLayoutMapPage.title == 'Cargando...') {
            _modelLayoutMapPage = ModalRoute.of(context)!.settings.arguments
                as DALayoutMapViewModel;
            _bodyWidgets = [];
            _isLoading = true;
            getPosition();
          }

          DaMapView _mapView = DaMapView(
            child: DAWidgetPageList(
              children: _bodyWidgets,
              daScreenAlignment: _modelLayoutMapPage.daScreenAlignment,
              viewportFraction: .98,
            ),
            title: _modelLayoutMapPage.title,
            hasBackButton: _modelLayoutMapPage.hasBackButton,
            target: new LatLng(
              currPosition.latitude,
              currPosition.longitude,
            ),
          );

          setState(() {});
          return _mapView;
        } catch (e) {
          print(e.toString());
        }
      }

      Widget _builder = LoadingOverlay(
        isLoading: (_isLoading),
        child: Scaffold(
          key: _scaffoldKey,
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
          floatingActionButton: _modelLayoutMapPage.floatingActionButton,
          body: layoutMapView(),
          backgroundColor: Color.fromRGBO(244, 243, 243, 1),
          bottomNavigationBar: _modelLayoutMapPage.bottomNavigationBar,
        ),
      );

      return _builder;
    } catch (e) {
      DAToast(context, e.toString());
      return Container();
    }
  }

  Future<void> getPosition({String? filter}) async {
    try {
      // Register my position
      currPosition = await DAController.getCurrentLocation();
      setState(() {});
    } catch (e) {
      _isLoading = false;
      setState(() {});
      DAToast(context, e.toString());
    }
  }
}

class DALayoutMapViewProvider with ChangeNotifier {
  static DALayoutMapViewProvider _instancia =
      new DALayoutMapViewProvider._internal();
  DALayoutMapViewProvider._internal();
  static List<Widget> _bodyWidgets = [];

  factory DALayoutMapViewProvider() {
    return _instancia;
  }

  static String _error = '';
  // ignore: avoid_init_to_null
  static bool? _isLoading = null;

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    notifyListeners();
  }

  List<Widget> get bodyWidgets {
    return _bodyWidgets;
  }

  notify() {
    notifyListeners();
  }

  set bodyWidgets(List<Widget> value) {
    _bodyWidgets = value;
    notifyListeners();
  }
}
