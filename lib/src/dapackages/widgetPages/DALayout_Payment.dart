// ignore_for_file: unused_field
import 'package:easy_pdf_viewer/easy_pdf_viewer.dart';
import 'package:flutter/material.dart';

import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALayoutPaymentPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutPaymentPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutPaymentPage createState() => _DALayoutPaymentPage();
}

class _DALayoutPaymentPage extends State<DALayoutPaymentPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  GlobalKey<RefreshIndicatorState> _fresh = GlobalKey<RefreshIndicatorState>();
  DALayoutPaymentFilters? _modelLayout;

  List<Widget> _bodyWidgets = [];
  bool _isLoading = true;
  bool _tapOther = false;
  bool _focusTime = false;
  bool _leave = false;
  bool _validID = false;

  @override
  void initState() {
    super.initState();
    DAController.refAvatar(context, () {
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Definir el modelo layout si es null
    if (_modelLayout == null) {
      _modelLayout =
          ModalRoute.of(context)!.settings.arguments as DALayoutPaymentFilters;
      _bodyWidgets = [];
      // Manejar el estado de carga inicial
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _isLoading = true; // Establecer el estado de carga
        });
      });
    }

    // Validamos el provider
    Provider.of<DALPaymentQueueProv>(context);
    final DALayoutPaymentProvider? _layoutProv =
        DALPaymentQueueProv.getByRefID(_modelLayout!.refID);
    _validID = (_modelLayout!.refID == DALPaymentQueueProv.refID);

    if (_validID) {
      if (_layoutProv!.error != '') {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          DAToast(context, _layoutProv.error);
        });
        return Container(); // Manejo apropiado si hay un error
      }

      if (!_layoutProv.isLoading) {
        setState(() {
          _isLoading = _layoutProv.isLoading;
          _bodyWidgets = _layoutProv.bodyWidgets;
          _layoutProv.scaffoldKey = _scaffoldKey;
        });
      }

      if (_layoutProv.refreshing && _modelLayout!.onRefresh != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _fresh.currentState!.show();
          _layoutProv.forceRefresh = false;
        });
      }
    }

    // Creamos una función interna para el contenido principal
    Widget _buildContent() {
      return LoadingOverlay(
        isLoading: _isLoading,
        child: Scaffold(
          key: _scaffoldKey,
          appBar: AppBar(
            title: Text(_modelLayout!.title),
            automaticallyImplyLeading: _modelLayout!.hasBackButton ?? false,
            actions: <Widget>[
              IconButton(
                  onPressed: _modelLayout!.onNotification as void Function()?,
                  icon: Icon(Icons.notifications))
            ],
          ),
          body: _modelLayout!.isPDF!
              ? SafeArea(child: PDFViewer(document: _modelLayout!.pdfPath!))
              : ListView(
                  shrinkWrap: true,
                  children: _bodyWidgets,
                ),
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          endDrawerEnableOpenDragGesture: true,
          bottomNavigationBar: _modelLayout!.bottomNavigationBar,
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade700
              : Color.fromRGBO(244, 243, 243, 1),
          floatingActionButton: _modelLayout!.floatingActionButton,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        ),
      );
    }

    return _buildContent();
  }
}

class DALPaymentQueueProv with ChangeNotifier {
  static DALPaymentQueueProv _instancia = new DALPaymentQueueProv._internal();
  DALPaymentQueueProv._internal();

  factory DALPaymentQueueProv() {
    return _instancia;
  }

  static String _refID = '';

  static get refID {
    return _refID;
  }

  static List<DALayoutPaymentProvider> _mainQueue = [];

  static bool exists(String refID) {
    bool exists = false;

    for (final DALayoutPaymentProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return true;
      }
    }

    return exists;
  }

  static add(DALayoutPaymentProvider prov) {
    if (exists(prov.refID)) {
      removeByRefID(prov.refID);
    }
    _mainQueue.add(prov);
  }

  static DALayoutPaymentProvider getByRefID(String refID) {
    for (final DALayoutPaymentProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return _currProv;
      }
    }

    DALayoutPaymentProvider _newProv =
        new DALayoutPaymentProvider(refID: refID);
    _mainQueue.add(_newProv);
    return _newProv;
  }

  static removeByRefID(String refID) {
    if (exists(refID)) {
      _mainQueue.removeWhere((item) => item.refID == refID);
    }
  }

  notify(String refID) {
    _refID = refID;
    notifyListeners();
  }
}

class DALayoutPaymentProvider {
  final String refID;

  @override
  DALayoutPaymentProvider({
    required this.refID,
  });

  DALPaymentQueueProv queue = DALPaymentQueueProv();
  static List<Widget> _bodyWidgets = [];

  // ignore: avoid_init_to_null
  static bool? _isLoading = null; // NO QUITAR = null
  static String _error = '';
  static dynamic _metadata;
  static bool? _submiting;
  static bool? _forceRefresh;
  static dynamic _data;
  static dynamic _scaffoldKey;

  set scaffoldKey(dynamic value) {
    _scaffoldKey = value;
  }

  get scaffoldKey => _scaffoldKey;

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    queue.notify(this.refID);
  }

  bool get submiting {
    return _submiting ?? false;
  }

  bool get refreshing {
    return _forceRefresh ?? false;
  }

  dynamic get metadata {
    return _metadata;
  }

  set metadata(dynamic value) {
    _metadata = value;
    queue.notify(this.refID);
  }

  dynamic get data {
    return _data;
  }

  set data(dynamic value) {
    _data = value;
    queue.notify(this.refID);
  }

  List<Widget> get bodyWidgets {
    return _bodyWidgets;
  }

  submit() async {
    _submiting = true;
    queue.notify(this.refID);
  }

  set forceRefresh(dynamic value) {
    _forceRefresh = value;
  }

  pageRefresh() async {
    _forceRefresh = true;
    queue.notify(this.refID);
  }

  offSubmit() {
    _submiting = false;
  }

  _resetProv() {
    if (DALPaymentQueueProv.exists(this.refID)) {
      DALPaymentQueueProv.removeByRefID(this.refID);
    }

    DALPaymentQueueProv.add(this);
  }

  set bodyWidgets(List<Widget>? value) {
    _bodyWidgets = (value) ?? [];

    _resetProv();
    queue.notify(this.refID);
  }

  List<dynamic>? getDataTable(String refID) {
    for (final Widget _wi in _bodyWidgets) {
      if (_wi.runtimeType.toString() == 'DADataTablePro') {
        DADataTablePro dt = _wi as DADataTablePro;
        if (dt.elID == refID) {
          return dt.data;
        }
      }
    }
    return null;
  }

  dynamic getFormData() async {
    try {
      Map<String, dynamic> res = {
        "dataTables": [],
      };

      for (final Widget _wi in _bodyWidgets) {
        switch (_wi.runtimeType.toString()) {
          case 'DADataTablePro':
            dynamic val = DAController.getWidgetValue(_wi);
            if (val != null) {
              res["dataTables"].add(val);
            }
            break;
          case 'DADropdown':
          case 'DAInput':
          case 'DADatePicker':
            dynamic val = DAController.getWidgetValue(_wi);
            if (val != null) {
              res[val.keys.first] = val[val.keys.first];
            }
            break;
          case 'DARadioList':
          case 'DASignature':
          case 'DaImageUpload':
            print('TODO ' + _wi.runtimeType.toString());
            break;
          default:
            break;
        }
      }

      if (res['dataTables'].length == 0) {
        res.remove('dataTables');
      }

      return res;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }
}
