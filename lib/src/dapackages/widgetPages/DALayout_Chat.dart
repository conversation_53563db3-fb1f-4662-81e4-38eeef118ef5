import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../main_DAPackages.dart';

class ReceivedMessage extends StatelessWidget {
  final String message;

  const ReceivedMessage({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return SizedBox(
          width: constraints.maxWidth * 0.75,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Flexible(
                flex: 3,
                child: GestureDetector(
                  onLongPress: () {
                    Clipboard.setData(ClipboardData(text: message));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Mensaje copiado al portapapeles'),
                        duration: Duration(seconds: 1),
                      ),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      message,
                      style: TextStyle(fontSize: 16),
                      softWrap: true,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ),
              ),
              Expanded(flex: 1, child: Container()),
            ],
          ),
        );
      },
    );
  }
}

class SentMessage extends StatelessWidget {
  final String message;

  const SentMessage({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return SizedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(flex: 1, child: Container()),
              Flexible(
                flex: 3,
                child: GestureDetector(
                  onLongPress: () {
                    Clipboard.setData(ClipboardData(text: message));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Mensaje copiado al portapapeles'),
                        duration: Duration(seconds: 1),
                      ),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      message,
                      style: TextStyle(fontSize: 16),
                      softWrap: true,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class SpeechToTextWidget extends StatefulWidget {
  final TextEditingController textController;
  final Function onSendChat;

  const SpeechToTextWidget({
    required this.textController,
    required this.onSendChat,
  });

  @override
  _SpeechToTextWidgetState createState() => _SpeechToTextWidgetState();
}

class _SpeechToTextWidgetState extends State<SpeechToTextWidget> {
  final SpeechToText _speechToText = SpeechToText();
  bool _speechEnabled = false;
  String _lastWords = "";

  SpeechListenOptions _listenOptions = SpeechListenOptions(
      cancelOnError: false,
      partialResults: false,
      listenMode: ListenMode.confirmation);

  void _initSpeech() async {
    _speechEnabled = await _speechToText.initialize();
  }

  void _startListening() async {
    await _speechToText.listen(
      onResult: _onSpeechResult,
      listenFor: const Duration(seconds: 30),
      localeId: "es_MX",
      listenOptions: _listenOptions,
    );
    setState(() {});
  }

  // ignore: unused_element
  void _stopListening() async {
    await _speechToText.stop();
    setState(() {});
  }

  void _onSpeechResult(SpeechRecognitionResult result) {
    setState(() {
      _lastWords = "$_lastWords${result.recognizedWords} ";
      widget.textController.text = _lastWords;
    });
  }

  @override
  void initState() {
    _initSpeech();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: widget.textController,
              decoration: InputDecoration(
                hintText: '',
                border: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.grey,
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                contentPadding: EdgeInsets.all(8.0),
              ),
            ),
          ),
          IconButton(
            icon: widget.textController.text.trim().isNotEmpty
                ? Icon(Icons.send)
                : Icon(Icons.mic),
            onPressed: () async {
              if (widget.textController.text.trim().isNotEmpty) {
                setState(() {
                  widget.onSendChat();
                });
              } else if (!_speechEnabled) {
                _initSpeech();
              }
              _startListening();
            },
          ),
        ],
      ),
    );
  }
}

class ScrollableTable extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  ScrollableTable({required this.data});

  Future<void> exportToExcel(BuildContext context) async {
    String csvData = "";

    if (data.isNotEmpty) {
      // Crear la fila de encabezados
      csvData += data[0].keys.join(",") + "\n";

      // Crear las filas de datos
      for (var row in data) {
        csvData += row.values.map((e) => e.toString()).join(",") + "\n";
      }
    }

    // Obtener la ruta temporal para guardar el archivo CSV
    final directory = await getTemporaryDirectory();
    final filePath = '${directory.path}/data.csv';
    final file = File(filePath);

    // Escribir los datos en el archivo CSV
    await file.writeAsString(csvData);

    // Compartir el archivo CSV
    await Share.shareXFiles([XFile(filePath)], text: 'Datos de la tabla');
  }

  void _showTableDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Container(
            width: double.maxFinite,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                child: DataTable(
                  columns: data.isEmpty
                      ? []
                      : data[0]
                          .keys
                          .map((key) => DataColumn(label: Text(key.toString())))
                          .toList(),
                  rows: data.map((row) {
                    return DataRow(
                      cells: row.keys
                          .map((key) => DataCell(Text(row[key].toString())))
                          .toList(),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ElevatedButton(
              onPressed: () => exportToExcel(context),
              child: Icon(
                Icons.share,
                color: Colors.black,
              ),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
            ),
          ],
        ),
        GestureDetector(
          onDoubleTap: () => _showTableDialog(context),
          child: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              return Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Container(
                      height: MediaQuery.of(context).size.height * 0.5,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Color.fromRGBO(136, 234, 99, 1),
                          width: 2.0,
                        ),
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey..withValues(alpha: 0.5),
                            spreadRadius: 5,
                            blurRadius: 7,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: SingleChildScrollView(
                          child: DataTable(
                            headingRowColor: WidgetStateProperty.all(
                                Color.fromRGBO(102, 228, 180, 1)),
                            columns: data.isEmpty
                                ? []
                                : data[0]
                                    .keys
                                    .map((key) =>
                                        DataColumn(label: Text(key.toString())))
                                    .toList(),
                            rows: data.map((row) {
                              return DataRow(
                                cells: row.keys
                                    .map((key) =>
                                        DataCell(Text(row[key].toString())))
                                    .toList(),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Container(),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}

class MyIconButton extends StatefulWidget {
  final TextEditingController messageCtrl;
  final Function onMicTap;
  final Function onTextChanged;
  final Function onSendChat;

  MyIconButton(
      {required this.messageCtrl,
      required this.onMicTap,
      required this.onTextChanged,
      required this.onSendChat});

  @override
  _MyIconButtonState createState() => _MyIconButtonState();
}

class _MyIconButtonState extends State<MyIconButton> {
  bool _isMicPressed = false;

  @override
  void initState() {
    super.initState();
    widget.messageCtrl.addListener(_handleTextChanged);
  }

  @override
  void dispose() {
    widget.messageCtrl.removeListener(_handleTextChanged);
    super.dispose();
  }

  void _handleTextChanged() {
    widget.onTextChanged();
    if (widget.messageCtrl.text.trim() != '') {
      setState(() {
        _isMicPressed = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: widget.messageCtrl.text.trim() != ''
          ? Icon(Icons.send)
          : Icon(Icons.mic, color: _isMicPressed ? Colors.red : null),
      onPressed: () {
        if (widget.messageCtrl.text.trim() != '') {
          setState(() {
            _isMicPressed = false;
          });
          widget.onSendChat();
        } else {
          setState(() {
            _isMicPressed = !_isMicPressed;
          });
          widget.onMicTap();
        }
      },
    );
  }
}

// ignore: must_be_immutable
class DALayoutChatPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutChatPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutChatPage createState() => _DALayoutChatPage();
}

class _DALayoutChatPage extends State<DALayoutChatPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
  GlobalKey<RefreshIndicatorState> _fresh =
      new GlobalKey<RefreshIndicatorState>();
  DALayoutChatFilters? _modelLayout;

  List<Widget> _bodyWidgets = [];

  bool _isLoading = true;
  bool _tapOther = false;
  bool _focusTime = false;
  bool _leave = false;
  bool _validID = false;

  Timer? _timer;
  int _lastMessageCount = 0;

  @override
  void initState() {
    super.initState();

    DAController.refAvatar(context, () {
      setState(() {});
    });
    setState(() {
      _isLoading = true;
    });

    // Iniciar el temporizador
    _timer = Timer.periodic(
        Duration(milliseconds: 100),
        (Timer t) => setState(() {
              _checkForNewMessages();
            }));
  }

  @override
  void dispose() {
    // Cancelar el temporizador al salir de la pantalla
    _timer?.cancel();
    super.dispose();
  }

  void _checkForNewMessages() {
    // Comprobar si la longitud de los mensajes ha cambiado
    if (_modelLayout != null && _modelLayout!.chatBody != null) {
      int currentMessageCount = _modelLayout!.chatBody!.length;
      if (_lastMessageCount != currentMessageCount) {
        // Actualizar la posición del scroll para mostrar el mensaje más reciente
        if (_modelLayout!.scrollController!.hasClients) {
          final maxScroll =
              _modelLayout!.scrollController!.position.maxScrollExtent + 500;
          _modelLayout!.scrollController!.jumpTo(
            maxScroll,
          );
          print('Scrolling to bottom');
        }
        _lastMessageCount =
            currentMessageCount; // Actualizar el recuento de mensajes

        print('New message count: $currentMessageCount');
      }
    }
  }

  List<Widget> parseMessages(List<dynamic> messages) {
    return messages.map((message) {
      if (message['Type'] == 'Received') {
        return ReceivedMessage(message: message['Text'] ?? '');
      } else if (message['Type'] == 'Sent') {
        return SentMessage(message: message['Text'] ?? '');
      } else {
        throw Exception('Unknown message type: ${message['Type']}');
      }
    }).toList();
  }

  void sendMessage(String message) {
    _modelLayout!.chatBody?.add(SentMessage(message: message));
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    try {
      if (_modelLayout == null) {
        _modelLayout =
            ModalRoute.of(context)!.settings.arguments as DALayoutChatFilters;
        _bodyWidgets = [];
        _isLoading = true;
      }

      // Validamos provider
      Provider.of<DALChatQueueProv>(context);
      final DALayoutChatProvider? _layoutProv =
          DALChatQueueProv.getByRefID(_modelLayout!.refID);
      _validID = (_modelLayout!.refID == DALChatQueueProv.refID);

      if (_validID) {
        if (_layoutProv!.error != '') {
          throw _layoutProv.error;
        }

        if (!_layoutProv.isLoading) {
          _isLoading = _layoutProv.isLoading;
          _bodyWidgets = _layoutProv.bodyWidgets;
          setState(() {});
        }

        if (_layoutProv.refreshing && _modelLayout!.onRefresh != null) {
          _fresh.currentState!.show();
          _layoutProv.forceRefresh = false;
        }
      }

      Random random = new Random();
      // ignore: unused_local_variable
      String _hero = 'effectrnf-' + (random.nextInt(100)).toString();

      // ignore: unused_element
      layoutForm() {
        try {
          Widget _daForm = DAPageForm(
            title: _modelLayout!.title,
            prefix: _modelLayout!.prefix,
            formKey: _formKey,
            formBody: _bodyWidgets,
            hasBackButton: _modelLayout!.hasBackButton,
            popUpMenu: _modelLayout!.popUpMenu,
          );

          Widget mainPage = (_modelLayout!.onRefresh == null)
              ? _daForm
              : Center(
                  child: RefreshIndicator(
                    key: _fresh,
                    backgroundColor: Colors.white,
                    onRefresh: () async => _modelLayout!.onRefresh!(),
                    child: _daForm,
                  ),
                );

          Widget focusPage = Focus(
            onFocusChange: (focus) {
              FocusNode _focus = FocusScope.of(context);

              if (_leave) {
                _leave = false;
                _tapOther = false;
                return;
              }

              if (_tapOther && _focusTime) {
                _focusTime = false;
                _leave = true;
                FocusScope.of(context).unfocus();
                FocusManager.instance.primaryFocus?.unfocus();
                return;
              }

              if (focus && !_focus.hasPrimaryFocus && _focus.hasFocus) {
                _tapOther = false;
              }

              if (!focus && !_focus.hasPrimaryFocus && !_focus.hasFocus) {
                _tapOther = true;
              }

              if (_tapOther) {
                _focusTime = true;
              }
            },
            child: mainPage,
          );

          Widget _builder = Builder(
            builder: (BuildContext context) {
              return focusPage;
            },
          );

          setState(() {});

          return _builder;
        } catch (e) {
          DAToast(context, e.toString());
          print(e.toString());
        }
      }

      int _el = 0;
      bool _reValidateForm() {
        bool hasError = false;
        for (final dynamic field in _bodyWidgets) {
          _el += 1;
          try {
            if (field.runtimeType.toString() == 'DAInput' ||
                field.runtimeType.toString() == 'DADropdown') {
              if (!field.isValid) {
                hasError = true;
                return !hasError;
              }
            }
          } catch (e) {
            hasError = hasError;
          }
        }
        return !hasError;
      }

      floatingBtnTap() async {
        try {
          _layoutProv!.isLoading = true;
          _isLoading = true;
          setState(() {});
          final form = _formKey.currentState;

          _hasLoading({String? msg}) {
            if (msg != null) {
              DAToast(context, msg);
            }
            _isLoading = false;
            setState(() {});
          }

          if (form!.validate()) {
            if (_reValidateForm()) {
              form.save();

              dynamic _formData = await _layoutProv.getFormData();
              await _modelLayout!.formSubmit!(_formData);

              _hasLoading();
            } else {
              _hasLoading(
                  msg:
                      'Datos incorrectos, favor de validar campo ${_el.toString()}');
            }
          } else {
            _hasLoading(msg: 'Datos incorrectos, favor de validar');
          }
        } catch (e) {
          _isLoading = false;
          DAToast(context, e.toString());
          setState(() {});
        }
      }

      validateSubmiting() async {
        if (_validID && _layoutProv!.submiting) {
          await _layoutProv.offSubmit();
          await floatingBtnTap();
        }
      }

      validateSubmiting();

      // Cargamos cuerpo de chatBody desde chat
      if (_modelLayout!.chatBody!.length == 0) {
        _modelLayout!.chatBody = parseMessages(_modelLayout!.chat!);
        setState(() {});
      }

      _modelLayout!.messageCtrl!.addListener(() {
        setState(() {});
      });

      Widget _builder = LoadingOverlay(
        isLoading: _isLoading,
        child: Scaffold(
          key: _scaffoldKey,
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          appBar: AppBar(
            automaticallyImplyLeading: true,
            title: _modelLayout!.titleChatPage,
            actions: [
              IconButton(
                icon: Icon(Icons.person),
                onPressed: () {
                  _scaffoldKey.currentState!.openEndDrawer();
                },
              ),
            ],
          ),
          body: Column(
            children: [
              Expanded(
                child: ListView.builder(
                  controller: _modelLayout!
                      .scrollController, // Add this line to control the scroll position
                  itemCount: _modelLayout!.chatBody?.length,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.all(8.0),
                      child: _modelLayout!.chatBody?[index],
                    );
                  },
                ),
              ),
              Container(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade800
                    : Colors.white,
                child: Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: TextField(
                          controller: _modelLayout!.messageCtrl,
                          focusNode: _modelLayout!.focusNode,
                          decoration: InputDecoration(
                            hintText: '',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? Colors.white
                                    : Colors.grey,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            contentPadding: EdgeInsets.all(8.0),
                          ),
                          onChanged: (text) {
                            setState(() {});
                          },
                          onEditingComplete: () {
                            setState(() {});
                          },
                          onTap: () {
                            setState(() {});
                          },
                          onTapOutside: (event) {
                            setState(() {
                              _modelLayout!.focusNode?.unfocus();
                            });
                          },
                        ),
                      ),
                    ),
                    MyIconButton(
                      messageCtrl: _modelLayout!.messageCtrl!,
                      onMicTap: _modelLayout!.onMicTap,
                      onTextChanged: () {
                        setState(() {});
                      },
                      onSendChat: () {
                        setState(() {
                          _modelLayout!.onSendChat();
                          // Desplazar hasta el final de la lista
                          if (_modelLayout!.scrollController!.hasClients) {
                            final maxScroll = _modelLayout!.scrollController!
                                    .position.maxScrollExtent *
                                1.5;
                            _modelLayout!.scrollController!.animateTo(
                              maxScroll, // Nueva posición de desplazamiento
                              duration: Duration(
                                  milliseconds:
                                      500), // Duración de la animación
                              curve: Curves.easeOut, // Tipo de animación
                            );
                          }
                          _isLoading = false;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade700
              : Color.fromRGBO(244, 243, 243, 1),
        ),
      );

      return _builder;
    } catch (e) {
      DAToast(context, e.toString());
      return Container();
    }
  }
}

class DALChatQueueProv with ChangeNotifier {
  static DALChatQueueProv _instancia = new DALChatQueueProv._internal();
  DALChatQueueProv._internal();

  factory DALChatQueueProv() {
    return _instancia;
  }

  static String _refID = '';

  static get refID {
    return _refID;
  }

  static List<DALayoutChatProvider> _mainQueue = [];

  static bool exists(String refID) {
    bool exists = false;

    for (final DALayoutChatProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return true;
      }
    }

    return exists;
  }

  static add(DALayoutChatProvider prov) {
    if (exists(prov.refID)) {
      removeByRefID(prov.refID);
    }
    _mainQueue.add(prov);
  }

  static DALayoutChatProvider getByRefID(String refID) {
    for (final DALayoutChatProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return _currProv;
      }
    }

    DALayoutChatProvider _newProv = new DALayoutChatProvider(refID: refID);
    _mainQueue.add(_newProv);
    return _newProv;
  }

  static removeByRefID(String refID) {
    if (exists(refID)) {
      _mainQueue.removeWhere((item) => item.refID == refID);
    }
  }

  notify(String refID) {
    _refID = refID;
    notifyListeners();
  }
}

class DALayoutChatProvider {
  final String refID;

  @override
  DALayoutChatProvider({
    required this.refID,
  });

  DALChatQueueProv queue = DALChatQueueProv();
  static List<Widget> _bodyWidgets = [];

  // ignore: avoid_init_to_null
  static bool? _isLoading = null; // NO QUITAR = null
  static String _error = '';
  static dynamic _metadata;
  static bool? _submiting;
  static bool? _forceRefresh;

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    queue.notify(this.refID);
  }

  bool get submiting {
    return _submiting ?? false;
  }

  bool get refreshing {
    return _forceRefresh ?? false;
  }

  dynamic get metadata {
    return _metadata;
  }

  set metadata(dynamic value) {
    _metadata = value;
    queue.notify(this.refID);
  }

  List<Widget> get bodyWidgets {
    return _bodyWidgets;
  }

  submit() async {
    _submiting = true;
    queue.notify(this.refID);
  }

  set forceRefresh(dynamic value) {
    _forceRefresh = value;
  }

  pageRefresh() async {
    _forceRefresh = true;
    queue.notify(this.refID);
  }

  offSubmit() {
    _submiting = false;
  }

  _resetProv() {
    if (DALChatQueueProv.exists(this.refID)) {
      DALChatQueueProv.removeByRefID(this.refID);
    }

    DALChatQueueProv.add(this);
  }

  set bodyWidgets(List<Widget>? value) {
    _bodyWidgets = (value) ?? [];

    _resetProv();
    queue.notify(this.refID);
  }

  List<dynamic>? getDataTable(String refID) {
    for (final Widget _wi in _bodyWidgets) {
      if (_wi.runtimeType.toString() == 'DADataTablePro') {
        DADataTablePro dt = _wi as DADataTablePro;
        if (dt.elID == refID) {
          return dt.data;
        }
      }
    }
    return null;
  }

  dynamic getFormData() async {
    try {
      Map<String, dynamic> res = {
        "dataTables": [],
      };

      for (final Widget _wi in _bodyWidgets) {
        switch (_wi.runtimeType.toString()) {
          case 'DADataTablePro':
            dynamic val = DAController.getWidgetValue(_wi);
            if (val != null) {
              res["dataTables"].add(val);
            }
            break;
          case 'DADropdown':
          case 'DAInput':
          case 'DADatePicker':
            dynamic val = DAController.getWidgetValue(_wi);
            if (val != null) {
              res[val.keys.first] = val[val.keys.first];
            }
            break;
          case 'DARadioList':
          case 'DASignature':
          case 'DaImageUpload':
            print('TODO ' + _wi.runtimeType.toString());
            break;
          default:
            break;
        }
      }

      if (res['dataTables'].length == 0) {
        res.remove('dataTables');
      }

      return res;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }
}
