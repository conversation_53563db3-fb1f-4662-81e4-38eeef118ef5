import 'package:flutter/material.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALayoutListCardPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutListCardPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutListCardPage createState() => _DALayoutListCardPage();
}

class _DALayoutListCardPage extends State<DALayoutListCardPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  GlobalKey<RefreshIndicatorState> _fresh =
      new GlobalKey<RefreshIndicatorState>();
  TextEditingController _inputFieldFinderController =
      new TextEditingController();

  DALayoutListModel? _modelLayout;
  List<dynamic> _dataListWidget = [];

  bool _isLoading = true;
  bool _tapOther = false;
  bool _focusTime = false;
  bool _leave = false;
  bool _validID = false;

  @override
  void initState() {
    super.initState();
    DAController.refAvatar(context, () {
      setState(() {});
    });
    setState(() {
      _isLoading = true;
    });
  }

  @override
  void dispose() {
    FocusScope.of(context).dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    try {
      if (_modelLayout == null) {
        _modelLayout =
            ModalRoute.of(context)!.settings.arguments as DALayoutListModel;
        _dataListWidget = [];
        _isLoading = true;
      }

      // Validamos provider
      Provider.of<DALayoutListCardQueueProv>(context);
      final DALayoutListCardProvider? _layoutProv =
          DALayoutListCardQueueProv.getByRefID(_modelLayout!.refID);
      _validID = (_modelLayout!.refID == DALayoutListCardQueueProv.refID);

      if (_validID) {
        if (_layoutProv!.error != '') {
          throw _layoutProv.error;
        }

        if (!_layoutProv.isLoading) {
          _isLoading = _layoutProv.isLoading;
          _dataListWidget = _layoutProv.data;
          setState(() {});
        }

        if (_layoutProv.refreshing && _modelLayout!.onRefresh != null) {
          _fresh.currentState!.show();
          _layoutProv.forceRefresh = false;
        }
      }

      layoutForm() {
        try {
          Widget _daForm = DAPageList(
            config: _modelLayout,
            data: _dataListWidget,
            finderController: _inputFieldFinderController,
            hasBackButton: (_modelLayout!.hasBackButton) ?? false,
            hasFinder: (_modelLayout!.hasFinder) ?? true,
            hasTotal: (_modelLayout!.hasTotal) ?? false,
            total: (_modelLayout!.amount) ?? 0,
            useCustomTotal: (_modelLayout!.useCustomTotal) ?? false,
            onFinderTap: () {
              _inputFieldFinderController.text = '';
              setState(() {});
            },
            onFinderChanged: (valor) {
              setState(() {});
            },
            onRefresh: () async {
              await _modelLayout!.dataSource!();
              return;
            },
            onCardTap: _modelLayout!.cardConfig.onCardTap,
          );

          Widget mainPage = (_modelLayout!.onRefresh == null)
              ? _daForm
              : Center(
                  child: RefreshIndicator(
                    key: _fresh,
                    backgroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[900]
                            : Color.fromRGBO(244, 243, 243, 1),
                    onRefresh: () async => _modelLayout!.onRefresh!(),
                    child: _daForm,
                  ),
                );

          Widget focusPage = Focus(
            onFocusChange: (focus) {
              FocusNode _focus = FocusScope.of(context);

              if (_leave) {
                _leave = false;
                _tapOther = false;
                return;
              }

              if (_tapOther && _focusTime) {
                _focusTime = false;
                _leave = true;
                FocusScope.of(context).unfocus();
                FocusManager.instance.primaryFocus?.unfocus();
                return;
              }

              if (focus && !_focus.hasPrimaryFocus && _focus.hasFocus) {
                _tapOther = false;
              }

              if (!focus && !_focus.hasPrimaryFocus && !_focus.hasFocus) {
                _tapOther = true;
              }

              if (_tapOther) {
                _focusTime = true;
              }
            },
            child: mainPage,
          );

          Widget _builder = Builder(
            builder: (BuildContext context) {
              return focusPage;
            },
          );

          setState(() {});

          return _builder;
        } catch (e) {
          DAToast(context, e.toString());
          print(e.toString());
        }
      }

      Widget _builder = LoadingOverlay(
        isLoading: _isLoading,
        child: Scaffold(
          key: _scaffoldKey,
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
          floatingActionButton: _modelLayout!.floatingActionButton,
          body: layoutForm(),
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : Color.fromRGBO(244, 243, 243, 1),
          bottomNavigationBar: _modelLayout!.bottomNavigationBar,
        ),
      );

      return _builder;
    } catch (e) {
      DAToast(context, e.toString());
      return Container();
    }
  }
}

class DALayoutListCardQueueProv with ChangeNotifier {
  static DALayoutListCardQueueProv _instancia =
      new DALayoutListCardQueueProv._internal();
  DALayoutListCardQueueProv._internal();

  factory DALayoutListCardQueueProv() {
    return _instancia;
  }

  static String _refID = '';

  static get refID {
    return _refID;
  }

  static List<DALayoutListCardProvider> _mainQueue = [];

  static bool exists(String refID) {
    bool exists = false;

    for (final DALayoutListCardProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return true;
      }
    }

    return exists;
  }

  static add(DALayoutListCardProvider prov) {
    if (exists(prov.refID)) {
      removeByRefID(prov.refID);
    }
    _mainQueue.add(prov);
  }

  static DALayoutListCardProvider getByRefID(String refID) {
    for (final DALayoutListCardProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return _currProv;
      }
    }

    DALayoutListCardProvider _newProv =
        new DALayoutListCardProvider(refID: refID);
    _mainQueue.add(_newProv);
    return _newProv;
  }

  static removeByRefID(String refID) {
    if (exists(refID)) {
      _mainQueue.removeWhere((item) => item.refID == refID);
    }
  }

  notify(String refID) {
    _refID = refID;
    notifyListeners();
  }
}

class DALayoutListCardProvider {
  final String refID;

  @override
  DALayoutListCardProvider({
    required this.refID,
  });

  DALayoutListCardQueueProv queue = DALayoutListCardQueueProv();
  static List<Widget> _bodyWidgets = [];

  // ignore: avoid_init_to_null
  static bool? _isLoading = null; // NO QUITAR = null
  static String _error = '';
  static dynamic _data;
  static bool? _submiting;
  static bool? _forceRefresh = false;

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    queue.notify(this.refID);
  }

  bool get submiting {
    return _submiting ?? false;
  }

  bool get refreshing {
    return _forceRefresh ?? false;
  }

  dynamic get data {
    return _data;
  }

  set data(dynamic value) {
    _data = value;
    queue.notify(this.refID);
  }

  List<Widget> get bodyWidgets {
    return _bodyWidgets;
  }

  set forceRefresh(dynamic value) {
    _forceRefresh = value;
  }

  pageRefresh() async {
    _forceRefresh = true;
    queue.notify(this.refID);
  }

  _resetProv() {
    if (DALayoutListCardQueueProv.exists(this.refID)) {
      DALayoutListCardQueueProv.removeByRefID(this.refID);
    }

    DALayoutListCardQueueProv.add(this);
  }

  set bodyWidgets(List<Widget>? value) {
    _bodyWidgets = (value) ?? [];

    _resetProv();
    queue.notify(this.refID);
  }

  notify() async {
    queue.notify(this.refID);
  }
}
