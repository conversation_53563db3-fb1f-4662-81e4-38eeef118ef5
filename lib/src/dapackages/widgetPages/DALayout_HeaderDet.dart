import 'package:flutter/material.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALayoutHdrDetPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutHdrDetPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutHdrDetPage createState() => _DALayoutHdrDetPage();
}

class _DALayoutHdrDetPage extends State<DALayoutHdrDetPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
  DALayoutDetModel _modelLayoutDetPage = new DALayoutDetModel(
    refID: "",
    tableConfig: DALayoutDetModelTableMainModel(scopeRowID: '', tableDef: []),
    header: DALayoutDetModelHeaderMainModel(
      headerTitle: DALayoutDetModelHeaderTitleModel(
        titlePage: "Cargando...",
      ),
      headerDetail: [],
    ),
  );

  bool _isLoading = true;
  List<dynamic> _dataHdrDet = [];

  @override
  void initState() {
    super.initState();
    DAController.refAvatar(context, () {
      setState(() {});
    });
    setState(() {
      _isLoading = true;
    });

    //DALayoutFormWidgetsProvider _layoutProvL = DALayoutFormWidgetsProvider(_layoutProv.refID);
    //_layoutProvL.isLoading = (_layoutProvL.isLoading) ?? true;
  }

  @override
  void dispose() {
    FocusScope.of(context).dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    try {
      if (_modelLayoutDetPage.header.headerTitle.titlePage == 'Cargando...') {
        _modelLayoutDetPage =
            ModalRoute.of(context)!.settings.arguments as DALayoutDetModel;
        _dataHdrDet = [];
        _isLoading = true;
      }

      final _layoutProv = Provider.of<DALayoutHdrDetProvider>(context);

      if (_layoutProv.error != '') {
        if (_modelLayoutDetPage.refID != "") {
          if (_modelLayoutDetPage.refID == _layoutProv.refID) {
            throw _layoutProv.error;
          }
        } else {
          throw _layoutProv.error;
        }
      }

      if (!_layoutProv.isLoading) {
        if (_modelLayoutDetPage.refID == _layoutProv.refID) {
          _isLoading = _layoutProv.isLoading;
          _dataHdrDet = _layoutProv.dataHdrDet;
          setState(() {});
        }
      }

      getDataDet() {
        try {
          if (_dataHdrDet[1].length == 0) {
            return DANoData(tipo: _modelLayoutDetPage.tableConfig.noDataMsg);
          }

          Widget _dataTable = DADataTablePro(
            refID: _modelLayoutDetPage.refID,
            scopeRowID: _modelLayoutDetPage.tableConfig.scopeRowID,
            columnsDef: _modelLayoutDetPage.tableConfig.tableDef,
            dataSource: _dataHdrDet[1],
            noDataMsg: _modelLayoutDetPage.tableConfig.noDataMsg,
            hasFinder: true,
            tableActions: _modelLayoutDetPage.tableConfig.tableActions,
            listLongPress: _modelLayoutDetPage.tableConfig.listLongPress,
          );

          return _dataTable;
        } catch (e) {
          return DANoData(tipo: _modelLayoutDetPage.tableConfig.noDataMsg);
        }
      }

      List<Widget> _bodyPage() {
        try {
          if (!_isLoading && _modelLayoutDetPage.refID == _layoutProv.refID) {
            setState(() {});
          }

          // Limpiamos Forma
          List<Widget> _formBody = [];
          _formBody.clear();

          // Header
          List<dynamic> _dataHeader = _dataHdrDet[0];
          if ((_dataHeader.length > 0)) {
            Widget _headerW = DALabelList(
              data: _dataHeader[0],
              config: _modelLayoutDetPage.header.headerDetail,
            );
            _formBody.add(_headerW);
          }

          // Divider
          _formBody.add(DADivider());

          // Data Table
          Widget _dataTable = getDataDet();
          _formBody.add(_dataTable);

          return _formBody;
        } catch (e) {
          DAToast(context, e.toString());
          return [DANoData(tipo: _modelLayoutDetPage.tableConfig.noDataMsg)];
        }
      }

      layoutForm() {
        if (_dataHdrDet.length == 0 &&
            _modelLayoutDetPage.refID == _layoutProv.refID) {
          if (!_isLoading) {
            DAToast(context, "Error al obtener datos.");
            return DANoData(tipo: _modelLayoutDetPage.tableConfig.noDataMsg);
          }
          return DANoData(
            tipo: 'Cargando...',
            replaceAll: true,
          );
        }

        try {
          _fixData(String key) {
            List<dynamic> _dataHeader = _dataHdrDet[0];

            if (_dataHeader.length == 0) {
              return key.replaceAll('{{', '').replaceAll('}}', '');
            }

            if (key.indexOf('{{') >= 0 && key.indexOf('}}') >= 0) {
              key = _dataHeader[0]
                  [key.replaceAll('{{', '').replaceAll('}}', '').toString()];
            }
            return key;
          }

          DALayoutDetModelHeaderTitleModel _hd =
              _modelLayoutDetPage.header.headerTitle;
          String _titPage = (_fixData(_hd.titlePage) == '')
              ? _fixData((_hd.titlePageNull) ?? '')
              : _fixData(_hd.titlePage);

          Widget mainPage = DAPageForm(
            prefix: _fixData((_hd.subTitlePage) ?? ''),
            title: _titPage,
            formKey: _formKey,
            formBody: _bodyPage(),
            hasBackButton: true,
            onRefresh: () async => _layoutProv.refreshData(),
          );

          return mainPage;
        } catch (e) {
          print(e.toString());
        }
      }

      Widget _builder = LoadingOverlay(
        isLoading: (_isLoading),
        child: Scaffold(
          key: _scaffoldKey,
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
          floatingActionButton: _modelLayoutDetPage.floatingActionButton,
          bottomNavigationBar: _modelLayoutDetPage.bottomNavigationBar,
          backgroundColor: Color.fromRGBO(244, 243, 243, 1),
          body: layoutForm(),
        ),
      );

      return _builder;
    } catch (e) {
      DAToast(context, e.toString());
      return Container();
    }
  }

  Future<void> loadSesion() async {
    DAToast(context, 'refresh');
  }
}

// ignore: must_be_immutable
class DALayoutHdrDetProvider with ChangeNotifier {
  static DALayoutHdrDetProvider _instancia =
      new DALayoutHdrDetProvider._internal();
  DALayoutHdrDetProvider._internal();
  static List<dynamic> _dataHdrDet = [];
  static DARequestModel? _req;

  factory DALayoutHdrDetProvider() {
    return _instancia;
  }

  // ignore: avoid_init_to_null
  static bool? _isLoading = null; // NO QUITAR = null
  static String _error = '';
  static String _refID = '';

  // ignore: unnecessary_getters_setters
  String get refID {
    return _refID;
  }

  // ignore: unnecessary_getters_setters
  set refID(String value) {
    _refID = value;
  }

  dynamic get isLoading {
    return (_isLoading) ?? true;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    notifyListeners();
  }

  List<dynamic> get dataHdrDet {
    return _dataHdrDet;
  }

  void notify(String refID) async {
    _refID = refID;
    notifyListeners();
  }

  set dataHdrDet(List<dynamic>? value) {
    _dataHdrDet = (value) ?? [];
    notifyListeners();
  }

  set refRequest(DARequestModel req) {
    _req = req;
  }

  refreshData() async {
    List<dynamic> dataPage = await DAMainLoadingProvider.execAPI(
      _req!.uriReq,
      _req!.bodyReq,
    );

    _dataHdrDet = dataPage;
    notifyListeners();
  }
}
