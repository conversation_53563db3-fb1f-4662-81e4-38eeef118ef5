import 'package:flutter/material.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DAMenuCardsPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DAMenuCardsPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DAMenuCardsPage createState() => _DAMenuCardsPage();
}

class _DAMenuCardsPage extends State<DAMenuCardsPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    DAController.refAvatar(context, () {
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    DAMenuCardsModel _modelLayoutMenuCardsPage =
        ModalRoute.of(context)!.settings.arguments as DAMenuCardsModel;
    final _layoutProv = Provider.of<DAMenuCardsPageProvider>(context);

    return LoadingOverlay(
      isLoading: (_layoutProv.isLoading ?? _isLoading),
      child: Scaffold(
        key: _scaffoldKey,
        endDrawer: DAMainMenu(
          defLogoAvatar: widget.defLogoAvatar,
          avatarTap: () async {
            await DAController.saveAvatar(context, _scaffoldKey, () {
              setState(() {
                _isLoading = false;
              });
            });
          },
          tiles: widget.appMenu,
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
        floatingActionButton: _modelLayoutMenuCardsPage.floatingActionButton,
        body: (mainLayout(_modelLayoutMenuCardsPage)) ?? Container(),
        backgroundColor: Color.fromRGBO(244, 243, 243, 1),
        bottomNavigationBar: _modelLayoutMenuCardsPage.bottomNavigationBar,
      ),
    );
  }

  mainLayout(DAMenuCardsModel _modelLayoutMenuCardsPage) {
    try {
      Widget _border = Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(30)),
        ),
        padding: EdgeInsets.only(
          bottom: 25.0,
        ),
      );

      List<Widget> _resList = [
        _border,
        SizedBox(height: 10.0),
      ];

      if (_modelLayoutMenuCardsPage.menuButtons.length == 0) {
        _resList.add(DANoData(
          tipo: (_modelLayoutMenuCardsPage.noData) ?? 'tarjetas',
        ));
      }

      _modelLayoutMenuCardsPage.menuButtons.forEach((element) {
        _resList.add(element);
      });

      Widget listaBody = SliverList(
        delegate: SliverChildListDelegate(
          [
            new Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: _resList,
            ),
          ],
        ),
      );

      Widget list = CustomScrollView(
        slivers: <Widget>[
          DATitleBig(
            prefix: (_modelLayoutMenuCardsPage.prefix) ?? '',
            title: (_modelLayoutMenuCardsPage.title) ?? 'Cargando...',
          ),
          listaBody,
        ],
      );

      return Stack(
        children: [
          list,
        ],
      );
    } catch (e) {
      DAToast(context, e.toString());
    }
  }
}

class DAMenuCardsPageProvider with ChangeNotifier {
  static DAMenuCardsPageProvider _instancia =
      new DAMenuCardsPageProvider._internal();
  DAMenuCardsPageProvider._internal();

  static bool? _isLoading;

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
    notifyListeners();
  }

  factory DAMenuCardsPageProvider() {
    return _instancia;
  }

  notify() {
    notifyListeners();
  }
}
