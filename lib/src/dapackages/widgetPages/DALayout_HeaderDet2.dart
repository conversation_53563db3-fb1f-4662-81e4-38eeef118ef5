import 'package:flutter/material.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALayoutHdrDetPage2 extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutHdrDetPage2({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutHdrDetPage2 createState() => _DALayoutHdrDetPage2();
}

class _DALayoutHdrDetPage2 extends State<DALayoutHdrDetPage2> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _inputFieldFinderController =
      new TextEditingController();
  DALayoutHdrDetProvider2 _layoutProv = DALayoutHdrDetProvider2();
  List<dynamic> _dataHeader = [];
  bool _isLoading = true;
  bool _ignoreLoading = false;

  // Data Tables
  int _sortColumnIndex = 0;
  bool _sortAsc = true;
  String? _selected;

  DALayoutDetModel _modelLayoutDetPage = new DALayoutDetModel(
    refID: "",
    tableConfig: DALayoutDetModelTableMainModel(scopeRowID: '', tableDef: []),
    header: DALayoutDetModelHeaderMainModel(
      headerTitle: DALayoutDetModelHeaderTitleModel(
        titlePage: "Cargando...",
      ),
      headerDetail: [],
    ),
  );

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    loadSesion();
    DAController.refAvatar(context, () {
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    try {
      final _layoutProv = Provider.of<DALayoutHdrDetProvider2>(context);
      if (_layoutProv.error != '') {
        throw _layoutProv.error;
      } else if (_layoutProv.forceRefresh) {
        if (_layoutProv.refID == _modelLayoutDetPage.refID) {
          _layoutProv.forceRefresh = false;
          loadSesion();
        }
      } else {
        //if (_modelLayoutDetPage.title == 'Cargando...') {
        if (!_ignoreLoading) {
          _modelLayoutDetPage =
              ModalRoute.of(context)!.settings.arguments as DALayoutDetModel;
        }
        if (_isLoading && !_ignoreLoading) {
          loadSesion();
        }
        //}
      }

      Widget _builder = LoadingOverlay(
        isLoading: (_isLoading),
        child: Scaffold(
          key: _scaffoldKey,
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
          floatingActionButton: _modelLayoutDetPage.floatingActionButton,
          body: layoutForm(),
          backgroundColor: Color.fromRGBO(244, 243, 243, 1),
          bottomNavigationBar: _modelLayoutDetPage.bottomNavigationBar,
        ),
      );

      _layoutProv.forceRefresh = false;
      return _builder;
    } catch (e) {
      DAToast(context, e.toString());
      return DANoData(
        tipo: 'Sin datos',
        replaceAll: true,
      );
    }
  }

  // Widgets
  _bodyPage() {
    try {
      if (!_isLoading) {
        setState(() {});
      }

      // Limpiamos Forma
      List<Widget> _formBody = [];
      _formBody.clear();

      // Header
      if ((_dataHeader.length > 0)) {
        Widget _headerW = DALabelList(
          data: _dataHeader[0],
          config: _modelLayoutDetPage.header.headerDetail,
        );
        _formBody.add(_headerW);
      }

      // Divider
      _formBody.add(DADivider());

      // Finder
      if (_layoutProv.dataFiltro.length > 0 &&
          (!_isLoading || _ignoreLoading)) {
        Widget _finder = DAPageFilter(
          hintText: _modelLayoutDetPage.finderLabel,
          //icon: Icons.add,
          controller: _inputFieldFinderController,
          //autofocus: true,
          padding: EdgeInsets.symmetric(
            horizontal: 20.0,
          ),
          onTap: () {
            _inputFieldFinderController.text = '';
            DALayoutHdrDetProvider2.setFiltro('');
            setState(() {});
          },
          onChanged: (valor) {
            DALayoutHdrDetProvider2.setFiltro(valor);
            setState(() {});
          },
        );
        _formBody.add(_finder);
      }

      // Data Table
      if (!_isLoading || _ignoreLoading) {
        Widget _dataTable = DADataTableMin(
          columnsDef: _modelLayoutDetPage.tableConfig.tableDef,
          dataSource: _layoutProv.dataFiltro,
          noDataMsg: _modelLayoutDetPage.tableConfig.noDataMsg,
          sortColumnIndex: _sortColumnIndex,
          sortAscending: _sortAsc,
          selected: _selected,
          onSort: (columnIndex, sortAscending, value) {
            _isLoading = true;
            _ignoreLoading = true;
            setState(() {});

            _sortColumnIndex = columnIndex;
            _sortAsc = !_sortAsc;

            _layoutProv.dataFiltro.sort((a, b) => a[value.scope]
                .toString()
                .toLowerCase()
                .compareTo(b[value.scope].toString().toLowerCase()));

            _layoutProv.data = (!_sortAsc)
                ? _layoutProv.dataFiltro.reversed.toList()
                : _layoutProv.dataFiltro;

            DALayoutHdrDetProvider2.setFiltro(_inputFieldFinderController.text);

            _isLoading = false;
            _ignoreLoading = false;
            setState(() {});
          },
          onTap: (value) {
            _selected = json.encode(value);
            setState(() {});
            _modelLayoutDetPage.tableConfig.tableActions!.onTap!(value);
          },
          onDoubleTap: (value) async {
            _ignoreLoading = true;
            _isLoading = true;
            setState(() {});

            _selected = json.encode(value);
            await _modelLayoutDetPage
                .tableConfig.tableActions!.onDoubleTap!(value);

            _ignoreLoading = false;
            _isLoading = false;
            setState(() {});
          },
          onLongPress: (value) async {
            _ignoreLoading = true;
            _isLoading = true;
            setState(() {});

            _selected = json.encode(value);
            await _modelLayoutDetPage
                .tableConfig.tableActions!.onLongPress!(value);

            _ignoreLoading = false;
            _isLoading = false;
            setState(() {});
          },
        );
        _formBody.add(_dataTable);
      }

      return _formBody;
    } catch (e) {
      DAToast(context, e.toString());
      print(e.toString());
    }
  }

  layoutForm() {
    try {
      _fixData(String key) {
        if (_dataHeader.length == 0) {
          return key.replaceAll('{{', '').replaceAll('}}', '');
        }

        if (key.indexOf('{{') >= 0 && key.indexOf('}}') >= 0) {
          key = _dataHeader[0]
              [key.replaceAll('{{', '').replaceAll('}}', '').toString()];
        }
        return key;
      }

      DALayoutDetModelHeaderTitleModel _hd =
          _modelLayoutDetPage.header.headerTitle;
      String _titPage = (_fixData(_hd.titlePage) == '')
          ? _fixData((_hd.titlePageNull) ?? '')
          : _fixData(_hd.titlePage);

      Widget mainPage = DAPageForm(
        prefix: _fixData((_hd.subTitlePage) ?? ''),
        title: _titPage,
        formKey: _formKey,
        //heroTag: 'effect-' + _modelLayoutDetPage.refID,
        formBody: _bodyPage(),
        hasBackButton: true,
        onRefresh: () async => loadSesion(),
      );

      return mainPage;
    } catch (e) {
      DAToast(context, e.toString());
      print(e.toString());
    }
  }

  // Controller Methods
  Future<void> loadSesion() async {
    try {
      if (!_isLoading) {
        _isLoading = true;
        setState(() {});
      }

      // Cargamos Detalle
      if (_modelLayoutDetPage.dataSource != null) {
        await _layoutProv.descargar(
          _modelLayoutDetPage.dataSource!.uriReq,
          _modelLayoutDetPage.dataSource!.bodyReq,
        );

        // Descargamos Movimientos demo
        _dataHeader = _layoutProv.data[0];
        _layoutProv.data = _layoutProv.data[1];
        DALayoutHdrDetProvider2.setFiltro(_inputFieldFinderController.text);

        _isLoading = false;
        setState(() {});
      }
    } catch (e) {
      _isLoading = false;
      String _msg = (e.toString() == 'SESIÓN EXPIRADA')
          ? 'SESIÓN EXPIRADA'
          : 'No es posible mostrar el detalle con la información actual.';
      DAToast(context, _msg);
      setState(() {});
    }
  }
}

class DALayoutHdrDetProvider2 with ChangeNotifier {
  static DALayoutHdrDetProvider2 _instancia =
      DALayoutHdrDetProvider2._internal();
  DALayoutHdrDetProvider2._internal();

  factory DALayoutHdrDetProvider2() {
    return _instancia;
  }

  static dynamic _dataList = [];
  static dynamic _dataListFiltro = [];
  static dynamic _dataParams;
  static String _error = '';
  static String _refID = '';
  static bool _forceRefresh = false;

  dynamic get refID {
    return _refID;
  }

  dynamic get data {
    return _dataList;
  }

  set data(dynamic value) {
    _dataList = value;
    notifyListeners();
  }

  dynamic get dataFiltro {
    return _dataListFiltro;
  }

  set dataFiltro(dynamic value) {
    _dataListFiltro = value;
    notifyListeners();
  }

  dynamic get dataParams {
    return _dataParams;
  }

  set dataParams(dynamic value) {
    _dataParams = value;
    notifyListeners();
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    notifyListeners();
  }

  dynamic get forceRefresh {
    return _forceRefresh;
  }

  set forceRefresh(dynamic value) {
    _forceRefresh = value;
    if (_forceRefresh) {
      notifyListeners();
    }
  }

  static setFiltro(String? query) {
    try {
      _dataListFiltro = _dataList;
      if (query != null) {
        _dataListFiltro = _dataList
            .where((val) => jsonEncode(val)
                .toString()
                .replaceAll("{", "")
                .replaceAll("}", "")
                .replaceAll(":", "")
                .replaceAll(",", "")
                .replaceAll('"', '')
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();
      }
    } catch (e) {
      print('setFiltro: ' + e.toString());
      throw e.toString();
    }
  }

  // ignore: unnecessary_question_mark
  Future descargar(String uri, dynamic? body,
      {String? filter, bool? isDataSet}) async {
    try {
      List<dynamic> data = await DAMainLoadingProvider.execAPI(uri, body);
      _dataList = data;

      if (filter == null || filter == '') {
        _dataListFiltro = _dataList;
      } else {
        setFiltro(filter);
      }

      //notifyListeners();
      //throw ('MovimientoProvider.descargar: Prueba');
    } catch (e) {
      _dataListFiltro = [];
      _dataList = [];
      print('MovimientoProvider.descargar: ' + e.toString());
      throw e.toString();
    }
  }

  void notify(String refID) async {
    // Notificamos Cambios
    _refID = refID;
    notifyListeners();
  }
}
