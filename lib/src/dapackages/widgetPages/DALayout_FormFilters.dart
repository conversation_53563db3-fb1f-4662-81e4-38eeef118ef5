import 'dart:math';
import 'package:flutter/material.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALayoutFormWidgetsPage extends StatefulWidget {
  List<Widget> appMenu;
  final dynamic defLogoAvatar;

  DALayoutFormWidgetsPage({
    required this.appMenu,
    required this.defLogoAvatar,
  });

  @override
  _DALayoutFormWidgetsPage createState() => _DALayoutFormWidgetsPage();
}

class _DALayoutFormWidgetsPage extends State<DALayoutFormWidgetsPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey<FormState> _formKey = new GlobalKey<FormState>();
  GlobalKey<RefreshIndicatorState> _fresh =
      new GlobalKey<RefreshIndicatorState>();
  DALayoutFormFilters? _modelLayout;

  List<Widget> _bodyWidgets = [];

  bool _isLoading = true;
  bool _tapOther = false;
  bool _focusTime = false;
  bool _leave = false;
  bool _validID = false;

  @override
  void initState() {
    super.initState();
    DAController.refAvatar(context, () {
      setState(() {});
    });
    setState(() {
      _isLoading = true;
    });
  }

  @override
  void dispose() {
    //FocusScope.of(context).dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    try {
      if (_modelLayout == null) {
        _modelLayout =
            ModalRoute.of(context)!.settings.arguments as DALayoutFormFilters;
        _bodyWidgets = [];
        _isLoading = true;
      }

      // Validamos provider
      Provider.of<DALFormWgtQueueProv>(context);
      final DALayoutFormWidgetsProvider? _layoutProv =
          DALFormWgtQueueProv.getByRefID(_modelLayout!.refID);
      _validID = (_modelLayout!.refID == DALFormWgtQueueProv.refID);

      if (_validID) {
        if (_layoutProv!.error != '') {
          throw _layoutProv.error;
        }

        if (!_layoutProv.isLoading) {
          _isLoading = _layoutProv.isLoading;
          _bodyWidgets = _layoutProv.bodyWidgets;
          setState(() {});
        }

        if (_layoutProv.refreshing && _modelLayout!.onRefresh != null) {
          _fresh.currentState!.show();
          _layoutProv.forceRefresh = false;
        }
      }

      Random random = new Random();
      String _hero = 'effectrnf-' + (random.nextInt(100)).toString();

      layoutForm() {
        try {
          Widget _daForm = DAPageForm(
            title: _modelLayout!.title,
            prefix: _modelLayout!.prefix,
            formKey: _formKey,
            formBody: _bodyWidgets,
            hasBackButton: _modelLayout!.hasBackButton,
            popUpMenu: _modelLayout!.popUpMenu,
          );

          Widget mainPage = (_modelLayout!.onRefresh == null)
              ? _daForm
              : Center(
                  child: RefreshIndicator(
                    key: _fresh,
                    backgroundColor: Colors.white,
                    onRefresh: () async => _modelLayout!.onRefresh!(),
                    child: _daForm,
                  ),
                );

          Widget focusPage = Focus(
            onFocusChange: (focus) {
              FocusNode _focus = FocusScope.of(context);

              if (_leave) {
                _leave = false;
                _tapOther = false;
                return;
              }

              if (_tapOther && _focusTime) {
                _focusTime = false;
                _leave = true;
                FocusScope.of(context).unfocus();
                FocusManager.instance.primaryFocus?.unfocus();
                return;
              }

              if (focus && !_focus.hasPrimaryFocus && _focus.hasFocus) {
                _tapOther = false;
              }

              if (!focus && !_focus.hasPrimaryFocus && !_focus.hasFocus) {
                _tapOther = true;
              }

              if (_tapOther) {
                _focusTime = true;
              }
            },
            child: mainPage,
          );

          Widget _builder = Builder(
            builder: (BuildContext context) {
              return focusPage;
            },
          );

          setState(() {});

          return _builder;
        } catch (e) {
          DAToast(context, e.toString());
          print(e.toString());
        }
      }

      int _el = 0;
      bool _reValidateForm() {
        bool hasError = false;
        for (final dynamic field in _bodyWidgets) {
          _el += 1;
          try {
            if (field.runtimeType.toString() == 'DAInput' ||
                field.runtimeType.toString() == 'DADropdown') {
              if (!field.isValid) {
                hasError = true;
                return !hasError;
              }
            }
          } catch (e) {
            hasError = hasError;
          }
        }
        return !hasError;
      }

      floatingBtnTap() async {
        try {
          _layoutProv!.isLoading = true;
          _isLoading = true;
          setState(() {});
          final form = _formKey.currentState;

          _hasLoading({String? msg}) {
            if (msg != null) {
              DAToast(context, msg,
                  useFlutterToast: _modelLayout!.useTrueToast ?? false);
            }
            _isLoading = false;
            setState(() {});
          }

          if (form!.validate()) {
            if (_reValidateForm()) {
              form.save();

              dynamic _formData = await _layoutProv.getFormData();
              await _modelLayout!.formSubmit!(_formData);

              _hasLoading();
            } else {
              _hasLoading(
                  msg:
                      'Datos incorrectos, favor de validar campo ${_el.toString()}');
            }
          } else {
            _hasLoading(msg: 'Datos incorrectos, favor de validar');
          }
        } catch (e) {
          _isLoading = false;
          DAToast(context, e.toString(),
              useFlutterToast: _modelLayout!.useTrueToast ?? false);
          setState(() {});
        }
      }

      validateSubmiting() async {
        if (_validID && _layoutProv!.submiting) {
          await _layoutProv.offSubmit();
          await floatingBtnTap();
        }
      }

      validateSubmiting();

      Widget _builder = LoadingOverlay(
        isLoading: _isLoading,
        child: Scaffold(
          key: _scaffoldKey,
          endDrawer: DAMainMenu(
            defLogoAvatar: widget.defLogoAvatar,
            avatarTap: () async {
              await DAController.saveAvatar(context, _scaffoldKey, () {
                setState(() {
                  _isLoading = false;
                });
              });
            },
            tiles: widget.appMenu,
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
          floatingActionButton: DAFloatingActionButton(
            icon: _modelLayout!.iconSubmit,
            heroTag: (_modelLayout!.heroTag) ?? _hero,
            onPressed: () async {
              await floatingBtnTap();
            },
          ),
          body: layoutForm(),
          backgroundColor: Color.fromRGBO(244, 243, 243, 1),
          bottomNavigationBar: _modelLayout!.bottomNavigationBar,
        ),
      );

      return _builder;
    } catch (e) {
      DAToast(context, e.toString());
      return Container();
    }
  }
}

class DALFormWgtQueueProv with ChangeNotifier {
  static DALFormWgtQueueProv _instancia = new DALFormWgtQueueProv._internal();
  DALFormWgtQueueProv._internal();

  factory DALFormWgtQueueProv() {
    return _instancia;
  }

  static String _refID = '';

  static get refID {
    return _refID;
  }

  static List<DALayoutFormWidgetsProvider> _mainQueue = [];

  static bool exists(String refID) {
    bool exists = false;

    for (final DALayoutFormWidgetsProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return true;
      }
    }

    return exists;
  }

  static add(DALayoutFormWidgetsProvider prov) {
    if (exists(prov.refID)) {
      removeByRefID(prov.refID);
    }
    _mainQueue.add(prov);
  }

  static DALayoutFormWidgetsProvider getByRefID(String refID) {
    for (final DALayoutFormWidgetsProvider _currProv in _mainQueue) {
      if (_currProv.refID == refID) {
        return _currProv;
      }
    }

    DALayoutFormWidgetsProvider _newProv =
        new DALayoutFormWidgetsProvider(refID: refID);
    _mainQueue.add(_newProv);
    return _newProv;
  }

  static removeByRefID(String refID) {
    if (exists(refID)) {
      _mainQueue.removeWhere((item) => item.refID == refID);
    }
  }

  notify(String refID) {
    _refID = refID;
    notifyListeners();
  }
}

class DALayoutFormWidgetsProvider {
  final String refID;

  @override
  DALayoutFormWidgetsProvider({
    required this.refID,
  });

  DALFormWgtQueueProv queue = DALFormWgtQueueProv();
  static List<Widget> _bodyWidgets = [];

  // ignore: avoid_init_to_null
  static bool? _isLoading = null; // NO QUITAR = null
  static String _error = '';
  static dynamic _metadata;
  static bool? _submiting;
  static bool? _forceRefresh;

  dynamic get isLoading {
    return _isLoading;
  }

  set isLoading(dynamic value) {
    _isLoading = value;
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    queue.notify(this.refID);
  }

  bool get submiting {
    return _submiting ?? false;
  }

  bool get refreshing {
    return _forceRefresh ?? false;
  }

  dynamic get metadata {
    return _metadata;
  }

  set metadata(dynamic value) {
    _metadata = value;
    queue.notify(this.refID);
  }

  List<Widget> get bodyWidgets {
    return _bodyWidgets;
  }

  submit() async {
    _submiting = true;
    queue.notify(this.refID);
  }

  set forceRefresh(dynamic value) {
    _forceRefresh = value;
  }

  pageRefresh() async {
    _forceRefresh = true;
    queue.notify(this.refID);
  }

  offSubmit() {
    _submiting = false;
  }

  _resetProv() {
    if (DALFormWgtQueueProv.exists(this.refID)) {
      DALFormWgtQueueProv.removeByRefID(this.refID);
    }

    DALFormWgtQueueProv.add(this);
  }

  set bodyWidgets(List<Widget>? value) {
    _bodyWidgets = (value) ?? [];

    _resetProv();
    queue.notify(this.refID);
  }

  List<dynamic>? getDataTable(String refID) {
    for (final Widget _wi in _bodyWidgets) {
      if (_wi.runtimeType.toString() == 'DADataTablePro') {
        DADataTablePro dt = _wi as DADataTablePro;
        if (dt.elID == refID) {
          return dt.data;
        }
      }
    }
    return null;
  }

  dynamic getFormData() async {
    try {
      Map<String, dynamic> res = {
        "dataTables": [],
      };

      for (final Widget _wi in _bodyWidgets) {
        switch (_wi.runtimeType.toString()) {
          case 'DADataTablePro':
            dynamic val = DAController.getWidgetValue(_wi);
            if (val != null) {
              res["dataTables"].add(val);
            }
            break;
          case 'DADropdown':
          case 'DAInput':
          case 'DADatePicker':
            dynamic val = DAController.getWidgetValue(_wi);
            if (val != null) {
              res[val.keys.first] = val[val.keys.first];
            }
            break;
          case 'DARadioList':
          case 'DASignature':
          case 'DaImageUpload':
            print('TODO ' + _wi.runtimeType.toString());
            break;
          default:
            break;
        }
      }

      if (res['dataTables'].length == 0) {
        res.remove('dataTables');
      }

      return res;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }
}
