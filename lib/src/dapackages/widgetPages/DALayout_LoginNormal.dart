import 'package:flutter/material.dart';
import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DALoginNormalPage extends StatefulWidget {
  final Widget loginLogo;
  final String appName;
  final String licence;
  final int idUsuarioTipo;

  DALoginNormalPage({
    required this.loginLogo,
    required this.appName,
    required this.licence,
    required this.idUsuarioTipo,
  });

  @override
  _DALoginNormalPageState createState() => _DALoginNormalPageState();
}

class _DALoginNormalPageState extends State<DALoginNormalPage> {
  // Para agilizar snackbar y loading
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  DASessionProvider _prov = DASessionProvider();
  DALastUserSelectionProvider _userSelectionProvider =
      DALastUserSelectionProvider();
  SessionModel _modelForm = new SessionModel();
  bool _saving = false;

  // Form controllers
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController _userController = new TextEditingController();
  TextEditingController _passController = new TextEditingController();

  String _empresaCfgCtrl = '';
  String _sucursalCfgCtrl = '';
  List<dynamic> empresaList = [];
  List<dynamic> sucursalList = [];

  // Auxiliares
  final focus = FocusNode();

  // API
  TextEditingController _apiController = new TextEditingController();
  void onValueChange() {
    setState(() {
      _apiController.text;
    });
  }

  @override
  void initState() {
    super.initState();
    _apiController.text = _prov.apiUri;
    _apiController.addListener(onValueChange);
    loadDataFromPrefs();
  }

  loadDataFromPrefs() async {
    await _userSelectionProvider.loadDataFromPrefs();
    if (_userController.text.toUpperCase().trim() ==
        _userSelectionProvider.lastUser) {
      _empresaCfgCtrl = _userSelectionProvider.lastEmpresa.toString();
      _sucursalCfgCtrl = _userSelectionProvider.lastSucursal.toString() == '-1'
          ? ''
          : _userSelectionProvider.lastSucursal.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    var login = DAPageLogin(
      isLoading: _saving,
      keyLoading: _scaffoldKey,
      formKey: _formKey,
      floatingActionButton: _configAPI(),
      body: _loginForm(), // cargamelo BD
    );

    // Widget noPop = WillPopScope(
    //   onWillPop: () async => false,
    //   child: login,
    // );

    return login;
  }

  Widget _configAPI() {
    return DAFloatingActionButton(
      heroTag: 'login',
      icon: _prov.connected ? Icons.cloud_done : Icons.cloud_off,
      onPressed: () => _apiDialog(),
    );
  }

  void _apiDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return DAInputDialog(
          title: 'Configurar app',
          subtitle: 'Ingrese Uri de API First',
          okText: 'Conectar',
          input: <Widget>[
            DAInput(
              refID: 'apiCfg',
              padding: 0.0,
              tipo: DAInputType.url,
              controller: _apiController,
              onSaved: (value) {
                _prov.apiUri = _apiController.text;
              },
            ),
          ],
          onPressed: _validaAPI,
        );
      },
    );
  }

  Future<void> _validaAPI() async {
    try {
      if (_prov.apiUri != _apiController.text) {
        _prov.reset();
        _prov.clear();
      }
      Navigator.of(context).pop();
      setState(() {
        _prov.connected = false;
        _prov.apiUri = _apiController.text;
        _saving = true;
      }); // Loading start

      final httpProv = new HttpProvider();
      var res = await httpProv.test();
      _prov.connected = (res.statusCode == 200);

      String msg = (res.statusCode == 200)
          ? 'Conexión Exitosa'
          : 'Error de servidor: ${res.reasonPhrase.toString()}';
      DAToast(context, msg);

      setState(() => _saving = false); // L
    } catch (e) {
      // ignore: unused_element
      setState(() {
        _saving = false; // Loading end
        _prov.connected = false;
        DAToast(context, e.toString());
      });
    }
  }

  // Login
  Widget _loginForm() {
    var form = Container(
      margin: EdgeInsets.symmetric(horizontal: 15.0),
      child: ListView(
        children: [
          SizedBox(height: 100.0),
          widget.loginLogo,
          SizedBox(height: 30.0),
          Center(
            child: Text(
              widget.appName,
              style: TextStyle(
                  color: Theme.of(context).textTheme.bodyLarge!.color,
                  fontSize: 20.0,
                  fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(height: 30.0),
          DAInput(
            refID: 'user',
            tipo: DAInputType.notEmail,
            label: 'Usuario',
            textInputAction: TextInputAction.next,
            controller: _userController,
            onFieldSubmitted: (v) {
              FocusScope.of(context).requestFocus(focus);
            },
            onSaved: (value) {
              _modelForm.username = value;
            },
          ),
          SizedBox(height: 30.0),
          DAInput(
            refID: 'password',
            tipo: DAInputType.password,
            label: 'Contraseña',
            focusNode: focus,
            textInputAction: TextInputAction.go,
            controller: _passController,
            onFieldSubmitted: (value) {
              _login();
            },
            onSaved: (value) {
              _modelForm.password = value?.toUpperCase();
            },
          ),
          SizedBox(height: 60.0),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: DAButton(
              label: 'Ingresar',
              onPressed: () async => await _login(),
            ),
          ),
          SizedBox(height: 30.0),
        ],
      ),
    );

    return form;
  }

  Future<void> _login() async {
    // setState(() async {
    try {
      final form = _formKey.currentState;
      form!.save();

      if (!_prov.connected) {
        DAToast(context, 'Requiere configurar un API válida');
        return;
      }

      if (form.validate()) {
        setState(() => _saving = true); // Loading start

        if (_prov.session.estacion != null) {
          _modelForm.estacion = _prov.session.estacion;
        }

        await loadDataFromPrefs();

        final httpProv = new HttpProvider();
        var res = await httpProv.login(_modelForm, widget.licence);

        if (res.statusCode == 200) {
          //almacenar refresh token y access token
          var respuesta = json.decode(res.body);
          var profiles = respuesta['profiles'] != null
              ? json.decode(respuesta['profiles'])
              : [];
          var empresas = respuesta['Empresas'] != null
              ? json.decode(respuesta['Empresas'])
              : [];
          var sucursales = respuesta['Sucursales'] != null
              ? json.decode(respuesta['Sucursales'])
              : [];
          var sucursalesNombres = respuesta['SucursalesNombres'] != null
              ? json.decode(respuesta['SucursalesNombres'])
              : [];
          var sucursalesBackup = [];

          if (profiles.length > 0) {
            profiles.forEach((profile) {
              if (profile['IDUsuarioTipo'].toString() !=
                      respuesta['IDUsuarioTipo'].toString() &&
                  profile['IDUsuarioTipo'].toString() ==
                      widget.idUsuarioTipo.toString()) {
                respuesta['IDUsuario'] = profile['IDUsuario'];
                respuesta['Usuario'] = profile['Usuario'];
                respuesta['IDUsuarioTipo'] = profile['IDUsuarioTipo'];
                respuesta['IDRole'] = profile['IDRole'];
                respuesta['Empresa'] = profile['Empresa'];
                respuesta['Sucursal'] = profile['Sucursal'];
              }
            });
          }

          // Validamos unicamente cuando el usuario trae IDUsuarioTipo
          if (respuesta['IDUsuarioTipo'] != null) {
            if (respuesta['IDUsuarioTipo'].toString() !=
                widget.idUsuarioTipo.toString()) {
              await httpProv.logOut();
              throw ('Tipo de usuario inválido');
            }
          }

          if (empresas.length > 0) {
            empresas = empresas.toSet().toList();
          } else {
            await httpProv.logOut();
            throw ('El usuario no tiene acceso a ninguna Empresa');
          }

          if (sucursales.length > 0 && sucursalesNombres.length > 0) {
            sucursales = sucursales.toSet().toList();
            sucursalesNombres = sucursalesNombres.toSet().toList();
          } else if (sucursales.length > 0) {
            sucursales.forEach((sucursal) {
              sucursalesBackup.add({
                "Sucursal": sucursal["Sucursal"],
                "Nombre": sucursal["Nombre"]
              });
            });
          } else {
            await httpProv.logOut();
            throw ('El usuario no tiene acceso a ninguna Sucursal');
          }

          empresaList.clear();
          sucursalList.clear();

          if (empresas.length > 0) {
            empresas.forEach((empresa) {
              if (empresa is Map) {
                empresaList.add({
                  "Empresa": empresa["Empresa"],
                  "Nombre": "${empresa["Empresa"]} - ${empresa["Nombre"]}"
                });
              } else {
                empresaList.add({"Empresa": empresa});
              }
            });
          }

          if (sucursales.length > 0 && sucursalesNombres.length > 0) {
            sucursales.asMap().forEach((index, sucursal) {
              sucursalList.add(
                  {"Sucursal": sucursal, "Nombre": sucursalesNombres[index]});
            });
          } else if (sucursalesBackup.length > 0) {
            sucursalesBackup.forEach((sucursal) {
              sucursalList.add({
                "Sucursal": sucursal["Sucursal"],
                "Nombre": "${sucursal["Sucursal"]} - ${sucursal["Nombre"]}"
              });
            });
          }

          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return DAInputDialog(
                title: 'Configurar app',
                subtitle: 'Seleccione Empresa y Sucursal',
                okText: 'Ingresar',
                input: <Widget>[
                  DADropdown(
                    refID: 'empresaCfg',
                    data: empresaList,
                    controllerValue: _empresaCfgCtrl,
                    isRequired: true,
                    inputLabel: 'Empresa',
                    value: 'Empresa',
                    text: 'Empresa',
                    onChanged: (value) {
                      _empresaCfgCtrl = value.toString();
                    },
                    onSaved: (value) {
                      _empresaCfgCtrl = value.toString();
                    },
                  ),
                  DADropdown(
                    refID: 'sucursalCfg',
                    data: sucursalList,
                    controllerValue: _sucursalCfgCtrl,
                    isRequired: true,
                    inputLabel: 'Sucursal',
                    value: 'Sucursal',
                    text: 'Nombre',
                    onChanged: (value) {
                      _sucursalCfgCtrl = value.toString();
                    },
                    onSaved: (value) {
                      _sucursalCfgCtrl = value.toString();
                    },
                  ),
                ],
                onPressed: () {
                  respuesta["UserEmpresa"] = _empresaCfgCtrl;
                  respuesta["UserSucursal"] = _sucursalCfgCtrl;

                  if (respuesta['Empresa'] == null ||
                      respuesta['Empresa'] == '' ||
                      respuesta['Empresa'] == 'null') {
                    respuesta['Empresa'] = _empresaCfgCtrl;
                  }

                  _userSelectionProvider.lastUser = _userController.text;
                  _userSelectionProvider.lastEmpresa = _empresaCfgCtrl;
                  _userSelectionProvider.lastSucursal =
                      int.parse(_sucursalCfgCtrl);
                  _userSelectionProvider.saveDataToPrefs();

                  _prov.session = sessionModelFromLogin(respuesta);
                  _formKey.currentState!.reset();

                  Navigator.pushNamed(context, '/home');
                },
                onCancelPressed: () async {
                  await httpProv.logOut();
                  _userController.clear();
                  _passController.clear();
                  _empresaCfgCtrl = '';
                  _sucursalCfgCtrl = '';
                },
              );
            },
          );
        } else if (res.statusCode == 400) {
          var respuesta = json.decode(res.body);
          String msg = respuesta["error_description"].toString();
          // Valida si la estación en sesión es valida
          if (msg == 'Estacion invalida.') {
            _prov.reset();
            _prov.resetEstacion();
            _prov.resetUserEmpresaSucursal();
            _modelForm.estacion = _prov.session.estacion;
            msg = '$msg Favor de intentar nuevamente.';
          }
          DAToast(context, msg);
          //_snakeBar(msg);
        } else {
          //ERROR NO CONTROLADO DESDE EL API
          String msg = res.reasonPhrase.toString();
          DAToast(context, 'Error de servidor: $msg');
        }

        setState(() => _saving = false); // Loading end
      }
    } catch (e) {
      setState(() => _saving = false); // Loading end
      DAToast(context, e.toString());
    }
  }
}
