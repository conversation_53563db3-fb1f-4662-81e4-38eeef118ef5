import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
export 'package:provider/provider.dart';
export '../http/http_provider.dart';
////////////////////////////////////////////////////////////////////////////////
import '../main_DAPackages.dart';

class DAMainProvider {
  static DAMainProvider _instancia = new DAMainProvider._internal();
  DAMainProvider._internal();
  factory DAMainProvider() {
    return _instancia;
  }

  static Widget multiProvider(Widget materialApp) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => DASessionProvider()),
        ChangeNotifierProvider(create: (context) => DAMainLoadingProvider()),
        ChangeNotifierProvider(create: (context) => DAUsuarioProvider()),
        ChangeNotifierProvider(create: (context) => DASearchDelegateProvider()),
        ChangeNotifierProvider(create: (context) => DAMenuCardsPageProvider()),
        ChangeNotifierProvider(
            create: (context) => DALayoutListCardQueueProv()),
        ChangeNotifierProvider(create: (context) => DALayoutHdrDetProvider()),
        ChangeNotifierProvider(create: (context) => DALayoutHdrDetProvider2()),
        ChangeNotifierProvider(create: (context) => DALFormWgtQueueProv()),
        ChangeNotifierProvider(create: (context) => DALChatQueueProv()),
        ChangeNotifierProvider(create: (context) => DALayoutListMapQueueProv()),
        ChangeNotifierProvider(create: (context) => DALayoutMapViewProvider()),
        ChangeNotifierProvider(
            create: (context) => DALastUserSelectionProvider()),
        ChangeNotifierProvider(
          create: (context) => DALPaymentQueueProv(),
        )
      ],
      child: materialApp,
    );
  }
}

class DASessionProvider with ChangeNotifier {
  static final DASessionProvider _instancia = DASessionProvider._internal();
  static SessionModel _session = new SessionModel();
  static String _apiUri = '';
  static String _avatar = '';
  static bool _connected = false;
  static bool _toLogin = false;

  factory DASessionProvider() {
    return _instancia;
  }

  DASessionProvider._internal();
  late SharedPreferences _prefs;

  initPrefs() async {
    this._prefs = await SharedPreferences.getInstance();
  }

  bool get appIsOffline => _prefs.getBool('AppIsOffline') ?? false;

  validaRefresh() async {
    this.loadSession();

    if (_session.access == null) {
      return false;
    }

    DateTime now = new DateTime.now();
    DateTime token = _session.expires ?? now;
    int difference = token.difference(now).inMinutes;

    if (difference <= 0) {
      if (_session.refresh == null) {
        this.reset();
        return false;
      }

      final httpProv = new HttpProvider();
      var res = await httpProv.refresh(_session);
      if (res.statusCode == 200) {
        var respuesta = json.decode(res.body);
        // Se valida Empresa y Sucursal indicada por el usuario solo LoginNormal
        if (_session.userEmpresa != null && _session.userSucursal != null) {
          respuesta['Empresa'] = _session.userEmpresa;
          respuesta['Sucursal'] = _session.userSucursal;
          respuesta['UserEmpresa'] = _session.userEmpresa;
          respuesta['UserSucursal'] = _session.userSucursal;
        }
        // Se valida Agente del usuario si se encuentra en sesion
        if (_session.agente != null) {
          respuesta['Agente'] = _session.agente;
        }
        // Se valida Montacargas del usuario si se encuentra en sesion WMS
        if (_session.montacargas != null) {
          respuesta['Montacargas'] = _session.montacargas;
        }
        // Se valida Almacén del usuario si se encuentra en sesion WMS
        if (_session.almacen != null) {
          respuesta['Almacen'] = _session.almacen;
        }
        // Se valida IDAcceso del usuario si se encuentra en sesion WMS
        if (_session.idAcceso != null) {
          respuesta['IDAcceso'] = _session.idAcceso;
        }
        _session = sessionModelFromLogin(respuesta);
        _instancia.session = _session;
        return true;
      } else {
        return false;
      }
    }

    return true;
  }

  void reset() {
    _session.username = null;
    _session.password = null;
    _session.access = null;
    _session.refresh = null;
    _session.expires = null;
    _session.nombre = null;
    _session.empresa = null;
    _session.apodo = null;
    _session.descripcion = null;
    _session.usuario = null;
    _session.agente = null;

    String sessionString = sessionModelToJsonString(_session);
    _prefs.setString('session', sessionString);
  }

  void resetEstacion() {
    _session.estacion = null;

    String sessionString = sessionModelToJsonString(_session);
    _prefs.setString('session', sessionString);
  }

  void resetUserEmpresaSucursal() {
    _session.userEmpresa = null;
    _session.userSucursal = null;

    String sessionString = sessionModelToJsonString(_session);
    _prefs.setString('session', sessionString);
  }

  void resetMontacargas() {
    _session.montacargas = null;

    String sessionString = sessionModelToJsonString(_session);
    _prefs.setString('session', sessionString);
  }

  void resetAlmacen() {
    _session.almacen = null;

    String sessionString = sessionModelToJsonString(_session);
    _prefs.setString('session', sessionString);
  }

  void resetIDAcceso() {
    _session.idAcceso = null;

    String sessionString = sessionModelToJsonString(_session);
    _prefs.setString('session', sessionString);
  }

  void clear() {
    _prefs.clear();
  }

  loadSession() async {
    String? sessionString = _prefs.getString('session') ?? null;
    if (sessionString != null && sessionString != '') {
      _session = sessionModelFromJsonString(sessionString);
    }

    if (_session.access == null || _session.refresh == null) {
      _toLogin = false;
    } else {
      _toLogin = true;
    }

    return;
  }

  bool _toBoolean(String? str, [bool? strict]) {
    if (str == null) return false;
    if (strict == true) {
      return str == '1' || str == 'true';
    }
    return str != '0' && str != 'false' && str != '';
  }

  setAgente() async {
    try {
      //SessionProvider _prov = SessionProvider();
      final httpProvider = new HttpProvider();
      String route =
          "/CRUD/Usuario?c=defagente&q=Usuario:${this.session.usuario}";

      List<dynamic> data = await httpProvider.get(route);
      this.session.agente = data[0]['defagente'].toString();
      String sessionString = sessionModelToJsonString(this.session);
      _prefs.setString('session', sessionString);
    } catch (e) {
      print(e);
      throw e;
    }
  }

  setEmpresaSucursal() {
    this.session.empresa = this.session.userEmpresa;
    this.session.sucursal = this.session.userSucursal;
    String sessionString = sessionModelToJsonString(this.session);
    _prefs.setString('session', sessionString);
  }

  setMontacargas(String montacargas) async {
    try {
      this.session.montacargas = montacargas;
      String sessionString = sessionModelToJsonString(this.session);
      _prefs.setString('session', sessionString);
    } catch (e) {
      print(e);
      throw e;
    }
  }

  setSucursal(String sucursal) async {
    try {
      this.session.sucursal = sucursal;
      String sessionString = sessionModelToJsonString(this.session);
      _prefs.setString('session', sessionString);
    } catch (e) {
      print(e);
      throw e;
    }
  }

  setAlmacen(String almacen) async {
    try {
      this.session.almacen = almacen;
      String sessionString = sessionModelToJsonString(this.session);
      _prefs.setString('session', sessionString);
    } catch (e) {
      print(e);
      throw e;
    }
  }

  setIDAcceso(String idAcceso) async {
    try {
      this.session.idAcceso = idAcceso;
      String sessionString = sessionModelToJsonString(this.session);
      _prefs.setString('session', sessionString);
    } catch (e) {
      print(e);
      throw e;
    }
  }

  // GET y SET de Login
  get iconConnected {
    var x = _prefs.getString('apiconn');
    var _ac = _toBoolean(x);
    _connected = (_ac == false) ? false : true;
    return _connected;
  }

  bool get connected {
    var x = _prefs.getString('apiconn');
    var _ac = _toBoolean(x);
    _connected = (_ac == false) ? false : true;
    return _connected;
  }

  set connected(bool? value) {
    _connected = (value == null || value == false) ? false : true;
    _prefs.setString('apiconn', _connected.toString().toLowerCase());
  }

  // GET y SET de Login
  String get apiUri {
    _apiUri = _prefs.getString('apiUri') ?? '';
    return _apiUri;
  }

  set apiUri(String value) {
    _apiUri = value;
    _prefs.setString('apiUri', _apiUri);
  }

  // GET y SET Avatar
  String get avatar {
    _avatar = _prefs.getString('avatar') ?? '';
    return _avatar;
  }

  set avatar(String? value) {
    if (value != null) {
      _avatar = value;
      _prefs.setString('avatar', _avatar);
    }
  }

  SessionModel get session {
    return _session;
  }

  set session(SessionModel value) {
    _session = value;

    String sessionString = sessionModelToJsonString(_session);
    _prefs.setString('session', sessionString);
  }

  static get toLogin {
    return _toLogin;
  }

  getAppConfig(String _licence, {int? idUsuarioTipo}) async {
    try {
      //SessionProvider _prov = SessionProvider();
      final httpProvider = new HttpProvider();
      String _tipo = (idUsuarioTipo == null || idUsuarioTipo == 0)
          ? ''
          : '&Tipo=' + idUsuarioTipo.toString();
      String route = "/ME?App=" + _licence + _tipo;
      return await httpProvider.get(route);
    } catch (e) {
      print(e);
      throw e;
    }
  }

  bool _isNumeric(String str) {
    // ignore: unnecessary_null_comparison
    if (str == null) {
      return false;
    }
    return (double.tryParse(str) != null);
  }

  int? fixSucursal(value) {
    if (_isNumeric(value.toString())) {
      if (value is int) {
        return value;
      } else {
        return int.parse(value.toString());
      }
    }

    return null;
  }

  void notifyLogOut() async {
    // Notificamos Logout
    _toLogin = false;
    notifyListeners();
  }
}

class DALastUserSelectionProvider with ChangeNotifier {
  static DALastUserSelectionProvider _instancia =
      new DALastUserSelectionProvider._internal();
  DALastUserSelectionProvider._internal();
  factory DALastUserSelectionProvider() {
    return _instancia;
  }

  static String _lastUser = '';
  static String _lastEmpresa = '';
  static int _lastSucursal = 0;
  static String _lastMail = '';
  static String _lastPassword = '';
  static bool _rememberUser = false;

  String get lastUser {
    return _lastUser;
  }

  set lastUser(String value) {
    _lastUser = value;
    notifyListeners();
  }

  String get lastEmpresa {
    return _lastEmpresa;
  }

  set lastEmpresa(String value) {
    _lastEmpresa = value;
    notifyListeners();
  }

  int get lastSucursal {
    return _lastSucursal;
  }

  set lastSucursal(int value) {
    _lastSucursal = value;
    notifyListeners();
  }

  String get lastMail {
    return _lastMail;
  }

  set lastMail(String value) {
    _lastMail = value;
    notifyListeners();
  }

  String get lastPassword {
    return _lastPassword;
  }

  set lastPassword(String value) {
    _lastPassword = value;
    notifyListeners();
  }

  bool get rememberUser {
    return _rememberUser;
  }

  set rememberUser(bool value) {
    _rememberUser = value;
    notifyListeners();
  }

  void notify() async {
    // Notificamos Cambios
    notifyListeners();
  }

  void saveDataToPrefs() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('lastUser', _lastUser.toUpperCase().trim());
    prefs.setString('lastEmpresa', _lastEmpresa);
    prefs.setInt('lastSucursal', _lastSucursal);
    prefs.setString('lastMail', _lastMail);
    prefs.setString('lastPassword', _lastPassword);
    prefs.setBool('rememberUser', _rememberUser);
  }

  loadDataFromPrefs() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    _lastUser = prefs.getString('lastUser') ?? '';
    _lastEmpresa = prefs.getString('lastEmpresa') ?? '';
    _lastSucursal = prefs.getInt('lastSucursal') ?? -1;
    _lastMail = prefs.getString('lastMail') ?? '';
    _lastPassword = prefs.getString('lastPassword') ?? '';
    _rememberUser = prefs.getBool('rememberUser') ?? false;
  }

  void reset() {
    _lastUser = '';
    _lastEmpresa = '';
    _lastSucursal = 0;
    _lastMail = '';
    _lastPassword = '';
    _rememberUser = false;
    notifyListeners();
  }
}

class DAUsuarioProvider with ChangeNotifier {
  static DAUsuarioProvider _instancia = new DAUsuarioProvider._internal();
  DAUsuarioProvider._internal();
  factory DAUsuarioProvider() {
    return _instancia;
  }

  // ignore: unused_field
  static bool _loading = false;
  // ignore: unused_field
  late AppCfgModel _appConfig;

  void notify() async {
    // Notificamos Cambios
    notifyListeners();
  }

  ImageProvider? get avatar {
    DASessionProvider _prefs = DASessionProvider();
    final String? avatar = _prefs.avatar;

    if (avatar != null) {
      final Uint8List resAvatar = Base64Codec().decode(avatar);
      ImageProvider res = Image.memory(resAvatar, fit: BoxFit.cover).image;
      return res;
    }
    return null;
  }

  setAvatar(String? filePath) async {
    try {
      DASessionProvider _prefs = DASessionProvider();
      final _api = new DASincroProvider();
      // ignore: avoid_init_to_null
      var apiAvatar = null;

      if (filePath != null) {
        List<int> imageBytes = File(filePath).readAsBytesSync();
        final String base64Img = base64Encode(imageBytes);
        final String prefix = (filePath.split('.').last == 'png')
            ? "data:image/png;base64,"
            : "data:image/jpeg;base64,";
        apiAvatar = prefix + base64Img;
        // await _api.setAvatar(apiAvatar);
        _api.setAvatar(apiAvatar);
        _prefs.avatar = base64Img;
      } else {
        await _api.setAvatar(apiAvatar);
        _prefs.avatar = null;
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future saveAvatar(BuildContext context, GlobalKey<ScaffoldState> scaffoldKey,
      Image? image) async {
    final _picker = ImagePicker();

    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext bc) {
        return ClipRRect(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(10),
            topLeft: Radius.circular(10),
          ),
          child: Container(
            color: Colors.white,
            child: new Wrap(
              children: [
                ListTile(
                  title: new Text(
                    'Seleccione Foto de Perfil',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
                ListTile(
                  title: Row(
                    children: [
                      Icon(Icons.delete_forever),
                      SizedBox(width: 5.0),
                      Text('Eliminar foto')
                    ],
                  ),
                  onTap: () async {
                    await setAvatar(null);
                    DAToast(context, 'Se elimino correctamente');
                    await Future.delayed(
                      const Duration(milliseconds: 500),
                      () {
                        Navigator.of(context)
                            .popUntil(ModalRoute.withName('home'));
                      },
                    );
                  },
                ),
                ListTile(
                  title: Row(
                    children: [
                      Icon(Icons.image),
                      SizedBox(width: 5.0),
                      Text('Galería')
                    ],
                  ),
                  onTap: () async {
                    final pickedFile = await _picker.pickImage(
                      source: ImageSource.gallery,
                    );
                    if (pickedFile != null) {
                      await setAvatar(pickedFile.path);
                      DAToast(context, 'Se actualizó correctamente');
                      await Future.delayed(
                        const Duration(milliseconds: 500),
                        () {
                          Navigator.of(context)
                              .popUntil(ModalRoute.withName('home'));
                        },
                      );
                    }
                  },
                ),
                ListTile(
                  title: Row(
                    children: [
                      Icon(Icons.camera_alt),
                      SizedBox(width: 5.0),
                      Text('Cámara')
                    ],
                  ),
                  onTap: () async {
                    final pickedFile =
                        await _picker.pickImage(source: ImageSource.camera);
                    if (pickedFile != null) {
                      await setAvatar(pickedFile.path);
                      DAToast(context, 'Se actualizó correctamente');
                      await Future.delayed(
                        const Duration(milliseconds: 500),
                        () {
                          Navigator.of(context)
                              .popUntil(ModalRoute.withName('home'));
                        },
                      );
                    }
                  },
                ),
                ListTile(
                  title: new Text(
                    'Cancelar',
                    style: TextStyle(color: Theme.of(context).primaryColor),
                  ),
                  onTap: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  refAvatar() async {
    final _api = new DASincroProvider();
    dynamic avatar = await _api.getAvatar();
    String? bAvatar = (avatar != null ? base64Encode(avatar) : null);

    DASessionProvider _prefs = DASessionProvider();
    _prefs.avatar = bAvatar;

    return bAvatar;
  }

  setAppConfig(AppCfgModel appConfig) async {
    try {
      _appConfig = appConfig;
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }
}

class DASincroRoutes {
  static DASincroRoutes _instancia = new DASincroRoutes._internal();
  DASincroRoutes._internal();
  factory DASincroRoutes() {
    return _instancia;
  }

  // Obtiene datos de Usuario Logueado
  static get usuario {
    return "/ME";
  }

  // Para uso de Avatar
  static get avatar {
    return "/Avatar/ME";
  }
}

class DAMainLoadingProvider with ChangeNotifier {
  static DAMainLoadingProvider _instancia =
      new DAMainLoadingProvider._internal();
  DAMainLoadingProvider._internal();

  factory DAMainLoadingProvider() {
    return _instancia;
  }

  //final HttpProvider _httpProvider = new HttpProvider();
  static dynamic _dataParams = [];
  static dynamic _dataList = [];
  static dynamic _dataListFiltro = [];
  static String _error = '';
  static bool _forceRefresh = false;

  dynamic get dataParams {
    return _dataParams;
  }

  set dataParams(dynamic value) {
    _dataParams = value;
    notifyListeners();
  }

  dynamic get data {
    return _dataList;
  }

  set data(dynamic value) {
    _dataList = value;
    notifyListeners();
  }

  dynamic get dataFiltro {
    return _dataListFiltro;
  }

  set dataFiltro(dynamic value) {
    _dataListFiltro = value;
    notifyListeners();
  }

  dynamic get error {
    return _error;
  }

  set error(dynamic value) {
    _error = value;
    notifyListeners();
  }

  dynamic get forceRefresh {
    return _forceRefresh;
  }

  set forceRefresh(dynamic value) {
    _forceRefresh = value;
    if (_forceRefresh) {
      notifyListeners();
    }
  }

  static setFiltro(String? query) {
    try {
      _dataListFiltro = _dataList;
      if (query != null) {
        _dataListFiltro = _dataList
            .where((val) => jsonEncode(val)
                .toString()
                .replaceAll("{", "")
                .replaceAll("}", "")
                .replaceAll(":", "")
                .replaceAll(",", "")
                .replaceAll('"', '')
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();
      }
    } catch (e) {
      print('setFiltro: ' + e.toString());
      throw e.toString();
    }
  }

  // ignore: unnecessary_question_mark
  Future paramsData(String uri, dynamic? body) async {
    try {
      List<dynamic> data = await DAMainLoadingProvider.execAPI(uri, body);
      this.dataParams = data;
    } catch (e) {
      this.dataParams = [];
      print('MovimientoProvider.descargar: ' + e.toString());
      this.error = e.toString();
      throw e.toString();
    }
  }

  // ignore: unnecessary_question_mark
  Future descargar(String uri, dynamic? body,
      {String? filter, bool? isDataSet}) async {
    try {
      // Recarga de Movs
      //_dataList.clear();
      List<dynamic> data = await DAMainLoadingProvider.execAPI(uri, body);

      _dataList = data;

      if (filter == null || filter == '') {
        _dataListFiltro = _dataList;
      } else {
        setFiltro(filter);
      }

      //notifyListeners();
      //throw ('MovimientoProvider.descargar: Prueba');
    } catch (e) {
      _dataListFiltro = [];
      _dataList = [];
      print('MovimientoProvider.descargar: ' + e.toString());
      throw e.toString();
    }
  }

  static Future<dynamic> execAPI(String uri, dynamic body) async {
    try {
      final HttpProvider _httpProv = new HttpProvider();
      dynamic data;
      if (body == null) {
        data = await _httpProv.get(uri);
      } else {
        data = await _httpProv.post(uri, body);
      }
      return data;
    } catch (e) {
      print('MovimientoProvider.descargar: ' + e.toString());
      throw e.toString();
    }
  }

  static Future<dynamic> execAPIEsp(String uri, dynamic body) async {
    try {
      final HttpProvider _httpProv = new HttpProvider();
      dynamic data;
      if (body == null) {
        data = await _httpProv.get(uri);
      } else {
        data = await _httpProv.post(uri, body);
      }
      return data;
    } catch (e) {
      print('MovimientoProvider.descargar: ' + e.toString());
      return {'Ok': -1, 'OkRef': e.toString()};
    }
  }

  static Future<dynamic> updateAPI(String uri, dynamic body) async {
    try {
      final HttpProvider _httpProv = new HttpProvider();
      dynamic data;
      data = await _httpProv.put(uri, body);

      return data;
    } catch (e) {
      print('MovimientoProvider.descargar: ' + e.toString());
      throw e.toString();
    }
  }

  static Future<dynamic> deleteAPI(String uri, dynamic body) async {
    try {
      final HttpProvider _httpProv = new HttpProvider();
      dynamic data;
      data = await _httpProv.delete(uri, body);

      return data;
    } catch (e) {
      print('MovimientoProvider.descargar: ' + e.toString());
      throw e.toString();
    }
  }

  void notify() async {
    // Notificamos Cambios
    notifyListeners();
  }
}

class DASearchDelegateProvider with ChangeNotifier {
  static DASearchDelegateProvider _instancia =
      new DASearchDelegateProvider._internal();
  DASearchDelegateProvider._internal();
  factory DASearchDelegateProvider() {
    return _instancia;
  }

  static dynamic _finderData = [];
  static bool _downloading = false;

  // ignore: unnecessary_getters_setters
  bool get downloading {
    return _downloading;
  }

  // ignore: unnecessary_getters_setters
  set downloading(bool value) {
    _downloading = value;
    //notifyListeners();
  }

  // ignore: unnecessary_getters_setters
  dynamic get finderData {
    return _finderData;
  }

  // ignore: unnecessary_getters_setters
  set finderData(dynamic value) {
    _finderData = value;
    //notifyListeners();
  }

  setFiltro(dynamic data, String? query) {
    try {
      dynamic _dataListFiltro = data;
      if (query != null) {
        _dataListFiltro = data
            .where((val) => jsonEncode(val)
                .toString()
                .replaceAll("{", "")
                .replaceAll("}", "")
                .replaceAll(":", "")
                .replaceAll(",", "")
                .replaceAll('"', '')
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();
      }

      _finderData = _dataListFiltro;
    } catch (e) {
      print('setFiltro: ' + e.toString());
      throw e.toString();
    }
  }

  void notify() async {
    // Notificamos Cambios
    notifyListeners();
  }
}

class DASincroProvider with ChangeNotifier {
  static DASincroProvider _instancia = new DASincroProvider._internal();
  DASincroProvider._internal();
  factory DASincroProvider() {
    return _instancia;
  }

  double _avance = 0.0;

  double get avance {
    return _avance;
  }

  set avance(double? value) {
    this._avance = (value) ?? 0.0;
    print(_avance);
    notifyListeners();
  }

  DASessionProvider prefs = DASessionProvider();
  HttpProvider httpProv = new HttpProvider();

  void notify() async {
    // Notificamos Cambios
    notifyListeners();
  }

  Future usuarioApp(String _licence) async {
    try {
      final String endPoint = DASincroRoutes.usuario + "?App=" + _licence;
      return await httpProv.get(endPoint).timeout(Duration(seconds: 360));
    } catch (e) {
      print(e);
      throw "Error al obtener datos de usuario.";
    }
  }

  Future getAvatar() async {
    try {
      DASessionProvider _sessionProv = DASessionProvider();
      if (!_sessionProv.appIsOffline) {
        String endPoint = DASincroRoutes.avatar;
        return await httpProv
            .getImage(endPoint)
            .timeout(Duration(seconds: 360));
      }
    } catch (e) {
      print(e);
      throw "Error al obtener avatar y datos de usuario.";
    }
  }

  Future setAvatar(String avatar) async {
    try {
      final String endPoint = DASincroRoutes.avatar;
      final dynamic data = {"file": avatar};
      var res =
          await httpProv.post(endPoint, data).timeout(Duration(seconds: 360));
      return res;
    } catch (e) {
      print(e);
      throw "Error al cargar avatar.";
    }
  }

  Future usuarioFull() async {
    try {
      final String endPoint = DASincroRoutes.usuario + "?full=true";
      return await httpProv.get(endPoint).timeout(Duration(seconds: 360));
    } catch (e) {
      print(e);
      throw "Error al obtener datos completos del usuario.";
    }
  }
}
