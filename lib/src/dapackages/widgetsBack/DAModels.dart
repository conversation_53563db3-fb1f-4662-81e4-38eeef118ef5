import 'package:easy_pdf_viewer/easy_pdf_viewer.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../main_DAPackages.dart';
// ignore: unnecessary_import
import 'DAWidgetsComponents.dart';

DASessionProvider _eProv = DASessionProvider();

AppCfgModel appCfgModelFromJsonString(String str) =>
    AppCfgModel.fromJson(json.decode(str));

AppCfgModel appCfgModelFromJson(Map<String, dynamic> json) =>
    AppCfgModel.fromJson(json);

String appCfgModelToJson(AppCfgModel data) => json.encode(data.toJson());

class AppCfgModel {
  String? usuario;
  String? empresa;
  String? agente;
  int? sucursal;
  bool? cb;
  bool? cbDirAlmacen;
  bool? cbPreguntarCantidad;
  bool? cbProcesarLote;
  bool? cbSerieLote;
  bool? cbSubCodigos;
  bool? cbSubCuentas;

  AppCfgModel({
    this.usuario,
    this.empresa,
    this.agente,
    this.sucursal,
    this.cb,
    this.cbDirAlmacen,
    this.cbPreguntarCantidad,
    this.cbProcesarLote,
    this.cbSerieLote,
    this.cbSubCodigos,
    this.cbSubCuentas,
  });

  factory AppCfgModel.fromJson(Map<String, dynamic> json) => AppCfgModel(
        usuario: json["Usuario"],
        empresa: json["Empresa"],
        agente: json["Agente"],
        sucursal: _eProv.fixSucursal(json["Sucursal"]),
        cb: json["CB"],
        cbDirAlmacen: json["CBDirAlmacen"],
        cbPreguntarCantidad: json["CBPreguntarCantidad"],
        cbProcesarLote: json["CBProcesarLote"],
        cbSerieLote: json["CBSerieLote"],
        cbSubCodigos: json["CBSubCodigos"],
        cbSubCuentas: json["CBSubCuentas"],
      );

  Map<String, dynamic> toJson() => {
        "Usuario": usuario,
        "Empresa": empresa,
        "Agente": agente,
        "Sucursal": sucursal,
        "CB": cb,
        "CBDirAlmacen": cbDirAlmacen,
        "CBPreguntarCantidad": cbPreguntarCantidad,
        "CBProcesarLote": cbProcesarLote,
        "CBSerieLote": cbSerieLote,
        "CBSubCodigos": cbSubCodigos,
        "CBSubCuentas": cbSubCuentas,
      };
}

SessionModel sessionModelFromJsonString(String str) =>
    SessionModel.fromJson(json.decode(str));
SessionModel sessionModelFromJson(Map<String, dynamic> json) =>
    SessionModel.fromJson(json);
SessionModel sessionModelFromLogin(Map<String, dynamic> json) =>
    SessionModel.fromJsonLogin(json);

Map<String, dynamic> sessionModelToJson(SessionModel data) => data.toJson();

String sessionModelToJsonString(SessionModel data) =>
    json.encode(data.toJson());

String grantPassword(SessionModel data, String licence) =>
    "username=${data.username}&password=${data.password}&grant_type=password&IntelisisMK=$licence${data.estacion != null ? '&estacion=${data.estacion}' : ''}";

String grantRefresh(SessionModel data) =>
    "refresh_token=${data.refresh}&grant_type=refresh_token";

class SessionModel {
  String? username;
  String? password;
  String? access;
  String? refresh;
  DateTime? expires;
  String? estacion;
  String? nombre;
  String? empresa;
  String? sucursal;
  String? apodo;
  String? descripcion;
  String? usuario;
  String? agente;
  String? montacargas;
  String? almacen;
  String? idAcceso;
  String? userEmpresa;
  String? userSucursal;
  bool? isOffline;

  SessionModel({
    this.username,
    this.password,
    this.access,
    this.refresh,
    this.expires,
    this.estacion,
    this.nombre,
    this.empresa,
    this.sucursal,
    this.apodo,
    this.descripcion,
    this.usuario,
    this.agente,
    this.montacargas,
    this.almacen,
    this.idAcceso,
    this.userEmpresa,
    this.userSucursal,
    this.isOffline = false,
  });

  factory SessionModel.fromJsonLogin(Map<String, dynamic> json) {
    dynamic source = json["expires_in"];
    DateTime now = new DateTime.now();
    DateTime after = now.add(new Duration(seconds: source));

    return SessionModel(
      username: json["eMail"],
      password: json["Contrasena"],
      access: json["access_token"],
      refresh: json["refresh_token"],
      expires: after,
      estacion: json["Estacion"],
      nombre: json["Nombre"],
      empresa: json["Empresa"],
      sucursal: json["Sucursal"],
      apodo: json["Apodo"],
      descripcion: json["DescripcionTipo"],
      usuario: json["Usuario"],
      agente: json["Agente"],
      montacargas: json["Montacargas"],
      almacen: json["Almacen"],
      idAcceso: json["IDAcceso"],
      userEmpresa: json["UserEmpresa"],
      userSucursal: json["UserSucursal"],
      isOffline: false,
    );
  }

  factory SessionModel.fromJson(Map<String, dynamic> json) {
    int timestamp = json["expires_in"];
    DateTime expires = new DateTime.fromMillisecondsSinceEpoch(timestamp);

    return SessionModel(
      username: json["eMail"],
      password: json["Contrasena"],
      access: json["access_token"],
      refresh: json["refresh_token"],
      expires: expires,
      estacion: json["Estacion"],
      nombre: json["Nombre"],
      empresa: json["Empresa"],
      sucursal: json["Sucursal"],
      apodo: json["Apodo"],
      descripcion: json["DescripcionTipo"],
      usuario: json["Usuario"],
      agente: json["Agente"],
      montacargas: json["Montacargas"],
      almacen: json["Almacen"],
      idAcceso: json["IDAcceso"],
      userEmpresa: json["UserEmpresa"],
      userSucursal: json["UserSucursal"],
      isOffline: json["isOffline"],
    );
  }

  Map<String, dynamic> toJson() {
    DateTime fecha = (expires) ?? new DateTime.now();
    int timestamp = fecha.millisecondsSinceEpoch;

    Map<String, dynamic> json = {
      "eMail": username,
      "Contrasena": password,
      "access_token": access,
      "refresh_token": refresh,
      "expires_in": timestamp,
      "Estacion": estacion,
      "Nombre": nombre,
      "Empresa": empresa,
      "Sucursal": sucursal,
      "Apodo": apodo,
      "DescripcionTipo": descripcion,
      "Usuario": usuario,
      "Agente": agente,
      "Montacargas": montacargas,
      "Almacen": almacen,
      "IDAcceso": idAcceso,
      "UserEmpresa": userEmpresa,
      "UserSucursal": userSucursal,
      "isOffline": isOffline,
    };

    return json;
  }
}

class DARequestModel {
  String uriReq;
  Map<String, dynamic>? bodyReq;

  DARequestModel({
    required this.uriReq,
    this.bodyReq,
  });
}

class DAAppConfigModel {
  String licence;
  String appName;
  String homeLayout;
  int idUsuarioTipo;
  Color primaryColor;
  Color? accentColor;
  Widget? logo;
  Widget? loginLogo;
  Widget? logoAvatar;
  Widget? minLogo;
  Widget? appBarLogo;
  String? fontName;
  String? pushAppID;
  bool isOffline;

  DAAppConfigModel({
    required this.licence,
    required this.appName,
    required this.homeLayout,
    required this.idUsuarioTipo,
    required this.primaryColor,
    this.accentColor,
    this.logo,
    this.loginLogo,
    this.logoAvatar,
    this.minLogo,
    this.appBarLogo,
    this.fontName,
    this.pushAppID,
    this.isOffline = false,
  });
}

class DAConfigRowModel {
  String label;
  String scope;
  bool? isnumeric;
  bool? showEditIcon;
  bool? placeholder;
  DAConfigRowModelEditing? editConfig;

  DAConfigRowModel({
    required this.label,
    required this.scope,
    this.isnumeric,
    this.showEditIcon,
    this.placeholder,
    this.editConfig,
  });
}

class DAConfigDropdownEditing {
  List<dynamic> data;
  final String value;
  final String text;

  DAConfigDropdownEditing({
    required this.data,
    required this.value,
    required this.text,
  });
}

class DAConfigRowModelEditing {
  bool? editable = true;
  DADataTableTypes tipo;
  bool? showInTitle;
  DAConfigDropdownEditing? dropdownCfg;
  final Widget? customchild;

  DAConfigRowModelEditing({
    required this.tipo,
    this.editable,
    this.showInTitle,
    this.dropdownCfg,
    this.customchild,
  });
}

class DAMinDialogModel {
  String title;
  String? subtitle;
  String? okText;
  List<Widget>? body;
  Function()? onOkDialog;

  DAMinDialogModel({
    required this.title,
    this.subtitle,
    this.okText,
    this.body,
    this.onOkDialog,
  });
}

class DALayoutDetModelHeaderTitleModel {
  String titlePage;
  String? subTitlePage;
  String? titlePageNull;

  DALayoutDetModelHeaderTitleModel({
    required this.titlePage,
    this.subTitlePage,
    this.titlePageNull,
  });
}

class DALayoutDetModelHeaderMainModel {
  DALayoutDetModelHeaderTitleModel headerTitle;
  List<DADefListModel> headerDetail;

  DALayoutDetModelHeaderMainModel({
    required this.headerTitle,
    required this.headerDetail,
  });
}

class DALayoutDetModelTableActionsModel {
  final Function(dynamic)? onTap;
  final Function(dynamic)? onDoubleTap;
  final Function(dynamic)? onLongPress;
  final Function(dynamic)? onRowSave;

  DALayoutDetModelTableActionsModel({
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onRowSave,
  });
}

class DALayoutDetModelTableMainModel {
  final List<DAConfigRowModel> tableDef;
  final String scopeRowID;
  final DALayoutDetModelTableActionsModel? tableActions;
  final String? noDataMsg;
  final List<DADataTableListTile>? listLongPress;

  DALayoutDetModelTableMainModel({
    required this.tableDef,
    required this.scopeRowID,
    this.tableActions,
    this.noDataMsg,
    this.listLongPress,
  });
}

class DALayoutDetModel {
  String refID;
  DARequestModel? dataSource;
  DALayoutDetModelHeaderMainModel header;
  String? finderLabel;
  DALayoutDetModelTableMainModel tableConfig;
  DAFloatingActionButton? floatingActionButton;
  DABottomAppBar? bottomNavigationBar;

  DALayoutDetModel({
    required this.refID,
    this.dataSource,
    required this.header,
    this.finderLabel,
    required this.tableConfig,
    this.floatingActionButton,
    this.bottomNavigationBar,
  });
}

List<DADefListModel> defListModelFromJson(String str) =>
    List<DADefListModel>.from(
        json.decode(str).map((x) => DADefListModel.fromJson(x)));
String defListModelToJson(List<DADefListModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

DADefListModel modelDefListModelFromJson(String str) =>
    json.decode(str).map((x) => DADefListModel.fromJson(x));
String modelDefListModelToJson(DADefListModel data) =>
    json.encode(data.toJson());

class DADefListModel {
  String? id;
  String? title;
  String? subtitle;
  String? leading;
  String? trailing;
  bool? changed;
  String? icon;
  FormFieldSetter<dynamic>? onCardTap;
  String? latitude;
  String? longitude;
  String? metadata;
  String? showBadge = '';

  DADefListModel({
    this.id,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.changed,
    this.icon,
    this.onCardTap,
    this.latitude,
    this.longitude,
    this.metadata,
    this.showBadge,
  });

  factory DADefListModel.fromJson(Map<String, dynamic> json) => DADefListModel(
        id: json["id"],
        title: json["title"],
        subtitle: json["subtitle"],
        leading: json["leading"],
        trailing: json["trailing"],
        changed: json["changed"],
        icon: json["icon"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        metadata: json["metadata"],
        showBadge: json["showBadge"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "subtitle": subtitle,
        "leading": leading,
        "trailing": trailing,
        "changed": changed,
        "icon": icon,
        "latitude": latitude,
        "longitude": longitude,
        "metadata": metadata,
        "showBadge": showBadge,
      };
}

/////////////////////////////////////////////////

List<DAFinderCardModel> finderCardModelFromJson(String str) =>
    List<DAFinderCardModel>.from(
        json.decode(str).map((x) => DAFinderCardModel.fromJson(x)));
String finderCardModelToJson(List<DAFinderCardModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

DAFinderCardModel modelFinderCardModelFromJson(String str) =>
    json.decode(str).map((x) => DAFinderCardModel.fromJson(x));
String modelFinderCardModelModelToJson(DAFinderCardModel data) =>
    json.encode(data.toJson());

class DAFinderCardModel {
  String? id;
  String? title;
  String? leading;
  String? trailing;
  List<String>? moreLabels;
  List<String>? filterLabels;
  bool? changed;
  String? icon;
  bool? selected;
  Function(dynamic)? onCardTap;

  DAFinderCardModel({
    this.id,
    this.title,
    this.leading,
    this.trailing,
    this.moreLabels,
    this.filterLabels,
    this.changed,
    this.icon,
    this.selected,
    this.onCardTap,
  });

  factory DAFinderCardModel.fromJson(Map<String, dynamic> json) =>
      DAFinderCardModel(
        id: json["id"],
        title: json["title"],
        leading: json["leading"],
        trailing: json["trailing"],
        changed: json["changed"],
        icon: json["icon"],
        selected: false,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "leading": leading,
        "trailing": trailing,
        "changed": changed,
        "icon": icon,
      };
}

class DAUnbordingContent {
  String image;
  String title;
  String discription;

  DAUnbordingContent({
    required this.image,
    required this.title,
    required this.discription,
  });
}

class DALayoutListModel {
  String refID;
  String title;
  String? hintText;
  String? noDataMsg;
  DAFloatingActionButton? floatingActionButton;
  String prefix;
  RefreshCallback? dataSource;
  DADefListModel cardConfig;
  DABottomAppBar? bottomNavigationBar;
  bool? hasBackButton;
  bool? hasFinder;
  bool? hasTotal;
  bool? useCustomTotal;
  double? amount;
  Widget? customTotal;
  RefreshCallback? onRefresh;
  IconData? iconSubmit;
  String? heroTag;

  DALayoutListModel({
    required this.refID,
    required this.prefix,
    required this.title,
    this.hintText,
    this.noDataMsg,
    this.floatingActionButton,
    this.dataSource,
    required this.cardConfig,
    this.bottomNavigationBar,
    this.hasBackButton,
    this.hasFinder,
    this.hasTotal,
    this.useCustomTotal,
    this.amount,
    this.customTotal,
    this.onRefresh,
    this.iconSubmit,
    this.heroTag,
  });
}

class DALayoutListMapModel {
  String refID;
  String title;
  DAFloatingActionButton? floatingActionButton;
  RefreshCallback? dataSource;
  DADefListModel cardConfig;
  bool? hasBackButton;
  DABottomAppBar? bottomNavigationBar;
  RefreshCallback? onRefresh;
  LatLng? currPosition;

  DALayoutListMapModel({
    required this.refID,
    required this.title,
    this.floatingActionButton,
    required this.cardConfig,
    this.dataSource,
    this.hasBackButton,
    this.bottomNavigationBar,
    this.onRefresh,
    this.currPosition,
  });
}

class DALayoutMapViewModel {
  String title;
  DAFloatingActionButton? floatingActionButton;
  bool? hasBackButton;
  EdgeInsetsGeometry? daScreenAlignment;
  List<Widget>? pageWidgets;
  DABottomAppBar? bottomNavigationBar;

  DALayoutMapViewModel({
    required this.title,
    this.floatingActionButton,
    this.hasBackButton,
    this.daScreenAlignment,
    this.pageWidgets,
    this.bottomNavigationBar,
  });
}

class DAMenuCardsModel {
  List<DABigMenuButton> menuButtons;
  final String? prefix;
  final String? title;
  final String? noData;
  DAFloatingActionButton? floatingActionButton;
  DABottomAppBar? bottomNavigationBar;

  DAMenuCardsModel({
    required this.menuButtons,
    this.prefix,
    this.title,
    this.noData,
    this.floatingActionButton,
    this.bottomNavigationBar,
  });
}

class DADataTableListTile {
  IconData icon;
  String title;
  Function(dynamic value)? validation;
  Function(dynamic value)? onTap;

  DADataTableListTile({
    required this.icon,
    required this.title,
    this.validation,
    this.onTap,
  });
}

class DALayoutFormFilters {
  String refID;
  String title;
  List<Widget> formBody;
  String? prefix;
  String? heroTag;
  bool? hasBackButton;
  final IconData iconSubmit;
  final Function(dynamic value)? formSubmit;
  DABottomAppBar? bottomNavigationBar;
  RefreshCallback? onRefresh;
  Widget? popUpMenu;
  bool? useTrueToast;

  DALayoutFormFilters({
    required this.refID,
    required this.title,
    required this.formBody,
    this.prefix,
    this.heroTag,
    this.hasBackButton,
    required this.iconSubmit,
    this.formSubmit,
    this.bottomNavigationBar,
    this.onRefresh,
    this.popUpMenu,
    this.useTrueToast,
  });
}

class DALayoutChatFilters {
  String refID;
  String title;
  List<Widget> formBody;
  String? prefix;
  String? heroTag;
  bool? hasBackButton;
  final IconData iconSubmit;
  final Function(dynamic value)? formSubmit;
  RefreshCallback? onRefresh;
  Widget? popUpMenu;
  TextEditingController? messageCtrl;
  TextEditingController? receivedCtrl;
  Widget titleChatPage;
  List<dynamic>? chat;
  List<Widget>? chatBody;
  final VoidCallback onSendChat;
  final VoidCallback onMicTap;
  ScrollController? scrollController;
  FocusNode? focusNode;

  DALayoutChatFilters({
    required this.refID,
    required this.title,
    required this.formBody,
    this.prefix,
    this.heroTag,
    this.hasBackButton,
    required this.iconSubmit,
    this.formSubmit,
    this.onRefresh,
    this.popUpMenu,
    this.messageCtrl,
    this.receivedCtrl,
    required this.titleChatPage,
    this.chat,
    this.chatBody,
    required this.onSendChat,
    required this.onMicTap,
    this.scrollController,
    this.focusNode,
  });
}

class DALayoutPaymentFilters {
  String refID;
  String title;
  List<Widget> formBody;
  DABottomAppBar? bottomNavigationBar;
  RefreshCallback? dataSource;
  String? prefix;
  String? heroTag;
  bool? hasBackButton;
  final Function(dynamic value)? formSubmit;
  RefreshCallback? onRefresh;
  Widget? popUpMenu;
  TextEditingController? messageCtrl;
  TextEditingController? receivedCtrl;
  List<dynamic>? chat;
  List<Widget>? chatBody;
  ScrollController? scrollController;
  FocusNode? focusNode;
  bool? isPDF;
  PDFDocument? pdfPath;
  Function? onNotification;
  Widget? floatingActionButton;

  DALayoutPaymentFilters({
    required this.refID,
    required this.title,
    required this.formBody,
    this.bottomNavigationBar,
    this.dataSource,
    this.prefix,
    this.heroTag,
    this.hasBackButton,
    this.formSubmit,
    this.onRefresh,
    this.popUpMenu,
    this.messageCtrl,
    this.receivedCtrl,
    this.chat,
    this.chatBody,
    this.scrollController,
    this.focusNode,
    this.isPDF = false,
    this.pdfPath,
    this.onNotification,
    this.floatingActionButton,
  });
}

class SignedImageRes {
  String imageStr;
  Uint8List? bytesImagen;

  SignedImageRes({
    required this.imageStr,
    this.bytesImagen,
  });
}

class DAScreenAlignment {
  static EdgeInsetsGeometry top({required BuildContext context}) {
    return EdgeInsets.only(top: 100.0);
  }

  static EdgeInsetsGeometry center({required BuildContext context}) {
    double scrHeight = View.of(context).physicalSize.height;
    return EdgeInsets.only(top: scrHeight * 0.5);
  }

  static EdgeInsetsGeometry bottom({required BuildContext context}) {
    double scrHeight = View.of(context).physicalSize.height;
    return EdgeInsets.only(top: scrHeight - 165.0);
  }
}

enum DAInputType {
  string,
  number,
  email,
  url,
  password,
  textarea,
  ayuda,
  notEmail,
  userOrEmail,
}

enum DADataTableTypes {
  input_default,
  input_number,
  input_email,
  input_notEmail,
  input_url,
  input_password,
  input_textarea,
  dropdown,
  datepicker,
  custom,
}

enum DADecomposerType {
  string,
  number,
  datetime,
}

class DADecomposerModel {
  String name;
  int start;
  int end;
  DADecomposerType? type;
  int? decimales;
  String? dateFormat;

  DADecomposerModel({
    required this.name,
    required this.start,
    required this.end,
    this.type = DADecomposerType.string,
    this.decimales = 0,
    this.dateFormat = 'yyMMdd',
  });
}
