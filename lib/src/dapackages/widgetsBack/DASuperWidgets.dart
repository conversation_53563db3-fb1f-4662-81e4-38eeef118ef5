// ignore: import_of_legacy_library_into_null_safe

import 'dart:ui' as ui;
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:badges/badges.dart' as badges;
import 'package:shared_preferences/shared_preferences.dart';
import '../main_DAPackages.dart';

export 'dart:convert';
export 'dart:typed_data';
export 'dart:async';
export 'package:geolocator/geolocator.dart';
export 'package:flutter_svg/flutter_svg.dart';
export 'package:url_launcher/url_launcher.dart';
export 'package:image_picker/image_picker.dart';
export 'package:loading_overlay/loading_overlay.dart';
export 'package:flutter_signature_pad/flutter_signature_pad.dart';

class DAPageLogin extends StatelessWidget {
  final bool? isLoading;
  final GlobalKey<ScaffoldState>? keyLoading;
  final GlobalKey<FormState>? formKey;
  final Widget? floatingActionButton;
  final Widget body;

  DAPageLogin({
    this.isLoading,
    this.keyLoading,
    this.formKey,
    this.floatingActionButton,
    required this.body,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: (this.isLoading) ?? false,
      color: Colors.black,
      progressIndicator: CircularProgressIndicator(
        strokeWidth: 5,
      ),
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : Colors.white,
        ),
        key: this.keyLoading,
        body: Stack(children: [
          DALoginBackground(),
          Form(
            key: this.formKey,
            child: this.body,
          ),
        ]),
        floatingActionButton: Container(
          padding: EdgeInsets.only(top: 50),
          child: this.floatingActionButton,
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
      ),
    );
  }
}

class DAPageLoginPagos extends StatelessWidget {
  final bool? isLoading;
  final GlobalKey<ScaffoldState>? keyLoading;
  final GlobalKey<FormState>? formKey;
  final Widget? floatingActionButton;
  final Widget body;

  DAPageLoginPagos({
    this.isLoading,
    this.keyLoading,
    this.formKey,
    this.floatingActionButton,
    required this.body,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: (this.isLoading) ?? false,
      color: Colors.black,
      progressIndicator: CircularProgressIndicator(
        strokeWidth: 5,
      ),
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
          elevation: 0,
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : Colors.white,
        ),
        key: this.keyLoading,
        body: Stack(children: [
          DALoginPagosBackground(),
          Form(
            key: this.formKey,
            child: this.body,
          ),
        ]),
        floatingActionButton: Container(
          padding: EdgeInsets.only(top: 50),
          child: this.floatingActionButton,
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
      ),
    );
  }
}

// ignore: must_be_immutable
class DaMainAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  final Size preferredSize;
  final String title;
  final Widget? avatar;
  final List<String>? actions;
  final Function(String, BuildContext)? onSelected;

  DaMainAppBar({
    required this.title,
    // this.icon,
    this.avatar,
    this.actions,
    this.onSelected,
    required Key key,
  })  : preferredSize = Size.fromHeight(76.0),
        super(key: key);

  DASessionProvider prov = DASessionProvider();
  SessionModel user = new SessionModel();

  @override
  Widget build(BuildContext context) {
    user = prov.session;

    return Material(
      elevation: 5.0,
      child: Container(
        color: Theme.of(context).primaryColor,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppBar(
              elevation: 0,
              title: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 10.0),
                    child: SvgPicture.asset('assets/app/minLogo.svg',
                        height: 30.0),
                    //icon,
                  ),
                  Text(this.title),
                ],
              ),
              actions: <Widget>[
                Container(
                  child: (avatar == null)
                      ? CircleAvatar(
                          child: Text(user.username!.substring(0, 1)),
                          backgroundColor:
                              Theme.of(context).colorScheme.secondary,
                        )
                      : Center(
                          child: avatar,
                        ),
                ),
                PopupMenuButton<String>(
                  itemBuilder: _actionsBuilder(context),
                  onSelected: (val) => (onSelected!(val, context)),
                ),
              ],
              automaticallyImplyLeading: false,
            ),
          ],
        ),
      ),
    );
  }

  _actionsBuilder(BuildContext context) {
    final List<String> newActions = (this.actions) ?? [];
    return (context) {
      return newActions.map((String choice) {
        return PopupMenuItem<String>(
          value: choice,
          child: Text(choice),
        );
      }).toList();
    };
  }
}

class DaScaffoldLoading extends StatelessWidget {
  final bool? isLoading;
  final List<Widget> children;
  final GlobalKey<ScaffoldState>? keyLoading;
  final PreferredSizeWidget? appBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;

  DaScaffoldLoading({
    this.isLoading,
    required this.children,
    this.keyLoading,
    this.appBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
  });

  @override
  Widget build(BuildContext context) {
    return LoadingOverlay(
      isLoading: (this.isLoading) ?? false,
      color: Colors.black,
      progressIndicator: CircularProgressIndicator(
        strokeWidth: 5,
      ),
      child: Scaffold(
        key: this.keyLoading,
        appBar: this.appBar,
        body: Stack(children: this.children),
        floatingActionButton: this.floatingActionButton,
        floatingActionButtonLocation: this.floatingActionButtonLocation,
      ),
    );
  }
}

class DAPageList extends StatelessWidget {
  final DALayoutListModel? config;
  final List<dynamic> data;
  final TextEditingController finderController;
  final bool? hasBackButton;
  final bool? hasFinder;
  final bool? hasTotal;
  final bool? useCustomTotal;
  final double? total;
  final Function()? onFinderTap;
  final Function(String)? onFinderChanged;
  final RefreshCallback? onRefresh;
  final FormFieldSetter<DADefListModel>? onCardTap;

  DAPageList({
    required this.config,
    required this.data,
    required this.finderController,
    this.hasBackButton,
    this.hasFinder,
    this.hasTotal,
    this.total,
    this.useCustomTotal,
    this.onFinderTap,
    this.onFinderChanged,
    this.onRefresh,
    this.onCardTap,
  });

  @override
  Widget build(BuildContext context) {
    Widget _finder = DAPageFilter(
      hintText: this.config!.hintText,
      controller: finderController,
      onTap: this.onFinderTap,
      onChanged: (valor) => this.onFinderChanged!(valor),
    );

    filterCards() {
      try {
        List<dynamic> dataFilter = [];
        dataFilter = this.data;

        if (finderController.text != '') {
          dataFilter = this
              .data
              .where((val) => jsonEncode(val)
                  .toString()
                  .replaceAll("{", "")
                  .replaceAll("}", "")
                  .replaceAll(":", "")
                  .replaceAll(",", "")
                  .replaceAll('"', '')
                  .toString()
                  .toUpperCase()
                  .contains(finderController.text.toUpperCase()))
              .toList();
        }

        return dataFilter;
      } catch (e) {
        print('setFiltro: ' + e.toString());
        throw e.toString();
      }
    }

    Widget _listPage = DACardList(
      data: filterCards(),
      config: this.config!.cardConfig,
      tipoMsg: config!.noDataMsg,
      onTap: this.onCardTap,
    );

    Widget listaBody = SliverList(
      delegate: SliverChildListDelegate(
        [
          new Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              (this.hasTotal == true && this.useCustomTotal == false)
                  ? DABigMoneyTotal(currency: '\$ ', amount: this.total ?? 0)
                  : Container(),
              (this.hasTotal == true && this.useCustomTotal == true)
                  ? this.config!.customTotal ?? Container()
                  : Container(),
              (this.hasFinder == true) ? _finder : Container(),
              SizedBox(height: 10),
              _listPage,
            ],
          ),
        ],
      ),
    );

    Widget list = CustomScrollView(
      slivers: <Widget>[
        DATitleBig(
          prefix: this.config!.prefix.toString(),
          title: this.config!.title.toString(),
        ),
        listaBody,
      ],
    );

    Widget _daBackButton =
        (this.hasBackButton == true) ? DABackButton() : Container();

    Widget refreshPage = RefreshIndicator(
      child: Stack(
        children: [
          list,
          _daBackButton,
        ],
      ),
      onRefresh: () async => this.onRefresh!(),
    );

    return refreshPage;
  }
}

class DAPageForm extends StatelessWidget {
  final List<Widget> formBody;
  final Key formKey;
  final String? prefix;
  final String? title;
  final String? heroTag;
  final FormFieldSetter<String>? onSaved;
  final bool? hasBackButton;
  final RefreshCallback? onRefresh;
  final Widget? popUpMenu;

  DAPageForm({
    required this.formBody,
    required this.formKey,
    this.prefix,
    this.title,
    this.heroTag,
    this.onSaved,
    this.hasBackButton,
    this.onRefresh,
    this.popUpMenu,
  });

  @override
  Widget build(BuildContext context) {
    Widget _border = Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      padding: EdgeInsets.only(
        bottom: 40.0,
      ),
    );

    List<Widget> _formBody = [];

    _fixInput(Widget daInput) {
      Widget _input = new Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 10.0),
        child: daInput,
      );
      _formBody.add(_input);
    }

    this.formBody.forEach((Widget daInput) => _fixInput(daInput));

    _formBody.add(_border);
    _formBody.add(SizedBox(height: 45.0));

    Widget _form = SliverList(
      delegate: SliverChildListDelegate(
        _formBody,
      ),
    );

    Widget _daBackButton =
        (this.hasBackButton == true) ? DABackButton() : Container();

    Widget _page = Form(
      key: this.formKey,
      child: CustomScrollView(
        slivers: <Widget>[
          DATitleBig(
            prefix: (this.prefix) ?? '',
            title: (this.title) ?? 'Cargando...',
            heroTag: this.heroTag,
            hasLeading: (this.hasBackButton ?? false),
          ),
          _form,
        ],
      ),
    );

    Widget resPage = Stack(
      children: [
        _page,
        _daBackButton,
        this.popUpMenu ?? Container(),
      ],
    );

    if (this.onRefresh == null) {
      return resPage;
    } else {
      return RefreshIndicator(
        child: resPage,
        onRefresh: () async => this.onRefresh!(),
      );
    }
  }
}

class DADataTableMin extends StatelessWidget {
  final List<DAConfigRowModel> columnsDef;
  final List<dynamic> dataSource;
  final int? sortColumnIndex;
  final bool? sortAscending;
  final String? selected;
  final String? noDataMsg;

  final Function(int columnIndex, bool ascending, dynamic value)? onSort;
  final Function(dynamic)? onTap;
  final Function(dynamic)? onDoubleTap;
  final Function(dynamic)? onLongPress;

  DADataTableMin({
    required this.columnsDef,
    required this.dataSource,
    this.sortColumnIndex,
    this.sortAscending,
    this.selected,
    this.onSort,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.noDataMsg,
  });

  @override
  Widget build(BuildContext context) {
    if (this.dataSource.length == 0) {
      return DANoData(
        tipo: (this.noDataMsg) ?? "registros",
      );
    }

    List<DAConfigRowModel> _columnsDef = this.columnsDef;

    List<DataColumn> myCols = _columnsDef.map((value) {
      return DataColumn(
        label: Text(
          value.label,
          style: TextStyle(fontStyle: FontStyle.italic),
        ),
        numeric: (value.isnumeric) ?? false,
        tooltip: value.label,
        onSort: (columnIndex, sortAscending) {
          this.onSort!(columnIndex, sortAscending, value);
        },
      );
    }).toList();

    List<DataRow> myRows = this.dataSource.map((dataRow) {
      return DataRow(
        selected: (this.selected == json.encode(dataRow)),
        cells: _columnsDef.map((value) {
          return DataCell(
            Text(dataRow[value.scope].toString()),
            showEditIcon: (value.showEditIcon) ?? false,
            placeholder: (value.placeholder) ?? false,
            onTap: () {
              this.onTap!(dataRow);
            },
            onDoubleTap: () {
              this.onDoubleTap!(dataRow);
            },
            onLongPress: () {
              this.onLongPress!(dataRow);
            },
          );
        }).toList(),
      );
    }).toList();

    Widget _dataTable = SingleChildScrollView(
      child: DataTable(
        sortColumnIndex: this.sortColumnIndex,
        sortAscending: (this.sortAscending) ?? false,
        columns: myCols,
        rows: myRows,
      ),
      scrollDirection: Axis.horizontal,
    );

    return _dataTable;
  }
}

// ignore: must_be_immutable
class DADataTablePro extends StatefulWidget {
  final String refID;
  final String scopeRowID;
  final List<DAConfigRowModel> columnsDef;
  List<dynamic> dataSource;
  final bool? hasFinder;
  final String? finderLabel;
  final DALayoutDetModelTableActionsModel? tableActions;
  final String? noDataMsg;
  final bool? autoRefresh;
  final List<DADataTableListTile>? listLongPress;

  DADataTablePro({
    required this.refID,
    required this.scopeRowID,
    required this.columnsDef,
    required this.dataSource,
    this.hasFinder,
    this.finderLabel,
    this.tableActions,
    this.noDataMsg,
    this.autoRefresh,
    this.listLongPress,
  });

  String get elID => this.refID;
  List<dynamic> get data => this.dataSource;

  @override
  _DADataTablePro createState() => _DADataTablePro();
}

class _DADataTablePro extends State<DADataTablePro> {
  // ignore: unused_field
  int _sortColumnIndex = 0;
  bool _inSort = false;
  bool _sortAsc = true;
  String? _selected;
  List<dynamic> _dtData = [];
  List<dynamic> _filterData = [];
  String? _dtQuery;
  final TextEditingController _dtFilterFinderController =
      new TextEditingController();

  @override
  Widget build(BuildContext context) {
    setFiltro();

    List<DAConfigRowModel> _columnsDef = widget.columnsDef;

    List<DataColumn> myCols = _columnsDef.map((value) {
      return DataColumn(
        label: Text(
          value.label,
          style: TextStyle(fontStyle: FontStyle.italic),
        ),
        numeric: (value.isnumeric) ?? false,
        tooltip: value.label,
        onSort: (columnIndex, sortAscending) async {
          _inSort = true;
          _sortColumnIndex = columnIndex;
          _sortAsc = !_sortAsc;

          _filterData.sort((a, b) => a[value.scope]
              .toString()
              .toLowerCase()
              .compareTo(b[value.scope].toString().toLowerCase()));

          _dtData = (!_sortAsc) ? _filterData.reversed.toList() : _filterData;
          //await setFiltro();
          setState(() {});
        },
      );
    }).toList();

    bool isDate(String str) {
      return DateTime.tryParse(str) != null;
    }

    List<DataRow> myRows = _dtData.map((dataRow) {
      return DataRow(
        selected: (_selected == json.encode(dataRow)),
        cells: _columnsDef.map((DAConfigRowModel value) {
          String myVal = (dataRow[value.scope] == null)
              ? ''
              : dataRow[value.scope].toString();

          // Verificamos si es fecha
          try {
            if (isDate(myVal.toString().trim())) {
              if (DateTime.tryParse(myVal) != null &&
                  value.isnumeric != false) {
                DateTime parsedDate = DateTime.parse(myVal);
                myVal = DateFormat('dd/MM/yyyy').format(parsedDate);
              }
            }
          } catch (e) {
            print(e.toString());
          }

          if (value.editConfig != null) {
            if (value.editConfig!.dropdownCfg != null) {
              for (final dynamic field in value.editConfig!.dropdownCfg!.data) {
                if (field[value.editConfig!.dropdownCfg!.value.toString()] ==
                    myVal) {
                  myVal = field[value.editConfig!.dropdownCfg!.text.toString()];
                }
              }
            }
          }

          return DataCell(
            Text(myVal.toString()),
            showEditIcon: (value.showEditIcon) ?? false,
            placeholder: (value.placeholder) ?? false,
            onTap: () async {
              _selected = json.encode(dataRow);
              setState(() {});
              await widget.tableActions!.onTap!(dataRow);
            },
            onDoubleTap: () async {
              _selected = json.encode(dataRow);
              setState(() {});
              await _tableDoubleTap(dataRow);
              //await widget.tableActions!.onDoubleTap!(dataRow);
            },
            onLongPress: () async {
              _selected = json.encode(dataRow);
              setState(() {});
              await _tableLongPress(dataRow);
            },
          );
        }).toList(),
      );
    }).toList();

    Widget _dataTable = Container(
      margin: EdgeInsets.only(top: 5),
      child: SingleChildScrollView(
        child: DataTable(
          sortColumnIndex: _sortColumnIndex,
          sortAscending: _sortAsc,
          columns: myCols,
          rows: myRows,
        ),
        scrollDirection: Axis.horizontal,
      ),
    );

    Widget _finder = DAPageFilter(
      hintText: (widget.finderLabel) ?? 'Buscar...',
      icon: Icons.filter_list,
      controller: _dtFilterFinderController,
      padding: EdgeInsets.symmetric(
        horizontal: 20.0,
      ),
      onTap: () {
        _dtFilterFinderController.text = '';
        _dtQuery = null;
        _selected = null;
        setFiltro();
        setState(() {});
      },
      onChanged: (valor) {
        _dtQuery = valor;
        _selected = null;
        setFiltro();
        setState(() {});
      },
    );

    return Column(
      children: [
        Visibility(
          visible: (widget.hasFinder) ?? false,
          child: _finder,
        ),
        (_filterData.length > 0)
            ? _dataTable
            : DANoData(
                tipo: (widget.noDataMsg) ?? "registros",
              ),
      ],
    );
  }

  setFiltro() async {
    try {
      if (_dtQuery != null && _dtQuery != '') {
        _filterData = (_inSort)
            ? _dtData
            : widget.dataSource
                .where((val) => jsonEncode(val)
                    .toString()
                    .replaceAll("{", "")
                    .replaceAll("}", "")
                    .replaceAll(":", "")
                    .replaceAll(",", "")
                    .replaceAll('"', '')
                    .toString()
                    .toUpperCase()
                    .contains(_dtQuery!.toUpperCase()))
                .toList();
      } else {
        _filterData = (_inSort) ? _dtData : widget.dataSource;
      }

      _dtData = _filterData;
      _inSort = false;
    } catch (e) {
      print('setFiltro: ' + e.toString());
      throw e.toString();
    }
  }

  _tableDoubleTap(dynamic data) async {
    bool _showEdit = false;
    for (final DAConfigRowModel _colDef in widget.columnsDef) {
      if (_colDef.editConfig != null) {
        if (_colDef.editConfig!.editable != false) {
          _showEdit = true;
        }
      }
    }

    if (widget.tableActions!.onDoubleTap != null) {
      await widget.tableActions!.onDoubleTap!(data);
    }

    if (_showEdit) {
      await _confirmAddDialog(data);
    }
  }

  _tableLongPress(dynamic data) async {
    List<Widget> _mainActions = [
      ListTile(
        title: new Text(
          'Seleccione la acción a ejecutar',
          style: TextStyle(color: Colors.grey),
        ),
      ),
    ];

    bool _showEdit = false;
    for (final DAConfigRowModel _colDef in widget.columnsDef) {
      if (_colDef.editConfig != null) {
        if (_colDef.editConfig!.editable != false) {
          _showEdit = true;
        }
      }
    }

    if ((widget.listLongPress ?? []).length == 0 && _showEdit == false) {
      if (widget.tableActions!.onLongPress != null) {
        await widget.tableActions!.onLongPress!(data);
        return;
      } else {
        return;
      }
    }

    if (_showEdit) {
      _mainActions.add(
        ListTile(
          title: Row(
            children: [
              Icon(Icons.edit),
              SizedBox(width: 5.0),
              Text('Editar registro')
            ],
          ),
          onTap: () async {
            Navigator.pop(context);
            await _tableDoubleTap(data);
          },
        ),
      );
    }

    if ((widget.listLongPress ?? []).length > 0) {
      for (final DADataTableListTile _action in widget.listLongPress!) {
        bool showTile = false;

        if (_action.validation == null) {
          showTile = true;
        } else {
          try {
            showTile = _action.validation!(data) ?? true;
            print('e');
          } catch (e) {
            showTile = true;
          }
        }

        if (showTile) {
          Widget res = ListTile(
            title: Row(
              children: [
                Icon(_action.icon),
                SizedBox(width: 5.0),
                Text(_action.title),
              ],
            ),
            onTap: () {
              _action.onTap!(data);
            },
          );

          _mainActions.add(res);
        }
      }
    }

    _mainActions.add(
      ListTile(
        title: new Text(
          'Cancelar',
          style: TextStyle(color: Theme.of(context).primaryColor),
        ),
        onTap: () => Navigator.pop(context),
      ),
    );

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext bc) {
        return ClipRRect(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(10),
            topLeft: Radius.circular(10),
          ),
          child: Container(
            color: Colors.white,
            child: new Wrap(
              children: _mainActions,
            ),
          ),
        );
      },
    ); //.whenComplete(() async {});

    if (widget.tableActions != null) {
      if (widget.tableActions!.onLongPress != null) {
        await widget.tableActions!.onLongPress!(data);
      }
    }
  }

  refreshTable(dynamic _dataRes) async {
    if (widget.autoRefresh ?? true) {
      int _idRow = 0;
      for (final dynamic _row in _dtData) {
        if (_row[widget.scopeRowID].toString() ==
            _dataRes[widget.scopeRowID].toString()) {
          for (final dynamic _kiKey in _dataRes.keys) {
            try {
              _dtData[_idRow][_kiKey] = _dataRes[_kiKey].toString();
            } catch (e) {
              print(e.toString());
            }
          }
        }
        _idRow += 1;
      }
      setState(() {});
    }

    if (widget.tableActions != null) {
      if (widget.tableActions!.onRowSave != null) {
        await widget.tableActions!.onRowSave!(_dataRes);
      }
    }

    int count = 0;
    Navigator.popUntil(context, (route) {
      return count++ == 1;
    });
  }

  _confirmAddDialog(dynamic rowDataValue) async {
    List<String> _titleDialog = [];
    List<dynamic> _ctrls = [];
    List<Widget> _body = [];

    createWidgetForm(DAConfigRowModel cfg, String preRowData) async {
      dynamic rowData = json.decode(preRowData);
      Widget _res = Visibility(visible: false, child: Container());

      createWidgetInput(DAInputType tipo) {
        TextEditingController _dtCfgEditTxt = new TextEditingController();
        _dtCfgEditTxt.text = rowData[cfg.scope].toString();
        _ctrls.add(_dtCfgEditTxt);
        int _ctrlIndex = _ctrls.length - 1;

        _res = Container(
          margin: EdgeInsets.only(top: 10),
          child: DAInput(
            refID: cfg.label,
            tipo: tipo,
            label: cfg.label,
            padding: 0.0,
            controller: (_ctrls[_ctrlIndex] as TextEditingController),
          ),
        );
      }

      createWidgetDropDown() {
        String _dtdropdownValue = '';
        _dtdropdownValue = rowData[cfg.scope].toString();
        _ctrls.add(_dtdropdownValue);
        int _ctrlIndex = _ctrls.length - 1;

        _res = Container(
          margin: EdgeInsets.only(top: 10),
          child: DADropdown(
            refID: cfg.editConfig!.dropdownCfg!.value,
            padding: 0,
            data: cfg.editConfig!.dropdownCfg!.data,
            controllerValue: _ctrls[_ctrlIndex],
            inputLabel: cfg.label,
            value: cfg.editConfig!.dropdownCfg!.value,
            text: cfg.editConfig!.dropdownCfg!.text,
            onChanged: (value) {
              _ctrls[_ctrlIndex] = value.toString();
            },
            onSaved: (value) {
              _ctrls[_ctrlIndex] = value.toString();
            },
          ),
        );
      }

      createWidgetCalendar() {
        DateTime _dtFEntrega = DateTime.now();
        try {
          _dtFEntrega = DateTime.parse(rowData[cfg.scope].toString());
        } catch (e) {
          print(e.toString());
        }

        _ctrls.add(_dtFEntrega);
        int _ctrlIndex = _ctrls.length - 1;

        _res = Container(
          margin: EdgeInsets.only(top: 10),
          child: DADatePicker(
            refID: cfg.label,
            label: cfg.label,
            value: (_ctrls[_ctrlIndex] as DateTime),
            padding: EdgeInsets.symmetric(horizontal: 0),
            onDateChange: (value) async {
              _ctrls[_ctrlIndex] = value;
            },
          ),
        );
      }

      createWidgetCustom() {
        _ctrls.add(null);
        try {
          _res = Container(
            margin: EdgeInsets.only(top: 10),
            child: cfg.editConfig!.customchild!,
          );
        } catch (e) {
          print(e.toString());
        }
      }

      selectWidget() {
        switch (cfg.editConfig!.tipo) {
          case DADataTableTypes.input_default:
            createWidgetInput(DAInputType.string);
            break;
          case DADataTableTypes.input_number:
            createWidgetInput(DAInputType.number);
            break;
          case DADataTableTypes.input_email:
            createWidgetInput(DAInputType.email);
            break;
          case DADataTableTypes.input_url:
            createWidgetInput(DAInputType.url);
            break;
          case DADataTableTypes.input_textarea:
            createWidgetInput(DAInputType.textarea);
            break;
          case DADataTableTypes.input_password:
            createWidgetInput(DAInputType.password);
            break;
          case DADataTableTypes.dropdown:
            createWidgetDropDown();
            break;
          case DADataTableTypes.datepicker:
            createWidgetCalendar();
            break;
          case DADataTableTypes.custom:
            createWidgetCustom();
            break;
          default:
            break;
        }
      }

      selectWidget();

      return _res;
    }

    _createBody(DAConfigRowModel column) async {
      if (column.editConfig != null) {
        DAConfigRowModelEditing? _editCfg = column.editConfig;

        if ((_editCfg!.showInTitle) ?? false) {
          _titleDialog.add(rowDataValue[column.scope].toString());
        }

        if ((_editCfg.editable) ?? true) {
          dynamic _rowData = rowDataValue;
          dynamic _widget =
              await createWidgetForm(column, json.encode(_rowData));
          if (_widget != null) {
            _body.add(_widget);
          }
        }
      }
    }

    for (final DAConfigRowModel column in widget.columnsDef) {
      await _createBody(column);
    }

    saveDialog() async {
      try {
        Map<String, dynamic> _dataRes = {};
        int _indx = 0;

        if (_ctrls.length > 0) {
          for (final DAConfigRowModel widgetConfig in widget.columnsDef) {
            if (widgetConfig.editConfig != null) {
              if (widgetConfig.editConfig!.editable != false) {
                String _val = '';
                if (_ctrls[_indx].runtimeType.toString() ==
                    'TextEditingController') {
                  _val = (_ctrls[_indx] as TextEditingController).text;
                } else {
                  _val =
                      ((_ctrls[_indx] == null) ? '' : _ctrls[_indx].toString());
                }

                _dataRes[widgetConfig.scope] = (widgetConfig.isnumeric ?? false)
                    ? double.parse(_val)
                    : _val;
                _indx += 1;
              }
            }
          }
        }

        try {
          _dataRes[widget.scopeRowID] =
              rowDataValue[widget.scopeRowID].toString();
        } catch (e) {
          print(e.toString());
        }

        await refreshTable(_dataRes);
      } catch (e) {
        DAToast(context, e.toString());
      }
    }

    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        Widget _dialog = DAInputDialog(
          title: (_titleDialog.length > 0)
              ? _titleDialog.join(', ')
              : 'Actualizar registro.',
          subtitle: "Modifique los valores requeridos.",
          okText: "Actualizar",
          onPressed: () async {
            await saveDialog();
          },
          input: _body,
        );
        return _dialog;
      },
    );
  }
}

// ignore: must_be_immutable
class DAInputDialog extends StatefulWidget {
  final String title;
  final List<Widget> input;
  final String? subtitle;
  final String? okText;
  final String? cancelText;
  final Function()? onPressed;
  final Function()? onCancelPressed;
  final bool? noCancel;
  final bool? disabledOk;
  bool? isLoading;
  Widget? extraAction;

  DAInputDialog({
    required this.title,
    required this.input,
    this.subtitle,
    this.onPressed,
    this.onCancelPressed,
    this.okText,
    this.cancelText,
    this.noCancel,
    this.disabledOk,
    this.isLoading,
    this.extraAction,
  });

  @override
  _DAInputDialog createState() => _DAInputDialog();
}

class _DAInputDialog extends State<DAInputDialog> {
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    Widget _subtitleContainer = Visibility(
      visible: (widget.subtitle != null && widget.subtitle != ''),
      child: Container(
        width: MediaQuery.of(context).size.width - 50.0,
        margin: EdgeInsets.zero,
        alignment: Alignment.centerLeft,
        child: Center(
          child: Text(
            (widget.subtitle) ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isDarkMode ? Colors.white70 : Colors.black54,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ),
      ),
    );

    Widget _elInput = (widget.input.length == 0)
        ? Visibility(
            visible: false,
            child: Container(),
          )
        : SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: widget.input,
            ),
          );

    Widget _alert = Center(
      child: SingleChildScrollView(
        child: AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
          title: Column(
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black,
                  fontSize: 18,
                ),
              ),
              SizedBox(height: 10.0),
              _subtitleContainer,
            ],
          ),
          content: _elInput,
          actions: <Widget>[
            (widget.noCancel == true)
                ? Container(width: 0, height: 0) // Evitar salto de linea
                : TextButton(
                    child: Text(
                      (widget.cancelText) ?? 'Cancelar',
                      style: TextStyle(
                          color:
                              isDarkMode ? Colors.white70 : Colors.grey[500]),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();

                      if (widget.onCancelPressed != null) {
                        widget.onCancelPressed!();
                      }
                    },
                  ),
            Visibility(
                visible: (widget.extraAction != null),
                child: (widget.extraAction) ?? Container()),
            TextButton(
              child: Text(
                (widget.okText) ?? 'Ok',
                style: TextStyle(
                  color: (widget.disabledOk ?? false)
                      ? isDarkMode
                          ? Colors.grey
                          : Colors.grey.shade800
                      : Theme.of(context).primaryColor,
                ),
              ),
              onPressed: () async {
                if (!(widget.disabledOk ?? false)) {
                  widget.isLoading = true;
                  if (mounted) {
                    setState(() {});
                  }

                  await widget.onPressed!();
                  widget.isLoading = false;

                  if (mounted) {
                    setState(() {});
                  }
                }
              },
            ),
          ],
        ),
      ),
    );

    return LoadingOverlay(
      isLoading: (widget.isLoading) ?? false,
      child: _alert,
    );
  }
}

class DASignatureR extends StatelessWidget {
  final Widget? child;
  final GlobalKey<SignatureState> key;
  final String? title;
  final String? subtitle;
  final String? okText;
  final Function()? onSign;

  DASignatureR({
    this.child,
    required this.key,
    this.title,
    this.subtitle,
    this.okText,
    this.onSign,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    Widget _signature = Signature(
      key: this.key,
      color: Colors.black,
      strokeWidth: 3.0,
      backgroundPainter: null,
    );

    getFirma() {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0)),
            title: Text((this.title) ?? 'Firma'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  width: size.width - 50.0,
                  margin: EdgeInsets.zero,
                  alignment: Alignment.centerLeft,
                  child: Text((this.subtitle) ?? 'Capture firma.'),
                ),
                SizedBox(height: 20.0),
                Container(
                  height: 350,
                  color: Colors.grey[100],
                  child: _signature,
                ),
              ],
            ),
            actions: <Widget>[
              TextButton(
                child: Text(
                  'Cancelar',
                  style: TextStyle(color: Colors.grey[500]),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              TextButton(
                child: Text(
                  'Limpiar',
                  style: TextStyle(color: Colors.grey[500]),
                ),
                onPressed: () {
                  final sign = this.key.currentState;
                  sign!.clear();
                },
              ),
              TextButton(
                child: Text(
                  (this.okText) ?? 'Ok',
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
                onPressed: this.onSign,
              ),
            ],
          );
        },
      );
    }

    Widget widgetFirma = GestureDetector(
      child: (this.child) ?? Container(),
      onTap: getFirma,
      //onTap: () => Navigator.pushNamed(context, 'firma'),
    );

    return widgetFirma;
  }
}

//Waves
class DABottomWaveContainer extends StatefulWidget {
  @override
  _DABottomWaveContainerState createState() => _DABottomWaveContainerState();
}

class _DABottomWaveContainerState extends State<DABottomWaveContainer> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    var toAnim = Container(
      margin: EdgeInsets.only(top: 0),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : Theme.of(context).primaryColor,
        ),
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade800
            : Colors.white,
      ),
    );

    var anim = SizedBox(
      height: size.height,
      child: TweenAnimationBuilder(
        child: toAnim,
        curve: Curves.elasticOut,
        tween: Tween(begin: -40.0, end: 0.0),
        duration: Duration(seconds: 8),
        builder: (context, value, child) {
          return ClipPath(
            child: child,
            clipper: DABottomWaveClipper(value: value),
          );
        },
      ),
    );

    return Stack(
      children: [
        //_mapa,
        Container(
          width: double.infinity,
          height: double.infinity,
          child: Text(''),
          decoration: BoxDecoration(
            gradient: DAGradients.primary(context),
          ),
        ),
        anim,
      ],
    );
  }
}

class DABottomWaveContainerMap extends StatefulWidget {
  @override
  _DABottomWaveContainerMapState createState() =>
      _DABottomWaveContainerMapState();
}

class _DABottomWaveContainerMapState extends State<DABottomWaveContainerMap> {
  @override
  Widget build(BuildContext context) {
    var toAnim = Container(
      margin: EdgeInsets.only(top: 0),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).primaryColor),
        gradient: DAGradients.primary(context),
      ),
    );

    var anim = SizedBox(
      height: 86,
      child: TweenAnimationBuilder(
        child: toAnim,
        curve: Curves.elasticOut,
        tween: Tween(begin: -40.0, end: 0.0),
        duration: Duration(seconds: 8),
        builder: (context, value, child) {
          return ClipPath(
            child: child,
            clipper: DABottomWaveClipperMap(value: value),
          );
        },
      ),
    );

    return Stack(
      children: [
        Container(
          margin: EdgeInsets.only(top: 40),
          height: 50,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : Colors.white,
        ),
        anim,
      ],
    );
  }
}

class DABottomWaveClipperMap extends CustomClipper<Path> {
  var value;

  DABottomWaveClipperMap({this.value}) : super();

  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, 0);

    var firstControlPoint = Offset(size.width * 0.33, 80);
    var firstEndPoint = Offset(size.width, 0);

    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );

    path.lineTo(size.width, 80);

    var secondControlPoint = Offset(size.width * 0.3, 100);
    var secondEndPoint = Offset(0.0, 50);

    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );

    path.lineTo(0.0, 200);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    //return oldClipper != this;
    return oldClipper is DABottomWaveClipper && value != oldClipper.value;
  }
}

class DABottomWaveClipper extends CustomClipper<Path> {
  var value;

  DABottomWaveClipper({this.value}) : super();

  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0.0, size.height * 0.25);

    var firstControlPoint = Offset(
      size.width * 0.33,
      (size.height * 0.4) - value,
    );

    var firstEndPoint = Offset(size.width * 0.55, size.height * 0.2);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );

    var secondControlPoint = Offset(
      size.width * 0.75,
      (size.height * 0.05) + value,
    );

    var secondEndPoint = Offset(size.width * 1, size.height * 0.1);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );

    path.lineTo(size.width, size.height);
    //path.lineTo(size.width, 0.0);
    path.lineTo(0.0, size.height);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    //return oldClipper != this;
    return oldClipper is DABottomWaveClipper && value != oldClipper.value;
  }

  // @override
  // bool shouldReclip(CustomClipper<Path> oldClipper) =>
  //     oldClipper is BottomWaveClipper && value != oldClipper.value;
}

// ignore: must_be_immutable
class DASignature extends StatefulWidget {
  final GlobalKey<SignatureState> key;
  final String defPngImage;
  final String? title;
  late String? modelSign;
  final Widget? child;
  final String? subtitle;
  final String? okText;
  final double? width;
  final double? height;
  final BorderRadiusGeometry? borderRadius;
  final EdgeInsetsGeometry? padding;
  final FormFieldSetter<SignedImageRes>? onSign;

  DASignature({
    required this.key,
    required this.defPngImage,
    this.title,
    this.modelSign,
    this.child,
    this.subtitle,
    this.okText,
    this.width,
    this.height,
    this.borderRadius,
    this.padding,
    this.onSign,
  });

  @override
  _DASignature createState() => _DASignature();
}

class _DASignature extends State<DASignature> {
  var controllerValue;

  @override
  Widget build(BuildContext context) {
    try {
      //final size = MediaQuery.of(context).size;
      double _radius = 10.0;

      ImageProvider _getImage() {
        try {
          String? signImage = widget.modelSign;
          if (signImage != null) {
            final Uint8List resAvatar = Base64Codec().decode(
                signImage.replaceAll(
                    signImage.substring(0, signImage.indexOf(',') + 1), ""));
            ImageProvider res =
                Image.memory(resAvatar, fit: BoxFit.cover).image;
            return res;
          } else {
            return AssetImage('assets/system/' + widget.defPngImage);
          }
        } catch (e) {
          return AssetImage('assets/system/' + widget.defPngImage);
        }
      }

      template() {
        ImageProvider _image = _getImage();

        Widget _box = Container(
          decoration: BoxDecoration(
            borderRadius:
                (widget.borderRadius) ?? BorderRadius.circular(_radius),
            image: DecorationImage(
              fit: BoxFit.cover,
              image: _image,
            ),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius:
                  (widget.borderRadius) ?? BorderRadius.circular(_radius),
              gradient: LinearGradient(begin: Alignment.bottomRight, stops: [
                0.3,
                0.9
              ], colors: [
                Colors.black.withValues(alpha: .3),
                Colors.black.withValues(alpha: .05)
              ]),
            ),
            child: Align(
              child: Padding(
                padding: const EdgeInsets.all(15.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(),
                  ],
                ),
              ),
            ),
          ),
        );

        double _height = (widget.height) ?? 60.0;
        double _width = (widget.width) ?? double.infinity;

        Widget _template = Container(
          height: (_height < 60.0) ? 60.0 : _height,
          width: (_width < 60.0) ? double.infinity : _width,
          decoration: BoxDecoration(
            borderRadius:
                (widget.borderRadius) ?? BorderRadius.circular(_radius),
          ),
          child: _box,
        );

        Widget _position = Container(
          margin: (widget.padding) ??
              EdgeInsets.symmetric(
                horizontal: 20.0,
                vertical: 10.0,
              ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                (widget.title) ?? 'Firma',
                style: TextStyle(fontSize: 12),
              ),
              SizedBox(height: 10.0),
              _template,
            ],
          ),
        );

        return _position;
      }

      onSign() async {
        final sign = widget.key.currentState;
        final image = await sign!.getData();

        var data = await image.toByteData(format: ui.ImageByteFormat.png);
        final encoded = base64.encode(data!.buffer.asUint8List());

        SignedImageRes signRes = SignedImageRes(
          imageStr: encoded,
          bytesImagen: Base64Codec().decode(encoded.replaceAll(
              encoded.substring(0, encoded.indexOf(',') + 1), "")),
        );

        widget.modelSign = encoded;
        sign.clear();
        Navigator.of(context).pop();

        widget.onSign!(signRes);
        setState(() {});
      }

      dialogSignature() {
        Widget _cleanBtn = TextButton(
          child: Text(
            'Limpiar',
            style: TextStyle(color: Colors.grey[500]),
          ),
          onPressed: () {
            final sign = widget.key.currentState;
            sign!.clear();
          },
        );

        Widget _signature = Container(
          height: 350,
          color: Colors.grey[100],
          child: Signature(
            key: widget.key,
            color: Colors.black,
            strokeWidth: 3.0,
            backgroundPainter: null,
          ),
        );

        return showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            Widget _dialog = DAInputDialog(
              title: (widget.title) ?? 'Firma',
              subtitle: widget.subtitle,
              extraAction: _cleanBtn,
              okText: widget.okText,
              input: [_signature],
              onPressed: () async {
                await onSign();
              },
            );
            return _dialog;
          },
        );
      }

      return GestureDetector(
        child: template(),
        onTap: dialogSignature,
      );
    } catch (e) {
      print('DASignature: ' + e.toString());
      return Container();
    }
  }
}

class DAImageViewer {
  static Future<void> show({
    required BuildContext context,
    required String image64,
  }) async {
    try {
      showGeneralDialog(
        context: context,
        barrierColor: Colors.black12.withValues(alpha: 0.82),
        transitionDuration: Duration(milliseconds: 400),
        pageBuilder: (_, __, ___) {
          return SafeArea(
            child: Stack(
              children: <Widget>[
                SizedBox.expand(
                  child: Container(
                    padding: EdgeInsets.all(20.0),
                    child: InteractiveViewer(
                      child: Image(
                        image: DAController.getImageFromBase64(
                          image64: image64,
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: 30.0),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: GestureDetector(
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 32.0,
                      ),
                      onTap: () {
                        Navigator.pop(context);
                      },
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    } on PlatformException catch (e) {
      print(e.message);
    }
  }

  static Future<void> showMultiple({
    required BuildContext context,
    required List<String> images64,
    int startingPosition = 0,
    bool withInteractive = false,
  }) async {
    try {
      Widget _imgExpanded = Expanded(
        child: Container(
          padding: EdgeInsets.all(20.0),
          child: Image(
            image: DAController.getImageFromBase64(
              image64: images64[startingPosition],
            ),
          ),
        ),
      );

      Widget _swipe = GestureDetector(
        onPanUpdate: (details) {
          // Swipe Right
          if (details.delta.dx > 0) {
            if (startingPosition > 0) {
              Navigator.pop(context);
              DAImageViewer.showMultiple(
                context: context,
                images64: images64,
                startingPosition: startingPosition - 1,
                withInteractive: withInteractive,
              );
            }
          }
          // Swipe Left
          if (details.delta.dx < 0) {
            if (startingPosition < (images64.length - 1)) {
              Navigator.pop(context);
              DAImageViewer.showMultiple(
                context: context,
                images64: images64,
                startingPosition: startingPosition + 1,
                withInteractive: withInteractive,
              );
            }
          }
        },
        child: _imgExpanded,
      );

      Widget _interactive = InteractiveViewer(
        child: _imgExpanded,
      );

      Widget _showImg = (withInteractive == false) ? _swipe : _interactive;

      showGeneralDialog(
        context: context,
        barrierColor: Colors.black12.withValues(alpha: 0.82),
        transitionDuration: Duration(milliseconds: 400),
        pageBuilder: (_, __, ___) {
          return Stack(
            children: <Widget>[
              SizedBox.expand(
                child: _showImg,
              ),
              Container(
                margin: EdgeInsets.only(bottom: 30.0, left: 30.0),
                child: Align(
                  alignment: Alignment.bottomLeft,
                  child: Visibility(
                    visible: startingPosition > 0,
                    child: GestureDetector(
                      child: Container(
                        height: 50.0,
                        width: 50.0,
                        child: Icon(
                          Icons.arrow_left,
                          color: Colors.white,
                          size: 32.0,
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        DAImageViewer.showMultiple(
                          context: context,
                          images64: images64,
                          startingPosition: startingPosition - 1,
                          withInteractive: withInteractive,
                        );
                      },
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(bottom: 30.0, right: 30.0),
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: Visibility(
                    visible: startingPosition < (images64.length - 1),
                    child: GestureDetector(
                      child: Container(
                        height: 50.0,
                        width: 50.0,
                        child: Icon(
                          Icons.arrow_right,
                          color: Colors.white,
                          size: 32.0,
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        DAImageViewer.showMultiple(
                          context: context,
                          images64: images64,
                          startingPosition: startingPosition + 1,
                          withInteractive: withInteractive,
                        );
                      },
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(bottom: 30.0),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: GestureDetector(
                    child: Container(
                      height: 50.0,
                      width: 50.0,
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 32.0,
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            ],
          );
        },
      );
    } on PlatformException catch (e) {
      print(e.message);
    }
  }
}

// ignore: must_be_immutable
class DaImageUpload extends StatefulWidget {
  List<String>? childrenBase64;
  final String? label;
  late int? limit;
  final EdgeInsetsGeometry? padding;
  final bool? lockCam;
  final bool? lockGallery;
  final double? maxWidth;
  final double? maxHeight;

  DaImageUpload({
    this.childrenBase64,
    this.label,
    this.limit,
    this.padding,
    this.lockCam,
    this.lockGallery,
    this.maxWidth,
    this.maxHeight,
  });

  List<String> get resImages {
    _DaImageUpload _prov = _DaImageUpload();
    return _prov.images;
  }

  @override
  _DaImageUpload createState() => _DaImageUpload();
}

class _DaImageUpload extends State<DaImageUpload> {
  var controllerValue;
  //List<String> _imgWidget = [];

  @override
  Widget build(BuildContext context) {
    try {
      // Seteamos valores default
      widget.limit = (widget.limit) ?? 99;

      widget.childrenBase64 = (widget.childrenBase64) ?? [];
      if (widget.childrenBase64!.length > 0 &&
          widget.childrenBase64!.length > widget.limit!) {
        widget.childrenBase64 = widget.childrenBase64!.sublist(0, widget.limit);
      }

      int _itemCount = (widget.childrenBase64!.length < widget.limit!)
          ? widget.childrenBase64!.length
          : widget.limit!;

      final ImagePicker _picker = new ImagePicker();
      double tumbSize = (((MediaQuery.of(context).size.width) - 40.0) / 4);

      Widget _addImg = Opacity(
        opacity: 0.7,
        child: Container(
          color: Color.fromRGBO(242, 243, 245, 5.0),
          padding: EdgeInsets.all(20.0),
          child: SizedBox(
            height: tumbSize - 40.0,
            width: tumbSize - 40.0,
            child: FittedBox(
                fit: BoxFit.fill,
                child: SvgPicture.asset(
                  'assets/icon/addImg.svg',
                  colorFilter: ColorFilter.mode(
                      Theme.of(context).primaryColor, BlendMode.srcIn),
                )
                //color: Theme.of(context).primaryColor),
                ),
          ),
        ),
      );

      _getGallery() async {
        {
          final List<XFile>? _pickImages = await _picker.pickMultiImage();

          _pickImages!.forEach((XFile image) async {
            final XFile? pickedFile = image;
            Uint8List bytesNuevaImagen;

            if (pickedFile != null) {
              bytesNuevaImagen = await pickedFile.readAsBytes();
              if ((widget.childrenBase64!.length) < ((widget.limit) ?? 99)) {
                String cadenaNuevaImagen = base64.encode(bytesNuevaImagen);
                widget.childrenBase64!.add(cadenaNuevaImagen);
              }
            }
          });

          setState(() {});
        }
      }

      _getCamera() async {
        final XFile? image = await _picker.pickImage(
          source: ImageSource.camera,
          maxHeight: widget.maxHeight,
          maxWidth: widget.maxWidth,
        );
        final XFile? pickedFile = image;
        Uint8List bytesNuevaImagen;

        if (pickedFile != null) {
          bytesNuevaImagen = await pickedFile.readAsBytes();
          String cadenaNuevaImagen = base64.encode(bytesNuevaImagen);
          widget.childrenBase64!.add(cadenaNuevaImagen);
        }

        setState(() {});
      }

      _addImageSource() async {
        showModalBottomSheet(
          context: context,
          backgroundColor: Colors.transparent,
          builder: (BuildContext bc) {
            return ClipRRect(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(10),
                topLeft: Radius.circular(10),
              ),
              child: Container(
                color: Colors.white,
                child: new Wrap(
                  children: [
                    ListTile(
                      title: new Text(
                        'Seleccione el origén de la imagen',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                    ListTile(
                      title: Row(
                        children: [
                          Icon(Icons.image),
                          SizedBox(width: 5.0),
                          Text('Galería')
                        ],
                      ),
                      onTap: () {
                        _getGallery();
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      title: Row(
                        children: [
                          Icon(Icons.camera_alt),
                          SizedBox(width: 5.0),
                          Text('Cámara')
                        ],
                      ),
                      onTap: () {
                        _getCamera();
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      title: new Text(
                        'Cancelar',
                        style: TextStyle(color: Theme.of(context).primaryColor),
                      ),
                      onTap: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }

      Widget _addImage() {
        return GestureDetector(
          child: _addImg,
          onTap: () async {
            // Both
            if (!((widget.lockCam) ?? false) &&
                !((widget.lockGallery) ?? false)) {
              await _addImageSource();
            }
            // Only Camera
            else if (!((widget.lockCam) ?? false) &&
                ((widget.lockGallery) ?? false)) {
              await _getCamera();
            }
            // Only Gallery
            else if (((widget.lockCam) ?? false) &&
                !((widget.lockGallery) ?? false)) {
              await _getGallery();
            }
          },
        );
      }

      _deleteImage(int index) async {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            Widget _dialog = DAInputDialog(
              title: '¿Desea eliminar la imagen?',
              subtitle: "Esta acción no se podrá revertir.",
              okText: "Eliminar",
              onPressed: () async {
                int count = 0;
                Navigator.popUntil(context, (route) {
                  return count++ == 2;
                });
                widget.childrenBase64!.removeAt(index);
                setState(() {});
                DAToast(context, "Imagen eliminada.");
              },
              input: [],
            );
            return _dialog;
          },
        );
      }

      void _imgLongPress(int index) {
        showModalBottomSheet(
          context: context,
          backgroundColor: Colors.transparent,
          builder: (BuildContext bc) {
            return ClipRRect(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(10),
                topLeft: Radius.circular(10),
              ),
              child: Container(
                color: Colors.white,
                child: new Wrap(
                  children: [
                    ListTile(
                      title: new Text(
                        'Seleccione la acción a ejecutar',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                    ListTile(
                      title: Row(
                        children: [
                          Icon(Icons.delete),
                          SizedBox(width: 5.0),
                          Text('Eliminar imagen')
                        ],
                      ),
                      onTap: () async {
                        if (!((widget.lockCam) ?? false) ||
                            !((widget.lockGallery) ?? false)) {
                          await _deleteImage(index);
                        }
                      },
                    ),
                    ListTile(
                      title: new Text(
                        'Cancelar',
                        style: TextStyle(color: Theme.of(context).primaryColor),
                      ),
                      onTap: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }

      Widget list = Container(
        child: GridView.builder(
          padding: EdgeInsets.only(top: 10.0),
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: new SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            childAspectRatio: 1,
          ),
          itemCount: _itemCount + 1,
          itemBuilder: (BuildContext context, int index) {
            if (_itemCount == index) {
              if (_itemCount < ((widget.limit) ?? 1)) {
                return _addImage();
              } else {
                return Visibility(visible: false, child: Container());
              }
            }

            Widget img = Container(
              color: Color.fromRGBO(242, 243, 245, 1.0),
              child: SizedBox(
                height: tumbSize,
                width: tumbSize,
                child: FittedBox(
                  fit: BoxFit.fill,
                  child: Image(
                    image: DAController.getImageFromBase64(
                      image64: widget.childrenBase64![index],
                    ),
                  ),
                ),
              ),
            );

            return GestureDetector(
              child: img,
              onLongPress: () {
                _imgLongPress(index);
              },
              onTap: () {
                if (widget.childrenBase64!.length == 1) {
                  DAImageViewer.show(
                    context: context,
                    image64: widget.childrenBase64![index],
                  );
                } else {
                  DAImageViewer.showMultiple(
                    context: context,
                    images64: widget.childrenBase64!,
                    startingPosition: index,
                    withInteractive: true,
                  );
                }
              },
            );
          },
          shrinkWrap: true,
        ),
      );

      Widget _wrap = Container(
        padding: (widget.padding) ??
            EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 0.0,
            ),
        child: Wrap(
          children: [
            Container(
              child: Text(
                (widget.label) ?? 'Imágenes',
                style: TextStyle(fontSize: 12),
              ),
            ),
            list,
          ],
        ),
      );

      return _wrap;
    } catch (e) {
      print('DaImageUpload: ' + e.toString());
      return Container();
    }
  }

  List<String> get images {
    return widget.childrenBase64!;
  }
}

// ignore: must_be_immutable
class DaMapList extends StatefulWidget {
  final DALayoutListMapModel? config;
  final List<dynamic> data;
  LatLng? target;
  final FormFieldSetter<dynamic>? onCardTap;

  DaMapList({
    this.config,
    required this.data,
    this.target,
    this.onCardTap,
  });

  @override
  _DaMapList createState() => _DaMapList();
}

class _DaMapList extends State<DaMapList> {
  late GoogleMapController _mapController;
  PageController _pageController =
      new PageController(initialPage: 1, viewportFraction: 0.8);
  int prevPage = 1;
  List<Marker> allMarkers = [];
  List<dynamic> dataMap = [];

  @override
  Widget build(BuildContext context) {
    try {
      Widget setCurrent = Container(
        margin: EdgeInsets.only(top: 15.0),
        child: IconButton(
          icon: Icon(
            Icons.location_searching,
            color: Colors.black,
          ),
          onPressed: () async {
            await moveCurrent();
          },
        ),
      );

      Widget _daBackButton = (widget.config!.hasBackButton == true)
          ? DABackButton(title: widget.config!.title)
          : Container();

      Widget list = CustomScrollView(
        slivers: <Widget>[
          DATitleMin(
            title: widget.config!.hasBackButton == true
                ? ""
                : widget.config!.title.toString(),
            aux: setCurrent,
          ),
        ],
      );

      Widget pageMap = Stack(
        children: [
          list,
          (creaMapa()) ?? Container(),
          (tarjetasMapa()) ?? Container(),
          _daBackButton,
        ],
      );

      if (widget.data.length > 0) {
        moveCamera();
      } else {
        moveCurrent();
      }

      return pageMap;
    } catch (e) {
      print('DaMapList: ' + e.toString());
      return Container();
    }
  }

  // Tarjetas
  tarjetasMapa() {
    return DACardPageList(
      config: widget.config!.cardConfig,
      data: widget.data,
      onTap: widget.onCardTap,
      animationController: _pageController,
    );
  }

  // Mapa
  moveCamera() {
    _mapController.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
            target: allMarkers[_pageController.page!.toInt()].position,
            zoom: 15.0,
            bearing: 45.0,
            tilt: 45.0),
      ),
    );
  }

  onScroll() {
    if (_pageController.page!.toInt() != prevPage) {
      prevPage = _pageController.page!.toInt();
      moveCamera();
    }
  }

  loadMapMarkers() async {
    try {
      allMarkers.clear();
      dataMap.clear();

      widget.data.forEach((element) {
        try {
          LatLng _pos = new LatLng(
            double.parse(
                element[widget.config!.cardConfig.latitude].toString()),
            double.parse(
                element[widget.config!.cardConfig.longitude].toString()),
          );
          allMarkers.add(
            Marker(
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueOrange),
              markerId:
                  MarkerId(element[widget.config!.cardConfig.id].toString()),
              draggable: false,
              infoWindow: InfoWindow(
                  title: element[widget.config!.cardConfig.title].toString(),
                  snippet:
                      element[widget.config!.cardConfig.subtitle].toString(),
                  onTap: () async {
                    //await _detEmbarque(element);
                  }),
              position: _pos,
            ),
          );
          dataMap.add(element);
        } catch (e) {
          print(e.toString());
        }
      });

      _pageController.addListener(onScroll);
    } catch (e) {
      DAToast(context, e.toString());
    }
  }

  creaMapa() {
    try {
      if (widget.data.length > 0) {
        loadMapMarkers();
      }

      LatLng _target = (widget.target) ?? new LatLng(0.0, 0.0);
      if (allMarkers.length == 1) {
        _target = allMarkers[0].position;
      } else if (allMarkers.length > 1) {
        _target = allMarkers[1].position;
      }

      // Se obtiene la altura de la barra de estado y la app bar para calcular el margen
      double statusBarHeight = MediaQuery.of(context).padding.top;
      double appBarHeight = kToolbarHeight;

      Widget _map = Container(
        height: MediaQuery.of(context).size.height - 130.0,
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(top: statusBarHeight + appBarHeight),
        child: new GoogleMap(
          mapType: MapType.normal,
          initialCameraPosition: CameraPosition(
            target: _target,
            zoom: 15.0,
          ),
          markers: Set.from(allMarkers),
          zoomControlsEnabled: false,
          myLocationButtonEnabled: false,
          myLocationEnabled: true,
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
          },
        ),
      );

      return _map;
    } catch (e) {
      DAToast(context, 'creaMapa: ' + e.toString());
    }
  }

  // Encabezado
  moveCurrent() async {
    // Register my position
    Position currPosition = await DAController.getCurrentLocation();

    _mapController.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
            target: LatLng(currPosition.latitude, currPosition.longitude),
            zoom: 16.0,
            bearing: 45.0,
            tilt: 45.0),
      ),
    );
  }
}

// ignore: must_be_immutable
class DaMapView extends StatefulWidget {
  String title;
  LatLng? target;
  bool? hasBackButton;
  Widget child;

  DaMapView({
    required this.title,
    required this.child,
    this.target,
    this.hasBackButton,
  });

  @override
  _DaMapView createState() => _DaMapView();
}

class _DaMapView extends State<DaMapView> {
  late GoogleMapController _mapController;

  @override
  Widget build(BuildContext context) {
    try {
      Widget setCurrent = Container(
        margin: EdgeInsets.only(top: 15.0),
        child: IconButton(
          icon: Icon(
            Icons.location_searching,
            color: Colors.black,
          ),
          onPressed: () async {
            await moveCurrent();
          },
        ),
      );

      Widget _daBackButton = (widget.hasBackButton == true)
          ? DABackButton(title: widget.title)
          : Container();

      Widget list = CustomScrollView(
        slivers: <Widget>[
          DATitleMin(
            title: widget.title,
            aux: setCurrent,
          ),
        ],
      );

      Widget pageMap = Stack(
        children: [
          list,
          (creaMapa()) ?? Container(),
          widget.child,
          _daBackButton,
        ],
      );

      return pageMap;
    } catch (e) {
      print('DaMapList: ' + e.toString());
      return Container();
    }
  }

  // Mapa
  creaMapa() {
    try {
      LatLng _target = (widget.target) ?? new LatLng(0.0, 0.0);

      Widget _map = Container(
        height: MediaQuery.of(context).size.height - 130.0,
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(top: 90.0),
        child: new GoogleMap(
          mapType: MapType.normal,
          initialCameraPosition: CameraPosition(
            target: _target,
            zoom: 15.0,
          ),
          zoomControlsEnabled: false,
          myLocationButtonEnabled: false,
          markers: Set.from([]),
          myLocationEnabled: true,
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
          },
        ),
      );

      return _map;
    } catch (e) {
      DAToast(context, 'creaMapa: ' + e.toString());
    }
  }

  // Encabezado
  moveCurrent() async {
    // Register my position
    Position currPosition = await DAController.getCurrentLocation();

    _mapController.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
            target: LatLng(currPosition.latitude, currPosition.longitude),
            zoom: 16.0,
            bearing: 45.0,
            tilt: 45.0),
      ),
    );
  }
}

class DASearchDelegate extends SearchDelegate {
  DARequestModel reqSource;
  DAFinderCardModel configTiles;
  String? keyFinderID;
  String? searchLabel;
  String? noDataLabel;
  bool? scannerFinder;
  String? specificSearch;
  String? rowFilter;
  int? minLengthQuery;
  Function(dynamic)? onFinderTap;

  DASearchDelegate({
    required this.reqSource,
    required this.configTiles,
    this.keyFinderID,
    this.searchLabel,
    this.noDataLabel,
    this.scannerFinder,
    this.specificSearch,
    this.rowFilter,
    this.minLengthQuery,
    this.onFinderTap,
  });

  String _query = '';
  bool _tapLoading = false;

  @override
  String? get searchFieldLabel => this.searchLabel;

  @override
  List<Widget> buildActions(BuildContext context) {
    List<Widget> _actionsList = [];
    _actionsList.add(
      IconButton(icon: Icon(Icons.clear), onPressed: () => query = ''),
    );

    if ((this.scannerFinder) ?? false) {
      _actionsList.add(IconButton(
        icon: Icon(Icons.qr_code_scanner_outlined),
        onPressed: () async {
          query = await DAController.scanQR(context);
          DASearchDelegateProvider _searchProv = DASearchDelegateProvider();
          _searchProv.notify();
        },
      ));
    }

    return _actionsList;
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.arrow_back_ios),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return showResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _resultsBuilder(context);
  }

  Widget _resultsBuilder(context) {
    return showResults(context);
  }

  Widget showResults(BuildContext context) {
    if (query.length < (this.minLengthQuery ?? 3)) {
      return DANoData(tipo: this.noDataLabel);
    }

    final _searchProv = Provider.of<DASearchDelegateProvider>(context);

    if (!_searchProv.downloading && _query != query) {
      downloadData(context);
      return LoadingOverlay(
        isLoading: true,
        child: DANoData(
          tipo: 'Buscando...',
          replaceAll: true,
        ),
      );
      // ignore: dead_code
    } else if (_tapLoading) {
      return LoadingOverlay(
        isLoading: true,
        child: DANoData(
          tipo: 'Buscando...',
          replaceAll: true,
        ),
      );
    } else {
      if (this.specificSearch != null) {
        var _dataListFiltro = _searchProv.finderData
            .where((val) =>
                val[this.specificSearch].toString().toUpperCase() ==
                query.toUpperCase())
            .toList();

        _searchProv.finderData = _dataListFiltro;
      } else if (this.rowFilter != null) {
        var _dataListFiltro = _searchProv.finderData
            .where((val) => val[this.rowFilter]
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();

        _searchProv.finderData = _dataListFiltro;
      } else {
        var _dataListFiltro = _searchProv.finderData
            .where((val) => json
                .encode(val)
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();

        _searchProv.finderData = _dataListFiltro;
      }

      if (_searchProv.finderData.length == 0) {
        _searchProv.downloading = false;
        return DANoData(tipo: this.noDataLabel);
      }

      double _sWidth = MediaQuery.of(context).size.width;
      Widget list = ListView.builder(
        padding: EdgeInsets.all(0.0),
        physics: NeverScrollableScrollPhysics(),
        itemCount: (_searchProv.finderData == null)
            ? 0
            : _searchProv.finderData.length,
        itemBuilder: (BuildContext context, int index) {
          List<Widget> _rows = [];
          int _rowsLength = (this.configTiles.moreLabels == null)
              ? 0
              : this.configTiles.moreLabels!.length;

          if (_rowsLength > 0) {
            this.configTiles.moreLabels!.forEach((String element) {
              _rows.add(
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    element +
                        ": " +
                        _searchProv.finderData[index][element].toString(),
                    style: TextStyle(color: Colors.black45),
                  ),
                  width: ((_sWidth / ((_rowsLength == 0) ? 1 : _rowsLength)) <
                          100)
                      ? 100
                      : (_sWidth / ((_rowsLength == 0) ? 1 : _rowsLength)) - 16,
                ),
              );
            });
          }

          dynamic finderData = _searchProv.finderData[index];
          Widget _cards = DAFinderCard(
              metadata: jsonEncode(finderData),
              title: (finderData[this.configTiles.title]) ?? "Buscando...",
              bodyColumn: <Widget>[
                Container(
                  width: _sWidth,
                  height: 20,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: _rows,
                      ),
                    ],
                  ),
                ),
              ],
              leading: finderData[this.configTiles.leading],
              trailing: finderData[this.configTiles.trailing],
              icon: finderData[this.configTiles.icon],
              onTap: (finderData) async {
                _tapLoading = true;
                _searchProv.notify();
                await this.onFinderTap!(jsonDecode(finderData));
                _tapLoading = false;
                _searchProv.notify();
              });

          return _cards;
        },
        shrinkWrap: true,
      );

      _searchProv.downloading = false;
      return SingleChildScrollView(child: list);
    }
  }

  Map<String, dynamic> fixRequest(Map<String, dynamic>? bodyReq) {
    String varSp = jsonEncode(bodyReq)
        .replaceAll('<Finder>', query)
        .replaceAll('<ID>', (this.keyFinderID) ?? '');
    return jsonDecode(varSp);
  }

  void downloadData(BuildContext context) async {
    try {
      final Map<String, dynamic> _bodyReq = fixRequest(this.reqSource.bodyReq);
      DASearchDelegateProvider _searchProv = DASearchDelegateProvider();
      _searchProv.downloading = true;

      dynamic _data =
          await DAMainLoadingProvider.execAPI(this.reqSource.uriReq, _bodyReq);

      _searchProv.finderData = _data;
      //_searchProv.setFiltro(_data, query);
      _query = query.toString();
      _searchProv.notify();
    } catch (e) {
      DAToast(context, e.toString());
    }
  }
}

class DASearchDelegateOffline extends SearchDelegate {
  List<dynamic> dataSource;
  DAFinderCardModel configTiles;
  String? keyFinderID;
  String? searchLabel;
  String? noDataLabel;
  bool? scannerFinder;
  String? specificSearch;
  String? rowFilter;
  int? minLengthQuery;
  Function(dynamic)? onFinderTap;
  bool? closeOnTap;
  int? awaitFinder;
  bool? showAllAtStart;
  bool? showAllBtn;

  DASearchDelegateOffline({
    required this.dataSource,
    required this.configTiles,
    this.keyFinderID,
    this.searchLabel,
    this.noDataLabel,
    this.scannerFinder,
    this.specificSearch,
    this.rowFilter,
    this.minLengthQuery,
    this.onFinderTap,
    this.closeOnTap,
    this.awaitFinder,
    this.showAllAtStart,
    this.showAllBtn,
  });

  String _query = '';
  bool _tapLoading = false;
  bool _firstTime = true;

  @override
  String? get searchFieldLabel => this.searchLabel;

  @override
  List<Widget> buildActions(BuildContext context) {
    List<Widget> _actionsList = [];

    if ((this.showAllBtn) ?? false) {
      _actionsList.add(
        IconButton(icon: Icon(Icons.list), onPressed: () => query = '*'),
      );
    }

    _actionsList.add(
      IconButton(icon: Icon(Icons.clear), onPressed: () => query = ''),
    );

    if ((this.scannerFinder) ?? false) {
      _actionsList.add(IconButton(
        icon: Icon(Icons.qr_code_scanner_outlined),
        onPressed: () async {
          FocusScope.of(context).unfocus();
          query = await DAController.scanQR(context);
          DASearchDelegateProvider _searchProv = DASearchDelegateProvider();
          _searchProv.notify();
        },
      ));
    }

    return _actionsList;
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.arrow_back_ios),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return showResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _resultsBuilder(context);
  }

  Widget _resultsBuilder(context) {
    return showResults(context);
  }

  Widget showResults(BuildContext context) {
    if (_firstTime) {
      if ((this.showAllAtStart) ?? false) {
        query = '*';
      }

      _firstTime = false;
    }

    if (query.length < (this.minLengthQuery ?? 3) && query.trim() != '*') {
      return DANoData(tipo: this.noDataLabel);
    }

    final _searchProv = Provider.of<DASearchDelegateProvider>(context);

    if (_searchProv.downloading) {
      return LoadingOverlay(
        isLoading: true,
        child: DANoData(
          tipo: 'Buscando...',
          replaceAll: true,
        ),
      );
    }

    if (!_searchProv.downloading && _query != query) {
      downloadData(context);
      return LoadingOverlay(
        isLoading: true,
        child: DANoData(
          tipo: 'Buscando...',
          replaceAll: true,
        ),
      );
      // ignore: dead_code
    } else if (_tapLoading) {
      return LoadingOverlay(
        isLoading: true,
        child: DANoData(
          tipo: 'Buscando...',
          replaceAll: true,
        ),
      );
    } else {
      if (query.trim() == '*') {
        var _dataListFiltro = _searchProv.finderData.toList();

        _searchProv.finderData = _dataListFiltro;
      } else if (this.specificSearch != null) {
        var _dataListFiltro = _searchProv.finderData
            .where((val) =>
                val[this.specificSearch].toString().toUpperCase() ==
                query.toUpperCase())
            .toList();

        _searchProv.finderData = _dataListFiltro;
      } else if (this.rowFilter != null) {
        var _dataListFiltro = _searchProv.finderData
            .where((val) => val[this.rowFilter]
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();

        _searchProv.finderData = _dataListFiltro;
      } else {
        var _dataListFiltro = _searchProv.finderData
            .where((val) => json
                .encode(val)
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();

        _searchProv.finderData = _dataListFiltro;
      }

      if (_searchProv.finderData.length == 0) {
        _searchProv.downloading = false;
        return DANoData(tipo: this.noDataLabel);
      }

      double _sWidth = MediaQuery.of(context).size.width;
      Widget list = ListView.builder(
        padding: EdgeInsets.all(0.0),
        physics: NeverScrollableScrollPhysics(),
        itemCount: (_searchProv.finderData == null)
            ? 0
            : _searchProv.finderData.length,
        itemBuilder: (BuildContext context, int index) {
          List<Widget> _rows = [];
          int _rowsLength = (this.configTiles.moreLabels == null)
              ? 0
              : this.configTiles.moreLabels!.length;

          if (_rowsLength > 0) {
            this.configTiles.moreLabels!.forEach((String element) {
              _rows.add(
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    element +
                        ": " +
                        _searchProv.finderData[index][element].toString(),
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Colors.black45,
                      fontSize: 12,
                    ),
                  ),
                  width: ((_sWidth / ((_rowsLength == 0) ? 1 : _rowsLength)) <
                          100)
                      ? 100
                      : (_sWidth / ((_rowsLength == 0) ? 1 : _rowsLength)) - 16,
                ),
              );
            });
          }

          dynamic finderData = _searchProv.finderData[index];
          Widget _cards = DAFinderCard(
              metadata: jsonEncode(finderData),
              title: (finderData[this.configTiles.title]) ?? "Buscando...",
              bodyColumn: <Widget>[
                Container(
                  width: _sWidth,
                  height: 20,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: _rows,
                      ),
                    ],
                  ),
                ),
              ],
              leading: finderData[this.configTiles.leading],
              trailing: finderData[this.configTiles.trailing],
              icon: finderData[this.configTiles.icon],
              onTap: (finderData) async {
                _tapLoading = true;
                _searchProv.notify();
                FocusScope.of(context).unfocus();
                await this.onFinderTap!(jsonDecode(finderData));
                if (this.closeOnTap ?? false) {
                  close(context, null);
                }

                _tapLoading = false;
                _searchProv.notify();
              });

          return _cards;
        },
        shrinkWrap: true,
      );

      _searchProv.downloading = false;
      return SingleChildScrollView(child: list);
    }
  }

  void downloadData(BuildContext context) async {
    try {
      DASearchDelegateProvider _searchProv = DASearchDelegateProvider();
      _searchProv.downloading = true;
      _searchProv.finderData = this.dataSource;

      Duration _duration = Duration(milliseconds: this.awaitFinder ?? 2500);
      await Future.delayed(_duration);

      _searchProv.downloading = false;
      _query = query.toString();
      _searchProv.notify();
    } catch (e) {
      DAToast(context, e.toString());
    }
  }
}

class DASearchDelegateOfflineMultiple extends SearchDelegate {
  List<dynamic> dataSource;
  List<dynamic> dataSourceSelected;
  List<dynamic>? dataOriginal;
  DAFinderCardModel configTiles;
  String? keyFinderID;
  String? searchLabel;
  String? noDataLabel;
  bool? scannerFinder;
  String? specificSearch;
  List<String>? rowFilters;
  int? minLengthQuery;
  Function(dynamic)? onFinderTap;
  Function(dynamic)? onDoneTap;
  bool? closeOnTap;
  int? awaitFinder;
  bool? showAllAtStart;
  bool? showAllBtn;

  DASearchDelegateOfflineMultiple({
    required this.dataSource,
    required this.dataSourceSelected,
    this.dataOriginal,
    required this.configTiles,
    this.keyFinderID,
    this.searchLabel,
    this.noDataLabel,
    this.scannerFinder,
    this.specificSearch,
    this.rowFilters,
    this.minLengthQuery,
    this.onFinderTap,
    this.onDoneTap,
    this.closeOnTap,
    this.awaitFinder,
    this.showAllAtStart,
    this.showAllBtn,
  });

  String _query = '';
  bool _tapLoading = false;
  bool _firstTime = true;

  @override
  String? get searchFieldLabel => this.searchLabel;

  @override
  List<Widget> buildActions(BuildContext context) {
    List<Widget> _actionsList = [];

    if ((this.showAllBtn) ?? false) {
      _actionsList.add(
        IconButton(icon: Icon(Icons.list), onPressed: () => query = '*'),
      );
    }

    _actionsList.add(
      IconButton(icon: Icon(Icons.clear), onPressed: () => query = ''),
    );

    if (dataOriginal != null && dataOriginal!.isNotEmpty) {
      _actionsList.add(
        IconButton(
          icon: Icon(Icons.filter_list),
          onPressed: () {
            dataSource.clear();
            dataSource.addAll(dataOriginal!);
            _showFilterDialog(context, this.dataSource,
                this.configTiles.filterLabels!, dataOriginal!);
          },
        ),
      );
    }

    if ((this.scannerFinder) ?? false) {
      _actionsList.add(IconButton(
        icon: Icon(Icons.qr_code_scanner_outlined),
        onPressed: () async {
          FocusScope.of(context).unfocus();
          query = await DAController.scanQR(context);
          DASearchDelegateProvider _searchProv = DASearchDelegateProvider();
          _searchProv.notify();
        },
      ));
    }

    List<dynamic> _items = this
        .dataSource
        .where((element) =>
            element['checked'] != null && element['checked'] == true)
        .toList();

    _actionsList.add(badges.Badge(
      badgeContent: Padding(
        padding: const EdgeInsets.all(1.0),
        child: Text(_items.length.toString(),
            style: TextStyle(color: Colors.white)),
      ),
      badgeStyle: badges.BadgeStyle(
        badgeColor: Theme.of(context).primaryColor,
      ),
      position: badges.BadgePosition.topStart(top: -4, start: 24),
      child: IconButton(
        icon: Icon(Icons.done),
        onPressed: () async {
          FocusScope.of(context).unfocus();
          await this.onDoneTap!(query);
        },
      ),
    ));

    return _actionsList;
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: Icon(Icons.arrow_back_ios),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return showResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _resultsBuilder(context);
  }

  Widget _resultsBuilder(context) {
    return showResults(context);
  }

  Widget showResults(BuildContext context) {
    if (_firstTime) {
      if ((this.showAllAtStart) ?? false) {
        query = '*';
      }

      _firstTime = false;
    }

    final _searchProv = Provider.of<DASearchDelegateProvider>(context);

    if (query.length == 0 && this.dataSourceSelected.isNotEmpty) {
      List<dynamic> _items = this
          .dataSource
          .where((element) =>
              element['checked'] != null && element['checked'] == true)
          .toList();
      _searchProv.finderData = _items;
    } else {
      if (query.length < (this.minLengthQuery ?? 3) && query.trim() != '*') {
        return DANoData(tipo: this.noDataLabel);
      }
    }

    if (!_searchProv.downloading && _query != query) {
      downloadData(context);
      return LoadingOverlay(
        isLoading: true,
        child: DANoData(
          tipo: 'Buscando...',
          replaceAll: true,
        ),
      );
      // ignore: dead_code
    } else if (_tapLoading) {
      return LoadingOverlay(
        isLoading: true,
        child: DANoData(
          tipo: 'Buscando...',
          replaceAll: true,
        ),
      );
    } else {
      if (query.trim() == '*') {
        var _dataListFiltro = _searchProv.finderData.toList();

        _searchProv.finderData = _dataListFiltro;
      } else if (this.specificSearch != null) {
        var _dataListFiltro = _searchProv.finderData
            .where((val) =>
                val[this.specificSearch].toString().toUpperCase() ==
                query.toUpperCase())
            .toList();

        _searchProv.finderData = _dataListFiltro;
      } else if (this.rowFilters != null && this.rowFilters!.isNotEmpty) {
        var _dataListFiltro = _searchProv.finderData.where((val) {
          return this.rowFilters!.any((filter) => val[filter]
              .toString()
              .toUpperCase()
              .contains(query.toUpperCase()));
        }).toList();

        _searchProv.finderData = _dataListFiltro;
      } else {
        var _dataListFiltro = _searchProv.finderData
            .where((val) => json
                .encode(val)
                .toString()
                .toUpperCase()
                .contains(query.toUpperCase()))
            .toList();

        _searchProv.finderData = _dataListFiltro;
      }

      if (_searchProv.finderData.length == 0) {
        _searchProv.downloading = false;
        return DANoData(tipo: this.noDataLabel);
      }

      double _sWidth = MediaQuery.of(context).size.width;
      Widget list = ListView.builder(
        padding: EdgeInsets.all(0.0),
        physics: NeverScrollableScrollPhysics(),
        itemCount: (_searchProv.finderData == null)
            ? 0
            : _searchProv.finderData.length,
        itemBuilder: (BuildContext context, int index) {
          List<Widget> _rows = [];
          int _rowsLength = (this.configTiles.moreLabels == null)
              ? 0
              : this.configTiles.moreLabels!.length;

          if (_rowsLength > 0) {
            this.configTiles.moreLabels!.forEach((String element) {
              _rows.add(
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    element +
                        ": " +
                        _searchProv.finderData[index][element].toString(),
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Colors.black45,
                      fontSize: 12,
                    ),
                  ),
                  width: ((_sWidth / ((_rowsLength == 0) ? 1 : _rowsLength)) <
                          100)
                      ? 100
                      : (_sWidth / ((_rowsLength == 0) ? 1 : _rowsLength)) - 16,
                ),
              );
            });
          }

          dynamic finderData = _searchProv.finderData[index];
          Widget _cards = DAFinderCard(
              metadata: jsonEncode(finderData),
              title: (finderData[this.configTiles.title]) ?? "Buscando...",
              bodyColumn: <Widget>[
                Container(
                  width: _sWidth,
                  height: 20,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: _rows,
                      ),
                    ],
                  ),
                ),
              ],
              leading: finderData[this.configTiles.leading],
              trailing: finderData[this.configTiles.trailing],
              icon: finderData[this.configTiles.icon],
              checked: finderData['checked'],
              onTap: (finderData) async {
                _tapLoading = true;
                _searchProv.notify();
                FocusScope.of(context).unfocus();
                await this.onFinderTap!(jsonDecode(finderData));
                this.dataSourceSelected.add(jsonDecode(finderData));
                if (this.closeOnTap ?? false) {
                  close(context, null);
                }
                _tapLoading = false;
                _searchProv.notify();
              });

          return _cards;
        },
        shrinkWrap: true,
      );

      _searchProv.downloading = false;
      return SingleChildScrollView(child: list);
    }
  }

  void downloadData(BuildContext context) async {
    try {
      DASearchDelegateProvider _searchProv = DASearchDelegateProvider();
      _searchProv.downloading = true;
      _searchProv.finderData = this.dataSource;

      Duration _duration = Duration(milliseconds: this.awaitFinder ?? 2500);
      await Future.delayed(_duration);

      _searchProv.downloading = false;
      _query = query.toString();
      _searchProv.notify();
    } catch (e) {
      DAToast(context, e.toString());
    }
  }

  void _showFilterDialog(BuildContext context, List<dynamic> data,
      List<String> keys, List<dynamic> originalData) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    Map<String, String> selectedValues = {};

    // Cargar selecciones guardadas con prefijo "finderFilter"
    for (String key in keys) {
      selectedValues[key] = prefs.getString('finderFilter_$key') ?? 'Todos';
    }

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Filtros de búsqueda'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: keys.map((key) {
                List<String> options = data
                    .map((item) => item[key]?.toString())
                    .where((value) => value != null && value != 'null')
                    .cast<String>()
                    .toSet()
                    .toList();
                options.insert(0, 'Todos');
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          isExpanded: true,
                          decoration: InputDecoration(labelText: key),
                          value: selectedValues[key],
                          items: options.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(
                                value,
                                style: TextStyle(
                                  overflow: TextOverflow.clip,
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (newValue) {
                            selectedValues[key] = newValue!;
                          },
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancelar'),
              onPressed: () async {
                String? lastFinderFilteredData =
                    await prefs.getString('lastFinderFilteredData');
                if (lastFinderFilteredData != null) {
                  data.clear();
                  data.addAll(jsonDecode(lastFinderFilteredData));
                  Provider.of<DASearchDelegateProvider>(context, listen: false)
                      .finderData = jsonDecode(lastFinderFilteredData);
                  Provider.of<DASearchDelegateProvider>(context, listen: false)
                      .notify();
                }
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Reestablecer'),
              onPressed: () async {
                data.clear();
                data.addAll(originalData);
                Provider.of<DASearchDelegateProvider>(context, listen: false)
                    .finderData = originalData;
                Provider.of<DASearchDelegateProvider>(context, listen: false)
                    .notify();
                // Restablecer las selecciones de filtros a 'Todos'
                for (String key in keys) {
                  selectedValues[key] = 'Todos';
                  await prefs.setString('finderFilter_$key', 'Todos');
                }
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Aplicar'),
              onPressed: () async {
                List<dynamic> filteredData = data.where((item) {
                  return keys.every((key) {
                    return selectedValues[key] == 'Todos' ||
                        item[key] == selectedValues[key];
                  });
                }).toList();

                // Guardar selecciones con prefijo "finderFilter"
                for (String key in keys) {
                  await prefs.setString(
                      'finderFilter_$key', selectedValues[key]!);
                }

                // Guardar los datos filtrados para restaurarlos después
                await prefs.setString(
                    'lastFinderFilteredData', jsonEncode(filteredData));

                print('Filtros seleccionados: $selectedValues');
                print('Datos filtrados: $filteredData');
                data.clear();
                data.addAll(filteredData);
                if (query.isEmpty) {
                  query = '*';
                }
                Provider.of<DASearchDelegateProvider>(context, listen: false)
                    .finderData = filteredData;
                Provider.of<DASearchDelegateProvider>(context, listen: false)
                    .notify();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}

// ignore: must_be_immutable
class DAArtScanner extends StatefulWidget {
  final String refID;
  final String? title;
  final String? subtitle;
  final IconData? icon;
  final String? inputLabel;
  final String? okText;
  final Function(dynamic)? onPressed;
  final Function()? onCancelPressed;
  bool? processOnScann;
  int? processOnScannDelay;

  DAArtScanner({
    required this.refID,
    this.title,
    this.subtitle,
    this.icon,
    this.inputLabel,
    this.okText,
    this.onPressed,
    this.onCancelPressed,
    this.processOnScann,
    this.processOnScannDelay,
  });

  String get elID => this.refID;

  @override
  _DAArtScanner createState() => _DAArtScanner();
}

class _DAArtScanner extends State<DAArtScanner> {
  @override
  Widget build(BuildContext context) {
    return DABottomAppBarButton(
      icon: widget.icon ?? Icons.document_scanner_outlined,
      label: widget.title ?? 'Agregar',
      onTap: () {
        _confirmAddDialog();
      },
    );
  }

  TextEditingController _editTxt = new TextEditingController(text: "");
  Timer? _debounce;

  @override
  void initState() {
    super.initState();

    if (widget.processOnScann == true) {
      _editTxt.addListener(_handleEditTxtChange);
      _debounce = null;
    }
  }

  @override
  void dispose() {
    _editTxt.dispose();

    if (widget.processOnScann == true) {
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }

      _editTxt.removeListener(_handleEditTxtChange);
      _editTxt.dispose();
    }

    super.dispose();
  }

  void _handleEditTxtChange() {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    if (_editTxt.text.isNotEmpty) {
      _debounce = Timer(Duration(seconds: widget.processOnScannDelay ?? 0), () {
        _inputOnFieldSubmitted();
      });
    }
  }

  _inputOnFieldSubmitted() async {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    await widget.onPressed!(_editTxt.text);
    _editTxt.clear();
  }

  _confirmAddDialog() async {
    _editTxt.text = "";
    FocusNode _focusTxt = new FocusNode();

    Widget _input = DAInput(
      refID: widget.refID,
      tipo: DAInputType.string,
      label: widget.inputLabel ?? 'Ingrese Valor',
      padding: 0.0,
      autofocus: true,
      focusNode: _focusTxt,
      textInputAction: TextInputAction.send,
      controller: _editTxt,
      onFieldSubmitted: (val) async {
        _inputOnFieldSubmitted();
      },
      onEditingComplete: () {},
    );

    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        Widget _dialog = DAInputDialog(
          title: widget.title ?? 'Agregar',
          subtitle: widget.subtitle ?? "Modifique los valores requeridos.",
          okText: widget.okText ?? "Aceptar",
          cancelText: "Cerrar",
          onCancelPressed: widget.onCancelPressed,
          onPressed: () async {
            if (_debounce?.isActive ?? false) {
              _debounce?.cancel();
            }

            await widget.onPressed!(_editTxt.text);
            _editTxt.clear();
          },
          input: [
            Container(
              margin: EdgeInsets.only(top: 10),
              child: _input,
            ),
          ],
        );
        return _dialog;
      },
    );
  }
}

// ignore: must_be_immutable
class DAArtScannerOptions extends StatefulWidget {
  final String refID;
  final String? title;
  final String? subtitle;
  final IconData? icon;
  final String? inputLabel;
  final String? okText;
  final Function(dynamic)? onPressed;
  final Function()? onCancelPressed;
  final List<dynamic>? options;
  final String? optionsLabel;
  final String? optionsValue;
  final String? optionsText;

  DAArtScannerOptions({
    required this.refID,
    this.title,
    this.subtitle,
    this.icon,
    this.inputLabel,
    this.okText,
    this.onPressed,
    this.onCancelPressed,
    this.options,
    this.optionsLabel,
    this.optionsValue,
    this.optionsText,
  });

  String get elID => this.refID;

  @override
  _DAArtScannerOptions createState() => _DAArtScannerOptions();
}

class _DAArtScannerOptions extends State<DAArtScannerOptions> {
  @override
  Widget build(BuildContext context) {
    return DABottomAppBarButton(
      icon: widget.icon ?? Icons.document_scanner_outlined,
      label: widget.title ?? 'Agregar',
      onTap: () {
        _confirmAddDialog();
      },
    );
  }

  _confirmAddDialog() async {
    TextEditingController _editTxt = new TextEditingController(text: "");
    List<dynamic> _radioOptions = widget.options ?? [];
    String _selectedOption =
        _radioOptions.length >= 1 ? _radioOptions[0][widget.optionsValue] : '';

    Widget _input = DAInput(
      refID: widget.refID,
      tipo: DAInputType.string,
      label: widget.inputLabel ?? 'Ingrese Valor',
      padding: 0.0,
      autofocus: true,
      textInputAction: TextInputAction.send,
      controller: _editTxt,
      onFieldSubmitted: (val) async {
        await widget
            .onPressed!({'tipo': _selectedOption, 'valor': _editTxt.text});
        _editTxt.clear();
      },
      onEditingComplete: () {},
    );

    Widget _radio = DARadioList(
      data: _radioOptions,
      padding: 0.0,
      controllerValue: _selectedOption,
      inputLabel: widget.optionsLabel ?? 'Movimiento',
      value: widget.optionsValue ?? 'valor',
      text: widget.optionsText ?? 'campo',
      onChanged: (value) {
        _selectedOption = value.toString();
      },
    );

    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        Widget _dialog = DAInputDialog(
          title: widget.title ?? 'Agregar',
          subtitle: widget.subtitle ?? "Modifique los valores requeridos.",
          okText: widget.okText ?? "Aceptar",
          cancelText: "Cerrar",
          onCancelPressed: widget.onCancelPressed,
          onPressed: () async {
            await widget
                .onPressed!({'tipo': _selectedOption, 'valor': _editTxt.text});
            _editTxt.clear();
          },
          input: [
            _radio,
            Container(
              margin: EdgeInsets.only(top: 10),
              child: _input,
            ),
          ],
        );
        return _dialog;
      },
    );
  }
}

// ignore: must_be_immutable
class DAArtScannerCounter extends StatefulWidget {
  final String refID;
  final TextEditingController counterCtrl;
  final String? title;
  final String? subtitle;
  final IconData? icon;
  final String? inputLabel;
  final String? counterLabel;
  final String? okText;
  final Function(dynamic)? onProcess;
  final Function(dynamic)? onPressed;
  final Function(dynamic)? onCounter;
  final Function()? onCancelPressed;
  final double? counter;
  bool? isLoading;
  bool? processOnScann;
  int? processOnScannDelay;

  DAArtScannerCounter({
    required this.refID,
    required this.counterCtrl,
    this.title,
    this.subtitle,
    this.icon,
    this.inputLabel,
    this.counterLabel,
    this.okText,
    this.onProcess,
    this.onPressed,
    this.onCounter,
    this.onCancelPressed,
    this.counter,
    this.isLoading,
    this.processOnScann,
    this.processOnScannDelay,
  });

  String get elID => this.refID;

  @override
  _DAArtScannerCounter createState() => _DAArtScannerCounter();
}

class _DAArtScannerCounter extends State<DAArtScannerCounter> {
  @override
  Widget build(BuildContext context) {
    return DABottomAppBarButton(
      icon: widget.icon ?? Icons.document_scanner_outlined,
      label: widget.title ?? 'Agregar',
      onTap: () {
        _confirmAddDialog();
      },
    );
  }

  TextEditingController _editTxt = new TextEditingController(text: "");
  Timer? _debounce;

  @override
  void initState() {
    super.initState();

    if (widget.processOnScann == true) {
      _editTxt.addListener(_handleEditTxtChange);
      _debounce = null;
    }
  }

  @override
  void dispose() {
    _editTxt.dispose();

    if (widget.processOnScann == true) {
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }

      _editTxt.removeListener(_handleEditTxtChange);
      _editTxt.dispose();
    }

    super.dispose();
  }

  void _handleEditTxtChange() {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    if (_editTxt.text.isNotEmpty) {
      _debounce = Timer(Duration(seconds: widget.processOnScannDelay ?? 0), () {
        _inputOnFieldSubmitted();
      });
    }
  }

  _inputOnFieldSubmitted() async {
    widget.isLoading = true;
    if (mounted) {
      setState(() {});
    }

    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    await widget.onPressed!({
      'input': _editTxt.text,
      'counter': double.parse(widget.counterCtrl.text),
    });
    _editTxt.clear();
    widget.isLoading = false;

    if (mounted) {
      setState(() {});
    }
  }

  _confirmAddDialog() async {
    _editTxt.text = "";
    FocusNode _focusTxt = new FocusNode();

    Widget _input = DAInput(
      refID: widget.refID,
      tipo: DAInputType.string,
      label: widget.inputLabel ?? 'Ingrese Valor',
      padding: 0.0,
      autofocus: true,
      focusNode: _focusTxt,
      textInputAction: TextInputAction.send,
      controller: _editTxt,
      onFieldSubmitted: (val) async {
        _inputOnFieldSubmitted();
      },
      onEditingComplete: () {},
    );

    Widget _counter = DAInput(
      refID: widget.refID,
      tipo: DAInputType.number,
      label: widget.counterLabel ?? 'Ingrese Cantidad',
      padding: 0.0,
      autofocus: false,
      textInputAction: TextInputAction.send,
      controller: widget.counterCtrl,
      onFieldSubmitted: (val) async {
        await widget.onCounter!({
          'input': _editTxt.text,
          'counter': double.parse(
              widget.counterCtrl.text == '' ? '0' : widget.counterCtrl.text),
        });
        _focusTxt.requestFocus();
      },
      onEditingComplete: () {},
    );

    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        Widget _dialog = DAInputDialog(
          title: widget.title ?? 'Agregar',
          subtitle: widget.subtitle ?? "Modifique los valores requeridos.",
          okText: widget.okText ?? "Aceptar",
          cancelText: "Cerrar",
          onCancelPressed: widget.onCancelPressed,
          onPressed: () async {
            if (_debounce?.isActive ?? false) {
              _debounce?.cancel();
            }

            await widget.onProcess!({
              'input': _editTxt.text,
              'counter': double.parse(widget.counterCtrl.text),
            });
          },
          input: [
            Container(
              margin: EdgeInsets.only(top: 10),
              child: _input,
            ),
            Container(
              margin: EdgeInsets.only(top: 10),
              child: _counter,
            ),
          ],
        );
        return LoadingOverlay(
          isLoading: (widget.isLoading) ?? false,
          child: _dialog,
        );
      },
    );
  }
}

// ignore: must_be_immutable
class DAScannerDialog extends StatefulWidget {
  final String refID;
  final String? title;
  final String? subtitle;
  final String? inputLabel;
  final DAInputType? inputType;
  final String? inputValue;
  final String? okText;
  final Function(dynamic)? onPressed;
  final String? cancelText;
  final Function()? onCancelPressed;
  final bool? processOnScann;
  final int? processOnScannDelay;
  final bool? dateInput;
  final String? dateValue;
  final bool? isAutoCompleteTextField;
  final Widget? autoCompleteTextField;
  Widget? extraAction;
  Widget? extraContent;

  DAScannerDialog({
    required this.refID,
    this.title,
    this.subtitle,
    this.inputLabel,
    this.inputType,
    this.inputValue,
    this.okText,
    this.onPressed,
    this.cancelText,
    this.onCancelPressed,
    this.processOnScann,
    this.processOnScannDelay,
    this.dateInput,
    this.dateValue,
    this.isAutoCompleteTextField = false,
    this.autoCompleteTextField,
    this.extraAction,
    this.extraContent,
  });

  String get elID => this.refID;

  @override
  _DAScannerDialogState createState() => _DAScannerDialogState();
}

class _DAScannerDialogState extends State<DAScannerDialog> {
  TextEditingController _editTxt = TextEditingController();
  DateTime _editDate = DateTime.now();
  FocusNode _focusTxt = FocusNode();
  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return _buildDialog();
  }

  @override
  void initState() {
    super.initState();

    _editTxt.text = widget.inputValue ?? "";

    if (widget.dateInput ?? false) {
      _editDate = DateTime.parse(widget.dateValue ?? DateTime.now().toString());
    }

    if (widget.processOnScann == true) {
      _editTxt.addListener(_handleEditTxtChange);
      _debounce = null;
    }
  }

  @override
  void dispose() {
    _editTxt.dispose();

    if (widget.processOnScann == true) {
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }

      _editTxt.removeListener(_handleEditTxtChange);
      _editTxt.dispose();
    }

    super.dispose();
  }

  void _handleEditTxtChange() {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    if (_editTxt.text.isNotEmpty) {
      _debounce = Timer(Duration(seconds: widget.processOnScannDelay ?? 0), () {
        _inputOnFieldSubmitted();
      });
    }
  }

  _inputOnFieldSubmitted() async {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    await widget.onPressed!(
        widget.dateInput == true ? _editDate.toString() : _editTxt.text);
    _editTxt.clear();
  }

  Widget _input() {
    return widget.isAutoCompleteTextField ?? false
        ? widget.autoCompleteTextField ?? Container()
        : DAInput(
            refID: widget.refID,
            tipo: widget.inputType ?? DAInputType.string,
            label: widget.inputLabel ?? 'Ingrese Valor',
            padding: 0.0,
            autofocus: true,
            focusNode: _focusTxt,
            textInputAction: TextInputAction.send,
            controller: _editTxt,
            onFieldSubmitted: (val) async {
              _inputOnFieldSubmitted();
            },
            onEditingComplete: () {},
          );
  }

  Widget _dateInput() {
    return DADatePicker(
      refID: widget.refID,
      label: widget.inputLabel ?? "Seleccione la fecha",
      padding: EdgeInsets.symmetric(horizontal: 0.0),
      value: _editDate,
      onDateChange: (value) async {
        _editDate = value;
      },
    );
  }

  Widget _buildDialog() {
    List<Widget> _inputContent = [
      Container(
        margin: EdgeInsets.only(top: 10),
        child: widget.dateInput == true ? _dateInput() : _input(),
      ),
    ];

    if (widget.extraContent != null) {
      _inputContent.add(
        Container(
          padding: EdgeInsets.only(left: 10),
          alignment: Alignment.centerLeft,
          child: widget.extraContent,
        ),
      );
    }

    return DAInputDialog(
      title: widget.title ?? 'Agregar',
      subtitle: widget.subtitle ?? "Modifique los valores requeridos.",
      okText: widget.okText ?? "Aceptar",
      cancelText: widget.cancelText ?? "Cerrar",
      onCancelPressed: widget.onCancelPressed,
      onPressed: () async {
        if (_debounce?.isActive ?? false) {
          _debounce?.cancel();
        }

        await widget.onPressed!(
            widget.dateInput == true ? _editDate.toString() : _editTxt.text);
        _editTxt.clear();
      },
      input: _inputContent,
      extraAction: widget.extraAction,
    );
  }
}

// ignore: must_be_immutable
class DAScannerDecomposerDialog extends StatefulWidget {
  final String refID;
  final String? title;
  final String? subtitle;
  final String? inputLabel;
  final String? inputValue;
  final String? okText;
  final Function(dynamic)? onPressed;
  final String? cancelText;
  final Function()? onCancelPressed;
  final bool? processOnScann;
  final int? processOnScannDelay;
  final List<DADecomposerModel> decomposerRules;

  /// Funcion para escanear y descomponer un código de barras
  DAScannerDecomposerDialog({
    required this.refID,
    this.title,
    this.subtitle,
    this.inputLabel,
    this.inputValue,
    this.okText,
    this.onPressed,
    this.cancelText,
    this.onCancelPressed,
    this.processOnScann,
    this.processOnScannDelay,
    required this.decomposerRules,
  });

  String get elID => this.refID;

  @override
  _DAScannerDialogDecomposerState createState() =>
      _DAScannerDialogDecomposerState();
}

class _DAScannerDialogDecomposerState extends State<DAScannerDecomposerDialog> {
  TextEditingController _editTxt = TextEditingController();
  FocusNode _focusTxt = FocusNode();
  Timer? _debounce;

  @override
  Widget build(BuildContext context) {
    return _buildDialog();
  }

  @override
  void initState() {
    super.initState();

    _editTxt.text = widget.inputValue ?? "";

    if (widget.processOnScann == true) {
      _editTxt.addListener(_handleEditTxtChange);
      _debounce = null;
    }
  }

  @override
  void dispose() {
    _editTxt.dispose();

    if (widget.processOnScann == true) {
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }

      _editTxt.removeListener(_handleEditTxtChange);
      _editTxt.dispose();
    }

    super.dispose();
  }

  void _handleEditTxtChange() {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    if (_editTxt.text.isNotEmpty) {
      _debounce = Timer(Duration(seconds: widget.processOnScannDelay ?? 0), () {
        _inputOnFieldSubmitted();
      });
    }
  }

  List<Map<String, dynamic>> _decomposeString() {
    List<Map<String, dynamic>> _mappedCB = [];

    if (_editTxt.text.trim() == "") {
      return _mappedCB;
    }

    for (DADecomposerModel rule in widget.decomposerRules) {
      print(rule.toString());
      print(rule.name);
      Map<String, dynamic> map = {};

      try {
        dynamic value = _editTxt.text.substring((rule.start - 1), rule.end);

        switch (rule.type) {
          case DADecomposerType.string:
            value = value;
            break;
          case DADecomposerType.number:
            if (rule.decimales! > 0 && value.length >= rule.decimales!) {
              String number = value.substring(0, value.length - rule.decimales);
              String decimals = value.substring(value.length - rule.decimales);

              value = "$number.$decimals";
            }

            value = double.parse(value);
            break;
          case DADecomposerType.datetime:
            // intl no funciona ya que la fecha obtenida del CB no tiene separaciones

            String day = "";
            String month = "";
            String year = "";

            if (value.length == 6) {
              switch (rule.dateFormat) {
                case "ddMMyy":
                  day = value.substring(0, 2);
                  month = value.substring(2, 4);
                  year = value.substring(4, 6);
                  break;
                case "MMddyy":
                  day = value.substring(2, 4);
                  month = value.substring(0, 2);
                  year = value.substring(4, 6);
                  break;
                case "yyMMdd":
                  day = value.substring(4, 6);
                  month = value.substring(2, 4);
                  year = value.substring(0, 2);
                  break;
                case "yyddMM":
                  day = value.substring(2, 4);
                  month = value.substring(4, 6);
                  year = value.substring(0, 2);
                  break;
              }

              if (day.length == 2 && month.length == 2 && year.length == 2) {
                value = DateFormat("dd-MM-yy").parse("$day-$month-$year");
              }
            } else if (value.length == 8) {
              switch (rule.dateFormat) {
                case "ddMMyyyy":
                  day = value.substring(0, 2);
                  month = value.substring(2, 4);
                  year = value.substring(4, 8);
                  break;
                case "MMddyyyy":
                  day = value.substring(2, 4);
                  month = value.substring(0, 2);
                  year = value.substring(4, 8);
                  break;
                case "yyyyMMdd":
                  day = value.substring(6, 8);
                  month = value.substring(4, 6);
                  year = value.substring(0, 4);
                  break;
                case "yyyyddMM":
                  day = value.substring(4, 6);
                  month = value.substring(6, 8);
                  year = value.substring(0, 4);
                  break;
              }

              if (day.length == 2 && month.length == 2 && year.length == 4) {
                value = DateFormat("dd-MM-yyyy").parse("$day-$month-$year");
              }
            } else {
              value = "";
            }

            break;
          default:
            value = value;
        }

        map = {
          "Name": rule.name,
          "Value": value,
          "Type": rule.type,
        };
      } catch (e) {
        map = {
          "Name": rule.name,
          "Value": "",
          "Type": rule.type,
        };
      }

      _mappedCB.add(map);
    }

    return _mappedCB;
  }

  _inputOnFieldSubmitted() async {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    await widget.onPressed!({
      "cb": _editTxt.text,
      "cbArr": _decomposeString(),
    });
    _editTxt.clear();
  }

  Widget _input() {
    return DAInput(
      refID: widget.refID,
      tipo: DAInputType.string,
      label: widget.inputLabel ?? 'Ingrese Valor',
      padding: 0.0,
      autofocus: true,
      focusNode: _focusTxt,
      textInputAction: TextInputAction.send,
      controller: _editTxt,
      onFieldSubmitted: (val) async {
        _inputOnFieldSubmitted();
      },
      onEditingComplete: () {},
    );
  }

  Widget _buildDialog() {
    return DAInputDialog(
      title: widget.title ?? 'Agregar',
      subtitle: widget.subtitle ?? "Modifique los valores requeridos.",
      okText: widget.okText ?? "Aceptar",
      cancelText: widget.cancelText ?? "Cerrar",
      onCancelPressed: widget.onCancelPressed,
      onPressed: () async {
        if (_debounce?.isActive ?? false) {
          _debounce?.cancel();
        }

        await widget.onPressed!({
          "cb": _editTxt.text,
          "cbArr": _decomposeString(),
        });
        _editTxt.clear();
      },
      input: [
        Container(
          margin: EdgeInsets.only(top: 10),
          child: _input(),
        ),
      ],
    );
  }
}

// ignore: must_be_immutable
class DAScannerDialogCounter extends StatefulWidget {
  final String refID;
  final TextEditingController counterCtrl;
  final TextEditingController? boxCounterCtrl; // Made optional
  final double? boxFactor; // Made optional
  final String? title;
  final String? subtitle;
  final String? inputValueLabel;
  final String? inputValue;
  final String? inputCounterLabel;
  final bool? inputCounterDisabled;
  final String? inputBoxCounterLabel; // Made optional
  final String? okText;
  final Function(dynamic)? onProcess;
  final Function(dynamic)? onPressed;
  final Function(dynamic)? onCounter;
  final String? cancelText;
  final Function()? onCancelPressed;
  final bool? processOnScann;
  final int? processOnScannDelay;
  final List<Widget>? input;
  final bool limpiarCB;
  final int? counterDecimals;
  final bool? noCancel;
  Widget? extraAction;

  DAScannerDialogCounter({
    required this.refID,
    required this.counterCtrl,
    this.boxCounterCtrl, // Made optional
    this.boxFactor, // Made optional
    this.title,
    this.subtitle,
    this.inputValueLabel,
    this.inputValue,
    this.inputCounterLabel,
    this.inputCounterDisabled,
    this.inputBoxCounterLabel, // Made optional
    this.okText,
    this.onProcess,
    this.onPressed,
    this.onCounter,
    this.cancelText,
    this.onCancelPressed,
    this.processOnScann,
    this.processOnScannDelay,
    this.input,
    this.limpiarCB = true,
    this.counterDecimals,
    this.noCancel,
    this.extraAction,
  });

  String get elID => this.refID;

  @override
  _DAScannerDialogCounterState createState() => _DAScannerDialogCounterState();
}

class _DAScannerDialogCounterState extends State<DAScannerDialogCounter> {
  TextEditingController _editTxt = TextEditingController();
  FocusNode _focusTxt = FocusNode();
  Timer? _debounce;
  Timer? _boxCounterUpdateDebounce;
  bool _isUpdatingBoxCounter = false; // Added to prevent cyclic updates

  @override
  Widget build(BuildContext context) {
    return _buildDialog();
  }

  @override
  void initState() {
    super.initState();

    _editTxt.text = widget.inputValue ?? "";

    // widget.counterCtrl.addListener(_updateBoxCounter);
    // widget.boxCounterCtrl?.addListener(_updatePieceCounter); // Made optional

    if (widget.processOnScann == true) {
      _editTxt.addListener(_handleEditTxtChange);
      _debounce = null;
    }
  }

  @override
  void dispose() {
    _editTxt.dispose();
    // widget.counterCtrl.removeListener(_updateBoxCounter);
    // widget.boxCounterCtrl?.removeListener(_updatePieceCounter); // Made optional

    if (widget.processOnScann == true) {
      if (_debounce?.isActive ?? false) {
        _debounce?.cancel();
      }

      _editTxt.removeListener(_handleEditTxtChange);
      _editTxt.dispose();
    }

    _boxCounterUpdateDebounce?.cancel();

    super.dispose();
  }

  void _handleEditTxtChange() {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    if (_editTxt.text.isNotEmpty) {
      _debounce =
          Timer(Duration(seconds: widget.processOnScannDelay ?? 0), () async {
        await _inputOnFieldSubmitted();
        _updateBoxCounter();
      });
    }
  }

  _inputOnFieldSubmitted() async {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    await widget.onPressed!({
      'Value': _editTxt.text,
      'Counter': double.tryParse(widget.counterCtrl.text) ?? 0,
      'BoxCounter': widget.boxCounterCtrl != null
          ? double.parse(widget.boxCounterCtrl!.text)
          : null, // Adjusted for optional
    });
    if (widget.limpiarCB) {
      _editTxt.clear();
    }
  }

  void _updateBoxCounter() {
    if (!_isUpdatingBoxCounter &&
        widget.boxFactor != null &&
        widget.boxCounterCtrl != null) {
      _isUpdatingBoxCounter = true; // Prevent cyclic updates
      _boxCounterUpdateDebounce?.cancel();
      _boxCounterUpdateDebounce = Timer(Duration(seconds: 0), () {
        double pieces = double.tryParse(widget.counterCtrl.text) ?? 0;
        widget.boxCounterCtrl!.text = (pieces / widget.boxFactor!)
            .toStringAsFixed(widget.counterDecimals ?? 2);
        _isUpdatingBoxCounter = false; // Allow updates again
      });
    }
  }

  void _updatePieceCounter() {
    if (_isUpdatingBoxCounter) return; // Prevent cyclic updates
    if (widget.boxFactor != null && widget.boxCounterCtrl != null) {
      _boxCounterUpdateDebounce?.cancel();
      _boxCounterUpdateDebounce = Timer(Duration(seconds: 0), () {
        double boxes = double.tryParse(widget.boxCounterCtrl!.text) ?? 0;
        widget.counterCtrl.text = (boxes * widget.boxFactor!)
            .toStringAsFixed(widget.counterDecimals ?? 2);
      });
    }
  }

  Widget _inputValue() {
    return DAInput(
      refID: widget.refID,
      tipo: widget.limpiarCB ? DAInputType.string : DAInputType.ayuda,
      label: widget.inputValueLabel ?? 'Ingrese Valor',
      padding: 0.0,
      autofocus: true,
      focusNode: _focusTxt,
      textInputAction: TextInputAction.send,
      controller: _editTxt,
      onFieldSubmitted: (val) async {
        await _inputOnFieldSubmitted();
        _updateBoxCounter();
      },
      onEditingComplete: () {},
      iconAyuda: widget.limpiarCB ? null : Icons.clear,
      onAyudaTap: () => widget.limpiarCB ? null : _editTxt.clear(),
    );
  }

  Widget _inputCounter() {
    return DAInput(
      refID: widget.refID,
      tipo: DAInputType.number,
      label: widget.inputCounterLabel ?? 'Ingrese Cantidad',
      padding: 0.0,
      autofocus: false,
      textInputAction: TextInputAction.send,
      controller: widget.counterCtrl,
      onFieldSubmitted: (val) async {
        await widget.onCounter!({
          'Value': _editTxt.text,
          'Counter': double.tryParse(widget.counterCtrl.text) ?? 0,
          'BoxCounter': widget.boxCounterCtrl != null
              ? double.parse(widget.boxCounterCtrl!.text)
              : null, // Adjusted for optional
        });
        _focusTxt.requestFocus();
        _updatePieceCounter();
      },
      onChanged: (value) {
        double pieces = double.tryParse(widget.counterCtrl.text) ?? 0;
        widget.boxCounterCtrl!.text = (pieces / widget.boxFactor!)
            .toStringAsFixed(widget.counterDecimals ?? 2);
      },
      onEditingComplete: () {},
      decimales: widget.counterDecimals ?? 0,
      disabled: widget.inputCounterDisabled,
    );
  }

  Widget _inputBoxCounter() {
    if (widget.boxCounterCtrl != null && widget.inputBoxCounterLabel != null) {
      // Check added for optional
      return DAInput(
        refID: widget.refID,
        tipo: DAInputType.number,
        label: widget.inputBoxCounterLabel!, // Adjusted for optional
        padding: 0.0,
        autofocus: false,
        textInputAction: TextInputAction.send,
        controller: widget.boxCounterCtrl!, // Adjusted for optional
        onFieldSubmitted: (val) async {
          await widget.onCounter!({
            'Value': _editTxt.text,
            'Counter': double.tryParse(widget.counterCtrl.text) ?? 0,
            'BoxCounter': double.parse(
                widget.boxCounterCtrl!.text), // Adjusted for optional
          });
          _focusTxt.requestFocus();
          _updateBoxCounter();
        },
        onChanged: (value) {
          double boxes = double.tryParse(widget.boxCounterCtrl!.text) ?? 0;
          widget.counterCtrl.text = (boxes * widget.boxFactor!)
              .toStringAsFixed(widget.counterDecimals ?? 2);
        },
        onEditingComplete: () {},
        decimales: widget.counterDecimals ?? 0,
      );
    } else {
      return Container(); // Return an empty container if boxCounterCtrl or inputBoxCounterLabel is null
    }
  }

  List<Widget> _inputList() {
    List<Widget> _inputList = [
      Container(
        margin: EdgeInsets.only(top: 10),
        child: _inputValue(),
      ),
      Container(
        margin: EdgeInsets.only(top: 10),
        child: _inputCounter(),
      ),
    ];

    // Conditionally add the box counter input if it's not null
    if (widget.boxCounterCtrl != null && widget.inputBoxCounterLabel != null) {
      _inputList.add(Container(
        margin: EdgeInsets.only(top: 10),
        child: _inputBoxCounter(),
      ));
    }

    List<Widget> _extraInfo = widget.input ?? [];

    _extraInfo.forEach((e) {
      _inputList.add(Container(
        margin: EdgeInsets.only(top: 10),
        child: e,
      ));
    });

    return _inputList;
  }

  Widget _buildDialog() {
    return DAInputDialog(
      title: widget.title ?? 'Agregar',
      subtitle: widget.subtitle ?? "Modifique los valores requeridos.",
      okText: widget.okText ?? "Aceptar",
      cancelText: widget.cancelText ?? "Cerrar",
      onCancelPressed: widget.onCancelPressed,
      onPressed: () async {
        if (_debounce?.isActive ?? false) {
          _debounce?.cancel();
        }

        await widget.onProcess!({
          'Value': _editTxt.text,
          'Counter': double.tryParse(widget.counterCtrl.text) ?? 0,
          'BoxCounter': widget.boxCounterCtrl != null
              ? double.parse(widget.boxCounterCtrl!.text)
              : null, // Adjusted for optional
        });

        if (widget.limpiarCB) {
          _editTxt.clear();
        }
      },
      input: _inputList(),
      noCancel: widget.noCancel,
      extraAction: widget.extraAction,
    );
  }
}
