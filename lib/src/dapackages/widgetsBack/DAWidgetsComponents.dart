import 'dart:io';
import 'dart:math';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../main_DAPackages.dart';

// ignore: import_of_legacy_library_into_null_safe
import 'package:another_flushbar/flushbar.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:badges/badges.dart' as badges;
export 'dart:convert';
export 'dart:typed_data';
export 'package:geolocator/geolocator.dart';
export 'package:flutter_svg/flutter_svg.dart';
export 'package:url_launcher/url_launcher.dart';
export 'package:image_picker/image_picker.dart';
export 'package:loading_overlay/loading_overlay.dart';
export 'package:flutter_signature_pad/flutter_signature_pad.dart';

// ignore: non_constant_identifier_names
void DAToastOld(BuildContext context, String mensaje) {
  String _logOutMsg = "SESIÓN EXPIRADA";
  try {
    ScaffoldMessenger.of(context).removeCurrentSnackBar();
    mensaje = (mensaje.indexOf(_logOutMsg) >= 0) ? _logOutMsg : mensaje;

    final snackbar = SnackBar(
      content: Text(
        mensaje,
        style: TextStyle(
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.grey[900], //Theme.of(context).primaryColor,
      // behavior: SnackBarBehavior.floating,
      duration: Duration(seconds: 2),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackbar);
    bool isLogin = DASessionProvider.toLogin;
    if (!isLogin || mensaje == _logOutMsg) {
      throw mensaje;
    }
  } catch (e) {
    if (mensaje == _logOutMsg) {
      throw mensaje;
    }
  }
}

// ignore: non_constant_identifier_names

void DAToast(BuildContext context, String mensaje,
    {bool useFlutterToast = false, bool shortToast = false}) {
  String _logOutMsg = "SESIÓN EXPIRADA";
  try {
    mensaje = (mensaje.indexOf(_logOutMsg) >= 0) ? _logOutMsg : mensaje;

    if (useFlutterToast) {
      Fluttertoast.showToast(
        msg: mensaje,
        toastLength: shortToast ? Toast.LENGTH_SHORT : Toast.LENGTH_LONG,
        gravity: ToastGravity.SNACKBAR,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    } else {
      Flushbar(
        message: mensaje,
        margin: EdgeInsets.all(8),
        flushbarStyle: FlushbarStyle.FLOATING,
        duration: Duration(seconds: 3),
        leftBarIndicatorColor: Theme.of(context).primaryColor,
      )..show(context);
    }

    bool isLogin = DASessionProvider.toLogin;
    if (!isLogin || mensaje == _logOutMsg) {
      throw mensaje;
    }
  } catch (e) {
    if (mensaje == _logOutMsg) {
      throw mensaje;
    }
  }
}

class DALoginBackground extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final sf = Stack(
      children: <Widget>[
        Container(
          height: size.height,
          width: size.width,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.black
              : Colors.white,
        ),
        //BackWaveContainer(),
        Hero(
          tag: 'gbApp',
          child: DABottomWaveContainer(),
        ),
      ],
    );

    return sf;
  }
}

class DALoginPagosBackground extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final sf = Stack(
      children: <Widget>[
        Container(
          height: size.height,
          width: size.width,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.black
              : Colors.white,
        ),
        //BackWaveContainer(),
        Hero(
          tag: 'gbApp',
          child: DABottomWaveContainer(),
        ),
      ],
    );

    return sf;
  }
}

class DATitleMin extends StatelessWidget {
  final String title;
  final String? heroTag;
  final Widget? aux;

  DATitleMin({
    required this.title,
    this.heroTag,
    this.aux,
  });

  @override
  Widget build(BuildContext context) {
    Random random = new Random();
    int _randomNumber = random.nextInt(100);

    Widget _title = SafeArea(
      child: ListView(
        reverse: true,
        padding: EdgeInsets.only(left: 15.0),
        children: <Widget>[
          Hero(
            tag: (this.heroTag) ?? 'effectrnf-' + _randomNumber.toString(),
            child: Text(
              this.title,
              style: TextStyle(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );

    Widget sliverBar = SliverAppBar(
      automaticallyImplyLeading: false,
      elevation: 2.0,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.grey.shade800
          : Colors.white,
      collapsedHeight: 60,
      expandedHeight: 60.0,
      pinned: true,
      actions: <Widget>[
        (this.aux) ?? Container(),
        Container(
          margin: EdgeInsets.all(10.0),
          child: IconButton(
            iconSize: 36.0,
            icon: Icon(
              Icons.person_pin_circle_outlined,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            ),
            onPressed: () {
              Scaffold.of(context).openEndDrawer();
            },
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        title: _title,
      ),
    );

    return sliverBar;
  }
}

class DATitleBig extends StatelessWidget {
  final String prefix;
  final String title;
  final String? heroTag;
  final Widget? aux;
  final bool? hasLeading;

  DATitleBig({
    required this.prefix,
    required this.title,
    this.heroTag,
    this.aux,
    this.hasLeading,
  });

  @override
  Widget build(BuildContext context) {
    Random random = new Random();
    int _randomNumber = random.nextInt(100);

    Widget _title = SafeArea(child: LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        Widget _listTitle = ListView(
          reverse: true,
          padding: EdgeInsets.only(
            left: 8,
            right: 15,
          ),
          children: <Widget>[
            Hero(
              tag: (this.heroTag) ?? 'effectrnf-' + _randomNumber.toString(),
              child: Text(
                this.title,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black,
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            SizedBox(height: 5),
            Text(
              this.prefix,
              style: TextStyle(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white70
                    : Colors.black87,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        );

        if (hasLeading ?? false) {
          double percent = ((constraints.maxHeight - kToolbarHeight) *
              100 /
              (180 - kToolbarHeight));
          double dx = 0;

          dx = (100 - percent) / 2;

          if (constraints.maxHeight == 100) {
            dx = 0;
          }

          return Transform.translate(
            child: _listTitle,
            offset: Offset(dx, 0),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.only(left: 6),
            child: _listTitle,
          );
        }
      },
    ));

    Widget sliverBar = SliverAppBar(
      automaticallyImplyLeading: false,
      elevation: 2.0,
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.black
          : Colors.white,
      collapsedHeight: 60,
      expandedHeight: 200.0,
      leadingWidth: (hasLeading ?? false) ? 60 : 0,
      pinned: true,
      actions: <Widget>[
        (this.aux) ?? Container(),
        Container(
          margin: EdgeInsets.all(10.0),
          child: IconButton(
            iconSize: 36.0,
            icon: Icon(
              Icons.person_pin_circle_outlined,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            ),
            onPressed: () {
              Scaffold.of(context).openEndDrawer();
            },
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        titlePadding: EdgeInsets.only(left: 24),
        title: _title,
      ),
    );

    return sliverBar;
  }
}

class DABigMoneyTotal extends StatelessWidget {
  final String currency;
  final double amount;
  final String? heroTag;

  DABigMoneyTotal({
    required this.currency,
    required this.amount,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).brightness == Brightness.dark
          ? Colors.black
          : Colors.white,
      height: 60.0, // Adjust the height as needed
      child: Center(
        child: Hero(
          tag: (this.heroTag) ?? 'moneyEffect-${UniqueKey().toString()}',
          child: Text(
            "$currency${amount.toStringAsFixed(2)}",
            style: TextStyle(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}

class DAPageFilter extends StatelessWidget {
  final String? hintText;
  final TextEditingController controller;
  final IconData? icon;
  final bool? autofocus;
  final EdgeInsetsGeometry? padding;
  final Function()? onTap;
  final Function(String)? onChanged;

  DAPageFilter({
    this.hintText,
    required this.controller,
    this.icon,
    this.autofocus,
    this.padding,
    this.onTap,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    Widget _finder = Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.black
            : Colors.white,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      padding: (this.padding) ?? EdgeInsets.all(12.0),
      child: Container(
        padding: EdgeInsets.all(5),
        decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey.shade700
                : Colors.white,
            borderRadius: BorderRadius.circular(15)),
        child: TextField(
          controller: this.controller,
          autofocus: (this.autofocus) ?? false,
          decoration: InputDecoration(
              border: InputBorder.none,
              prefixIcon: Icon(
                (this.icon) ?? Icons.search,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
              ),
              suffix: GestureDetector(
                onTap: this.onTap,
                child: Container(
                  margin: EdgeInsets.only(right: 10),
                  child: (this.controller.text.length == 0)
                      ? Text('')
                      : Icon(
                          Icons.close,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.grey,
                          size: 20,
                        ),
                ),
              ),
              hintText: this.hintText,
              hintStyle: TextStyle(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.grey,
                  fontSize: 16)),
          onChanged: this.onChanged,
        ),
      ),
    );

    return _finder;
  }
}

// ignore: must_be_immutable
class DAFakeFilter extends StatelessWidget {
  String? label;
  String? hintText;
  final IconData? icon;
  final bool? autofocus;
  final EdgeInsetsGeometry? padding;
  final Function()? onTap;

  DAFakeFilter({
    this.label,
    this.hintText,
    this.icon,
    this.autofocus,
    this.padding,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    TextEditingController _controller = new TextEditingController();

    Widget _label = Container(
      margin: EdgeInsets.only(bottom: 12),
      child: DALabel(
        label: this.label ?? '',
        padding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 0.0),
        style: TextStyle(fontSize: 12, color: Colors.grey),
      ),
    );

    Widget _finder = Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      padding: (this.padding) ??
          EdgeInsets.only(
            left: 20.0,
            right: 20.0,
            bottom: 20.0,
          ),
      child: Container(
        padding: EdgeInsets.all(5),
        decoration: BoxDecoration(
            color: Color.fromRGBO(244, 243, 243, 1),
            borderRadius: BorderRadius.circular(15)),
        child: GestureDetector(
          onTap: this.onTap,
          child: TextField(
            enabled: false,
            controller: _controller,
            autofocus: false,
            decoration: InputDecoration(
                border: InputBorder.none,
                prefixIcon: Icon(
                  (this.icon) ?? Icons.search,
                  color: Colors.black87,
                ),
                hintText: this.hintText,
                hintStyle: TextStyle(color: Colors.grey, fontSize: 16)),
            onChanged: (value) {
              _controller.text = '';
              value = '';
            },
          ),
        ),
      ),
    );

    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Column(
        children: [
          Visibility(
            visible: (this.label == null || this.label == '') ? false : true,
            child: _label,
          ),
          _finder,
        ],
      ),
    );
  }
}

class DADivider extends StatelessWidget {
  DADivider();

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 0,
      thickness: 1,
      indent: 20,
      endIndent: 20,
      color: Theme.of(context).primaryColor,
    );
  }
}

class DALabel extends StatelessWidget {
  final String label;
  final String? leading;
  final String? trailing;
  final TextStyle? style;
  final EdgeInsetsGeometry? padding;

  DALabel({
    required this.label,
    this.leading,
    this.trailing,
    this.style,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    Widget _prefix = Container(
      margin: EdgeInsets.only(right: 8.0),
      child: Text(
        (this.leading) ?? '',
        style: TextStyle(
          color: Colors.black,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );

    Widget _trailing = Container(
      child: Text(
        (this.trailing) ?? '',
        style: TextStyle(
          color: Colors.black54,
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),
    );

    Widget _title = Container(
      width: MediaQuery.of(context).size.width,
      child: Text(
        this.label,
        //overflow: TextOverflow.ellipsis,
        style: (this.style) ??
            TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
      ),
    );

    Widget _label = Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        (this.leading != null) ? _prefix : Container(),
        Expanded(child: _title),
        //_title,
        (this.trailing != null) ? _trailing : Container(),
      ],
    );

    return Container(
      padding: (this.padding) ??
          EdgeInsets.symmetric(
            horizontal: 20.0,
            vertical: 10.0,
          ),
      child: _label,
    );
  }
}

class DaLabelRow extends StatelessWidget {
  final List<DALabel> labels;

  DaLabelRow({
    required this.labels,
  });

  @override
  Widget build(BuildContext context) {
    int _len = (labels.length == 0) ? 1 : labels.length;

    Widget _prefix(String? leading) {
      return Container(
        margin: EdgeInsets.only(right: 8.0),
        child: Text(
          (leading) ?? '',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    Widget _trailing(String? trailing) {
      return Container(
        child: Text(
          (trailing) ?? '',
          style: TextStyle(
            color: Colors.black54,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
      );
    }

    Widget _title(String label, TextStyle? style) {
      return Container(
        width: MediaQuery.of(context).size.width,
        child: Text(
          label,
          overflow: TextOverflow.ellipsis,
          style: (style) ??
              TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
        ),
      );
    }

    Widget rowLabel(
        String label, String? leading, String? trailing, TextStyle? style) {
      return Center(
        child: Container(
          width: (MediaQuery.of(context).size.width - 40) / _len,
          child: Column(
            children: [
              Container(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    _prefix(leading),
                    Expanded(
                      child: _title(label, style),
                    ),
                    _trailing(trailing),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    List<Widget> _listRows = [];
    for (final DALabel _label in labels) {
      _listRows.add(
        rowLabel(
          _label.label,
          _label.leading,
          _label.trailing,
          _label.style,
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 20.0,
        vertical: 10.0,
      ),
      child: Row(
        children: _listRows,
      ),
    );
  }
}

// ignore: must_be_immutable
class DADatePicker extends StatefulWidget {
  final String refID;
  final String? label;
  final EdgeInsetsGeometry? padding;
  final bool? isRequired;
  final DateTime? value;
  final bool? disabled;
  final Function(DateTime) onDateChange;

  DADatePicker({
    required this.refID,
    this.label,
    this.padding,
    this.isRequired,
    this.value,
    this.disabled,
    required this.onDateChange,
  });

  @override
  _DADatePicker createState() => _DADatePicker();
}

class DADateTextFormatter extends TextInputFormatter {
  static const _maxChars = 8;

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    var text = _format(newValue.text, '/');
    return newValue.copyWith(text: text, selection: updateCursorPosition(text));
  }

  String _format(String value, String seperator) {
    value = value.replaceAll(seperator, '');
    var newString = '';

    for (int i = 0; i < min(value.length, _maxChars); i++) {
      newString += value[i];
      if ((i == 1 || i == 3) && i != value.length - 1) {
        newString += seperator;
      }
    }

    return newString;
  }

  TextSelection updateCursorPosition(String text) {
    return TextSelection.fromPosition(TextPosition(offset: text.length));
  }
}

class _DADatePicker extends State<DADatePicker> {
  InputDecoration? _decoration;
  FormFieldValidator<String>? _validator;
  TextEditingController _myFactCtrl = new TextEditingController();
  DateTime selectedDate = DateTime.now();

  DateTime get value {
    return selectedDate;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.value != null && _myFactCtrl.text == '') {
      selectedDate = (widget.value) ?? DateTime.now();
      _myFactCtrl.text = DateFormat('dd/MM/yyyy').format(selectedDate);
    }

    Future<void> _selectDate(BuildContext context) async {
      //selectedDate = (widget.value) ?? DateTime.now();
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: selectedDate, // Refer step 1
        firstDate: DateTime(2016),
        lastDate: DateTime(2035),
      );

      if (picked == null) {
        return;
      }

      selectedDate = picked;
      _myFactCtrl.text = DateFormat('dd/MM/yyyy').format(selectedDate);
      setState(() {});

      await widget.onDateChange(selectedDate);
    }

    Widget _icon = Padding(
      padding: EdgeInsets.only(top: 15), // add padding to adjust icon
      child: Icon(
        Icons.calendar_today,
        color: ((widget.disabled) ?? false) ? Colors.black26 : Colors.black54,
      ),
    );

    _decoration = InputDecoration(
      hintText: widget.label ?? 'Fecha',
      labelText:
          ((widget.isRequired == true) ? '*' : '') + (widget.label ?? 'Fecha'),
      labelStyle: TextStyle(
        color: Colors.black,
      ),
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      errorStyle: TextStyle(
        color: Colors.red, // or any other color
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
      suffixIcon: _icon,
    );

    _validator = (value) {
      if (((widget.isRequired) ?? false) && _myFactCtrl.text == '') {
        return 'Valor requerido';
      }
      return null;
    };

    _getCalendar(BuildContext context) {
      TextFormField defField = TextFormField(
        controller: _myFactCtrl,
        keyboardType: TextInputType.datetime,
        decoration: _decoration,
        validator: _validator,
        maxLines: 1,
        enabled: false,
        style: TextStyle(
            color:
                ((widget.disabled) ?? false) ? Colors.black38 : Colors.black),
        inputFormatters: [DADateTextFormatter()],
      );

      return defField;
    }

    return GestureDetector(
      onTap: () async {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();

        if ((((widget.disabled) ?? false) == false)) {
          await _selectDate(context);
        }
      },
      child: Container(
        padding: widget.padding ?? EdgeInsets.symmetric(horizontal: 20.0),
        child: _getCalendar(context),
      ),
    );
  }
}

// ignore: must_be_immutable
class DAInput extends StatelessWidget {
  String refID;
  final DAInputType? tipo;
  final String? label;
  late TextEditingController controller;
  final FormFieldSetter<String>? onSaved;
  final Function(String)? onChanged;
  final double? padding;
  final TextInputAction? textInputAction;
  final bool? isRequired;
  bool? disabled;
  final Function(String)? onFieldSubmitted;
  final FocusNode? focusNode;
  final IconData? iconAyuda;
  final GestureTapCallback? onAyudaTap;
  final bool? autofocus;
  final Function()? onEditingComplete;
  final Function()? onTap;
  final num? decimales;

  DAInput({
    required this.refID,
    this.tipo,
    this.label,
    required this.controller,
    this.onSaved,
    this.onChanged,
    this.padding,
    this.textInputAction,
    this.isRequired,
    this.disabled,
    this.onFieldSubmitted,
    this.focusNode,
    this.iconAyuda,
    this.onAyudaTap,
    this.autofocus,
    this.onEditingComplete,
    this.onTap,
    this.decimales,
  });

  TextInputType? _keyboardType;
  InputDecoration? _decoration;
  FormFieldValidator<String>? _validator;
  List<TextInputFormatter>? _inputFormatters;
  bool? _obscureText;
  bool _multiLines = false;
  bool _isValid = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: (this.padding) ?? 20.0),
      child: _getInputType(context),
    );
  }

  bool get isValid {
    return _isValid;
  }

  _getInputWidget(BuildContext context) {
    TextFormField defField = TextFormField(
      controller: this.controller,
      keyboardType: _keyboardType,
      decoration: _decoration,
      validator: _validator,
      obscureText: (_obscureText) ?? false,
      textInputAction: (this.textInputAction) ?? null,
      focusNode: this.focusNode,
      onFieldSubmitted: (this.onFieldSubmitted) ?? null,
      onSaved: this.onSaved,
      onChanged: this.onChanged,
      maxLines: (_multiLines) ? null : 1,
      inputFormatters: _inputFormatters,
      enabled: (!((this.disabled) ?? false)) ? true : false,
      autofocus: this.autofocus ?? false,
      onEditingComplete: (this.onEditingComplete) ?? null,
      style: TextStyle(
          color: ((this.disabled) ?? false)
              ? Colors.black38
              : Theme.of(context).textTheme.bodyLarge!.color),
      onTap: this.onTap,
    );

    return defField;
  }

  _getInputType(BuildContext context) {
    switch (this.tipo) {
      case DAInputType.number:
        _number(context);
        break;
      case DAInputType.email:
        _email(context);
        break;
      case DAInputType.notEmail:
        _notEmail(context);
        break;
      case DAInputType.userOrEmail:
        _userOrEmail(context);
        break;
      case DAInputType.url:
        _url(context);
        break;
      case DAInputType.password:
        _password(context);
        break;
      case DAInputType.textarea:
        _textarea(context);
        break;
      case DAInputType.ayuda:
        _ayudaCaptura(context);
        break;
      default:
        _string(context);
        break;
    }

    return _getInputWidget(context);
  }

  bool validarYLimpiarString(String texto) {
    // Elimina los espacios en blanco a la izquierda y derecha
    texto = texto.trim();

    // Verifica si el string contiene el símbolo "@"
    if (texto.contains("@")) {
      // El string contiene una arroba, no permite avanzar
      return false;
    } else {
      // El string no contiene arrobas, se puede avanzar
      return true;
    }
  }

  void _number(BuildContext context) {
    _keyboardType = Platform.isIOS
        ? TextInputType.numberWithOptions(signed: true, decimal: true)
        : TextInputType.numberWithOptions(decimal: true);
    _decoration = InputDecoration(
      hintText: '',
      labelText: ((this.isRequired == true) ? '*' : '') + (this.label ?? ''),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      //errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    String regexPattern =
        '^\\d+\\.?\\d{0,${this.decimales != null ? this.decimales.toString() : '2'}}';

    _inputFormatters = [
      FilteringTextInputFormatter.allow(RegExp(regexPattern))
    ];

    _validator = (value) {
      String pattern = r'[.0-9]';
      RegExp regExp = new RegExp(pattern);
      if (regExp.hasMatch(value!)) {
        if (((this.isRequired) ?? false) && this.controller.text == '') {
          _isValid = false;
          return 'Valor requerido';
        }
        _isValid = true;
        return null;
      } else {
        _isValid = false;
        return 'Número incorrecto';
      }
    };
  }

  void _string(BuildContext context) {
    _decoration = InputDecoration(
      hintText: this.label ?? '',
      labelText: ((this.isRequired == true) ? '*' : '') + (this.label ?? ''),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      //counterText: this.controller.text,
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    _validator = (value) {
      if (((this.isRequired) ?? false) && this.controller.text == '') {
        _isValid = false;
        return 'Valor requerido';
      }

      _isValid = true;
      return null;
    };
  }

  void _textarea(BuildContext context) {
    _keyboardType = TextInputType.multiline;

    _decoration = InputDecoration(
      hintText: this.label ?? '',
      labelText: ((this.isRequired == true) ? '*' : '') + (this.label ?? ''),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    _multiLines = true;

    _validator = (value) {
      if (((this.isRequired) ?? false) && this.controller.text == '') {
        _isValid = false;
        return 'Valor requerido';
      }

      _isValid = true;
      return null;
    };
  }

  void _ayudaCaptura(BuildContext context) {
    Widget _ayuda = GestureDetector(
      onTap: this.onAyudaTap,
      child: Padding(
        padding: EdgeInsets.only(top: 15), // add padding to adjust icon
        child: Icon(
          (this.iconAyuda) ?? Icons.more_vert,
          color: ((this.disabled) ?? false)
              ? Colors.black26
              : Theme.of(context).textTheme.bodyLarge!.color,
        ),
      ),
    );

    _decoration = InputDecoration(
      hintText: this.label ?? '',
      labelText: ((this.isRequired == true) ? '*' : '') + (this.label ?? ''),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
      suffixIcon: _ayuda,
    );

    _validator = (value) {
      if (((this.isRequired) ?? false) && this.controller.text == '') {
        _isValid = false;
        return 'Valor requerido';
      }

      _isValid = true;
      return null;
    };
  }

  void _email(BuildContext context) {
    _keyboardType = TextInputType.emailAddress;

    _decoration = InputDecoration(
      hintText: '<EMAIL>',
      labelText: ((this.isRequired == true) ? '*' : '') +
          (this.label ?? 'Correo Electrónico'),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      counterText: this.controller.text,
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    _validator = (value) {
      String pattern =
          r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
      RegExp regExp = new RegExp(pattern);
      if (regExp.hasMatch(value!)) {
        if (((this.isRequired) ?? false) && this.controller.text == '') {
          _isValid = false;
          return 'Valor requerido';
        }
        _isValid = true;
        return null;
      } else {
        _isValid = false;
        return 'Email incorrecto';
      }
    };
  }

  void _notEmail(BuildContext context) {
    _keyboardType = TextInputType.text;

    _decoration = InputDecoration(
      hintText: 'CLAVE DE USUARIO',
      labelText: ((this.isRequired == true) ? '*' : '') +
          (this.label ?? 'Clave de Usuario'),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      counterText: this.controller.text,
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    _validator = (value) {
      // Eliminar espacios en blanco en la izquierda y derecha
      value = value?.trim();

      // Verificar que no contenga arrobas
      if (value != null && !value.contains('@')) {
        if ((this.isRequired ?? false) && value.isEmpty) {
          _isValid = false;
          return 'Valor requerido';
        }
        _isValid = true;
        return null;
      } else {
        _isValid = false;
        return 'Introduzca unicamente su clave de usuario';
      }
    };
  }

  void _userOrEmail(BuildContext context) {
    _keyboardType = TextInputType.text;

    _decoration = InputDecoration(
      hintText: 'Usuario o Correo Electrónico',
      labelText: ((this.isRequired == true) ? '*' : '') +
          (this.label ?? 'Usuario o Correo Electrónico'),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      counterText: this.controller.text,
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    _validator = (value) {
      if (value == null || value.isEmpty) {
        if (this.isRequired ?? false) {
          _isValid = false;
          return 'Valor requerido';
        }
        _isValid = true;
        return null;
      }

      value = value.trim();

      // Patrón para email
      String emailPattern =
          r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
      RegExp emailRegExp = RegExp(emailPattern);

      // Patrón para usuario (alfanumérico y algunos caracteres especiales)
      String userPattern = r'^[a-zA-Z0-9._-]+$';
      RegExp userRegExp = RegExp(userPattern);

      if (value.contains('@')) {
        // Validar como email
        if (emailRegExp.hasMatch(value)) {
          _isValid = true;
          return null;
        } else {
          _isValid = false;
          return 'Email incorrecto';
        }
      } else {
        // Validar como usuario
        if (userRegExp.hasMatch(value)) {
          _isValid = true;
          return null;
        } else {
          _isValid = false;
          return 'Usuario inválido. Use solo letras, números y los caracteres . _ -';
        }
      }
    };
  }

  void _url(BuildContext context) {
    _keyboardType = TextInputType.url;

    _decoration = InputDecoration(
      hintText: 'http://127.0.0.1',
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      labelText: ((this.isRequired == true) ? '*' : '') + (this.label ?? 'Url'),
      counterText: this.controller.text,
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    _validator = (value) {
      String pattern = r'(?:(?:https?|ftp):\/\/)?[\w/\-?=%.]+\.[\w/\-?=%.]+';
      RegExp regExp = new RegExp(pattern);
      if (regExp.hasMatch(value!)) {
        if (((this.isRequired) ?? false) && this.controller.text == '') {
          _isValid = false;
          return 'Valor requerido';
        }
        _isValid = true;
        return null;
      } else {
        _isValid = false;
        return 'url inválida';
      }
    };
  }

  void _password(BuildContext context) {
    _obscureText = true;

    _decoration = InputDecoration(
      // icon: Icon(Icons.lock_outline, color: Theme.of(context).primaryColor),
      hintText: '',
      labelText:
          ((this.isRequired == true) ? '*' : '') + (this.label ?? 'Contraseña'),
      labelStyle: TextStyle(
        color: (this.disabled ?? false)
            ? Colors.grey
            : Theme.of(context).textTheme.bodyLarge!.color,
      ),
      counterText: this.controller.text.length.toString(),
      errorText: null,
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );

    _validator = (value) {
      if (((this.isRequired) ?? false) && this.controller.text == '') {
        _isValid = false;
        return 'Valor requerido';
      } else if (value!.length >= 3) {
        _isValid = true;
        return null;
      } else {
        _isValid = false;
        return 'Requiere mas de 3 caracteres';
      }
    };
  }
}

// ignore: must_be_immutable
class DADropdown extends StatelessWidget {
  String refID;
  List<dynamic> data;
  late dynamic controllerValue;
  final FormFieldSetter<String>? onSaved;
  final String? inputLabel;
  final String value;
  final String text;
  final FormFieldSetter<String>? onChanged;
  final double? padding;
  final FocusNode? focusNode;
  bool? isRequired = false;
  bool? disabled = false;

  DADropdown({
    required this.refID,
    required this.data,
    required this.controllerValue,
    required this.onSaved,
    this.inputLabel,
    required this.value,
    required this.text,
    this.onChanged,
    this.padding,
    this.focusNode,
    this.isRequired,
    this.disabled,
  });

  bool _isValid = true;

  @override
  Widget build(BuildContext context) {
    try {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: (this.padding) ?? 20.0),
        child: _getDropdown(context),
      );
    } catch (e) {
      print('DADropdown: ' + e.toString());
      return Container();
    }
  }

  bool get isValid {
    return _isValid;
  }

  _getDropdown(BuildContext context) {
    int _validValue = 0;
    List<String> _forDuplicated = [];
    bool _isDuplicated = false;

    _fixDropDown(Map<String, dynamic> value) {
      _isDuplicated =
          (_forDuplicated.indexOf(value[this.value].toString()) >= 0);

      if (value[this.value].toString() == this.controllerValue) {
        _validValue += 1;
      }
      _forDuplicated.add(value[this.value].toString());
    }

    this.data.forEach((value) => _fixDropDown(value));

    if (_isDuplicated) {
      return Text(this.inputLabel! + ' Datos Inválidos');
    }

    FormFieldValidator<String>? _validator = (value) {
      if (value == null && ((this.isRequired) ?? false)) {
        _isValid = false;
        return 'Valor requerido';
      }

      _isValid = true;
      return null;
    };

    Widget _dropdown = DropdownButtonFormField<String>(
      isExpanded: true,
      validator: _validator,
      value: (_validValue == 1) ? this.controllerValue : null,
      focusNode: this.focusNode,
      icon: Padding(
        padding: const EdgeInsetsDirectional.only(end: 12.0),
        child: const Icon(Icons.expand_more), // myIcon is a 48px-wide widget.
      ),
      decoration: InputDecoration(
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: Color.fromRGBO(0, 0, 0, 0.1)),
        ),
        labelText:
            ((this.isRequired == true) ? '*' : '') + ((this.inputLabel) ?? ''),
        labelStyle: TextStyle(
          color: (this.disabled == true)
              ? Theme.of(context).disabledColor
              : Theme.of(context).textTheme.bodyLarge!.color,
        ),
      ),
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      onChanged: ((this.disabled ?? false) == true)
          ? null
          : ((this.onChanged) ??
              (String? newValue) {
                this.controllerValue = newValue;
              }),
      onSaved: this.onSaved,
      items: this.data.map<DropdownMenuItem<String>>((dynamic value) {
        return DropdownMenuItem<String>(
          value: value[this.value].toString(),
          child: Text(
            value[this.text].toString(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
    );

    return _dropdown;
  }
}

// ignore: must_be_immutable
class DARadioList extends StatefulWidget {
  List<dynamic> data;
  late dynamic controllerValue;
  final String? inputLabel;
  final String value;
  final String text;
  final double? padding;
  final FocusNode? focusNode;
  final FormFieldSetter<String>? onChanged;

  DARadioList({
    required this.data,
    required this.controllerValue,
    this.inputLabel,
    required this.value,
    required this.text,
    this.padding,
    this.focusNode,
    this.onChanged,
  });

  @override
  _DARadioList createState() => _DARadioList();
}

class _DARadioList extends State<DARadioList> {
  var controllerValue;

  @override
  Widget build(BuildContext context) {
    try {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: (widget.padding) ?? 20.0),
        child: _getRadioList(context),
      );
    } catch (e) {
      print('DARadioList: ' + e.toString());
      return Container();
    }
  }

  _getRadioList(BuildContext context) {
    List<String> _forDuplicated = [];
    bool _isDuplicated = false;

    if (this.controllerValue == null || this.controllerValue == '')
      this.controllerValue = widget.data[0][widget.value];

    _fixData(Map<String, dynamic> value) {
      _isDuplicated = (_forDuplicated.indexOf(value[widget.value]) >= 0);
      _forDuplicated.add(value[widget.value]);
    }

    widget.data.forEach((value) => _fixData(value));

    if (_isDuplicated) {
      return Text(widget.inputLabel! + ' Datos Inválidos');
    }

    Widget _radioList = Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: widget.data.map<Container>((dynamic value) {
        RadioListTile<String> _element = RadioListTile<String>(
          title: Text(
            value[widget.text].toString(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          activeColor: Theme.of(context).primaryColor,
          value: value[widget.value].toString(),
          groupValue: widget.controllerValue,
          onChanged: (String? newValue) {
            widget.controllerValue = newValue;
            setState(() {});
            return widget.onChanged!(newValue);
          },
        );

        return Container(child: _element);
      }).toList(),
    );

    return _radioList;
  }
}

// ignore: must_be_immutable
class DACheckbox extends StatefulWidget {
  final String label;
  late bool controller;
  final double? padding;
  final FocusNode? focusNode;
  final Function(bool)? onChanged;
  bool? disabled;

  DACheckbox({
    required this.label,
    required this.controller,
    this.padding,
    this.focusNode,
    this.onChanged,
    this.disabled,
  });

  @override
  _DACheckboxState createState() => _DACheckboxState();
}

class _DACheckboxState extends State<DACheckbox> {
  bool checkValue = false;

  @override
  void initState() {
    super.initState();
    checkValue = widget.controller;
  }

  @override
  Widget build(BuildContext context) {
    try {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: (widget.padding) ?? 20.0),
        child: _getCheckbox(context),
      );
    } catch (e) {
      print('DACheckbox: ' + e.toString());
      return Container();
    }
  }

  _getCheckbox(BuildContext context) {
    bool isDisabled = widget.disabled ?? false;

    List<CheckboxListTile> checkbox = [
      CheckboxListTile(
        title: Text(
          widget.label.toString(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        activeColor: Theme.of(context).primaryColor,
        value: widget.controller,
        onChanged: isDisabled
            ? null
            : (bool? checked) {
                setState(() {
                  if (checked!) {
                    widget.controller = true;
                  } else {
                    widget.controller = false;
                  }
                });
                widget.onChanged!(widget.controller);
              },
      ),
    ];

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: checkbox.toList(),
    );
  }
}

class DAButton extends StatelessWidget {
  final String label;
  final Function()? onPressed;

  DAButton({
    required this.label,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final w = MediaQuery.of(context).size.width;

    var btn = Container(
      padding: EdgeInsets.symmetric(horizontal: 20.0),
      child: MaterialButton(
        color: Colors.white,
        minWidth: w,
        height: 50.0,
        padding: EdgeInsets.zero,
        child: Container(
          width: w,
          height: 50.0,
          decoration: BoxDecoration(
            gradient: DAGradients.primary(context),
            borderRadius: BorderRadius.circular(5.0),
          ),
          child: Center(
            child: Text(
              this.label.toUpperCase(),
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white, fontSize: 14.0),
            ),
          ),
        ),
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        onPressed: this.onPressed,
      ),
    );

    return btn;
  }
}

class DAMapLink extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry? padding;

  DAMapLink({
    required this.title,
    this.subtitle,
    this.icon,
    this.onPressed,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    Widget _lugar = Row(
      children: [
        Container(
          padding: (this.padding) ??
              EdgeInsets.symmetric(
                horizontal: 20.0,
                vertical: 10.0,
              ),
          alignment: Alignment.topLeft,
          width: MediaQuery.of(context).size.width - 80,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DALabel(
                label: this.title,
                padding: EdgeInsets.all(0.0),
              ),
              Container(
                margin: EdgeInsets.only(top: 5.0),
                child: Text(
                  (this.subtitle) ?? '',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
            width: 70,
            child: RawMaterialButton(
              padding: EdgeInsets.all(5),
              shape: CircleBorder(),
              child: Icon(
                (this.icon) ?? Icons.directions,
                size: 40.0,
                color: Colors.blue,
              ),
              //onPressed: goMaps,
              onPressed: (this.onPressed) ?? () {},
            )),
      ],
    );

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 0),
      child: _lugar,
    );
  }
}

// ignore: must_be_immutable
class DATitleMinPrimary extends StatelessWidget {
  final String? header;
  final String title;
  EdgeInsetsGeometry? padding;

  DATitleMinPrimary({
    this.header,
    required this.title,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    Widget _primaryTitle = Container(
      padding: (this.padding) ??
          EdgeInsets.symmetric(
            horizontal: 20.0,
            vertical: 10.0,
          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            (this.header) ?? '',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          SizedBox(
            height: 10.0,
          ),
          Text(
            this.title,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );

    return _primaryTitle;
  }
}

class DABigMenuButton extends StatelessWidget {
  final String label;
  final String? image;
  final String? heroTag;
  final Widget? trailing;
  final Function()? onPressed;
  final bool isDisabled;
  final double opacity;

  DABigMenuButton({
    required this.label,
    this.image,
    this.heroTag,
    this.trailing,
    this.onPressed,
    this.isDisabled = false,
    this.opacity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    if (this.isDisabled) {
      return Container();
    }
    final w = MediaQuery.of(context).size.width;

    Widget _image = Container(
      alignment: Alignment.centerLeft,
      width: 80, //((w * .2) < 80) ? 80 : (w * .2),
      //color: Theme.of(context).primaryColor,
      child: SizedBox(
        child: Image.asset('assets/app/' + ((this.image) ?? 'login') + '.png'),
      ),
    );

    String _heroTag = (this.heroTag) ?? '';

    Widget _hero = (_heroTag == '')
        ? _image
        : Hero(
            tag: _heroTag,
            child: _image,
          );

    Widget _imageBtn = (this.image == null || (this.image) == '')
        ? Container(
            color: Theme.of(context).primaryColor,
            width: 2,
          )
        : _hero;

    var btn = Container(
      margin: EdgeInsets.symmetric(
        horizontal: (30.0),
        vertical: 10,
      ),
      child: MaterialButton(
        color: Colors.white.withValues(alpha: opacity),
        minWidth: w,
        height: 80.0,
        padding: EdgeInsets.zero,
        child: Container(
          width: w,
          height: 80.0,
          child: Center(
            child: Row(
              children: <Widget>[
                _imageBtn,
                Container(
                  width: 2,
                  color: Theme.of(context).primaryColor,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(right: 5),
                    child: Text(
                      this.label,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          color: Colors.grey.shade800, fontSize: 14.0),
                    ),
                  ),
                ),
                Visibility(
                  visible: (this.trailing != null),
                  child: Container(
                    width: 80,
                    child: this.trailing,
                  ),
                ),
                SizedBox(width: 5),
              ],
            ),

            // (this.label == 'Recepción de Tránsito' ||
            //         this.label == 'Órden de Surtido')
            //     ? _btnInside1
            //     : _btnInside2,
          ),
        ),
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.0),
        ),
        onPressed: this.onPressed,
      ),
    );

    return btn;
  }
}

class DABackButton extends StatelessWidget {
  final String? title;

  DABackButton({
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    final w = MediaQuery.of(context).size.width;

    Widget _button = SafeArea(
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.only(left: 12.0),
        child: Container(
          alignment: Alignment.centerLeft,
          color: Colors.transparent,
          height: 52.0,
          width: 24.0,
          child: MaterialButton(
            color: Colors.transparent,
            height: 52.0,
            minWidth: 32.0,
            padding: EdgeInsets.zero,
            child: Container(
              padding: EdgeInsets.only(top: 8.0),
              color: Colors.transparent,
              alignment: Alignment.centerLeft,
              height: 36.0,
              width: (this.title == null) ? 32.0 : (w - 150),
              //width: 40.0,
              child: Row(
                children: [
                  Icon(Icons.arrow_back_ios),
                  (this.title == null)
                      ? Container()
                      : Container(
                          child: Text(
                            (this.title) ?? '',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                ],
              ),
            ),
            elevation: 0,
            onPressed: () => Navigator.pop(context, false),
          ),
        ),
      ),
    );

    return _button;
  }
}

class DAPopUpMenu extends StatelessWidget {
  final List<PopupMenuItem> options;
  final double? right;
  final double? top;
  final double? height;
  final double? width;
  final EdgeInsetsGeometry? margin;

  DAPopUpMenu({
    required this.options,
    this.right,
    this.top,
    this.height,
    this.width,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    Widget _popBtn = Positioned(
      right: this.right ?? 10.0,
      top: this.top ?? 3.0,
      child: Container(
        height: this.height ?? 32,
        width: this.width ?? 32,
        alignment: Alignment.topRight,
        margin: this.margin ?? EdgeInsets.only(top: 45.0, right: 50.0),
        child: PopupMenuButton(
          itemBuilder: (context) => this.options,
        ),
      ),
    );

    return _popBtn;
  }
}

class DABottomAppBar extends StatelessWidget {
  final List<Widget> children;

  DABottomAppBar({
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> _def = [SizedBox(width: 50.0, height: 36.0)];

    return BottomAppBar(
      child: Container(
        margin: EdgeInsets.only(left: 12.0, right: 12.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: (this.children.length == 0) ? _def : this.children,
        ),
      ),
      shape: CircularNotchedRectangle(),
      color: Theme.of(context).brightness == Brightness.dark
          ? Colors.grey.shade800
          : Colors.white,
    );
  }
}

class DABottomAppBarButton extends StatelessWidget {
  final String? label;
  final GestureTapCallback? onTap;
  final IconData? icon;

  DABottomAppBarButton({
    this.label,
    this.onTap,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    Widget _btn = Padding(
      padding: const EdgeInsets.all(4.0),
      child: InkWell(
        onTap: this.onTap,
        child: Container(
          padding: EdgeInsets.all(8.0),
          child: Row(
            children: [
              (this.icon != null)
                  ? Icon((this.icon) ?? Icons.save)
                  : Visibility(
                      visible: false,
                      child: Container(),
                    ),
              (this.icon != null)
                  ? SizedBox(width: 5.0)
                  : Visibility(
                      visible: false,
                      child: Container(),
                    ),
              Visibility(
                visible: (this.label != null),
                child: Text(
                  ((this.label) ?? ''),
                  style: TextStyle(
                    fontSize: 16,
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );

    return _btn;
  }
}

class DABottomAppBarIcon extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;

  DABottomAppBarIcon({
    required this.icon,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: IconButton(
        iconSize: 32.0,
        icon: Icon(this.icon),
        onPressed: this.onPressed,
      ),
    );
  }
}

class DAFinderCard extends StatelessWidget {
  final String? title;
  final List<Widget>? bodyColumn;
  final String? leading;
  final String? trailing;
  final String? icon;
  final bool? checked;
  final Function(dynamic)? onTap;
  final String? metadata;

  DAFinderCard({
    this.title,
    this.bodyColumn,
    this.leading,
    this.trailing,
    this.icon,
    this.checked,
    this.onTap,
    this.metadata,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    Widget title = Container(
      padding: EdgeInsets.only(left: 2),
      width: size.width - 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          (this.icon == null)
              ? Container()
              : Container(
                  margin: EdgeInsets.only(right: 8.0),
                  child: Text(
                    (this.icon) ?? 'home',
                    style: TextStyle(
                      fontFamily: 'MaterialIcons',
                      fontSize: 20,
                      color: Colors.black54,
                    ),
                  ),
                ),
          Container(
            width: size.width - 42 - ((this.icon == null) ? 0 : 40),
            child: Text(
              (this.title) ?? '{ Title }',
              maxLines: 2,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 18.0,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );

    Widget body = Padding(
      padding: EdgeInsets.only(
          top: ((this.bodyColumn == null) ? 0 : 8.0), left: 5.0, bottom: 0),
      child: Container(
        width: size.width - 50,
        child: (this.bodyColumn == null)
            ? Container()
            : Column(
                mainAxisSize: MainAxisSize.max,
                children: (this.bodyColumn) ?? [],
              ),
      ),
    );

    Text _textAuxiliar = Text(
      (this.leading) ?? '',
      maxLines: 1,
      style: TextStyle(
        fontSize: 10,
        color: (this.checked ?? false)
            ? Theme.of(context).primaryColor
            : Colors.black,
      ),
      overflow: TextOverflow.ellipsis,
    );

    Widget auxiliar = Container(
      width: size.width - 48,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8.0, left: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            (this.checked ?? false)
                ? badges.Badge(
                    position:
                        badges.BadgePosition.topStart(top: -8, start: -18),
                    badgeStyle: badges.BadgeStyle(
                      badgeColor: Theme.of(context).primaryColor,
                    ),
                    badgeContent: Icon(
                      Icons.done,
                      color: Colors.white,
                      size: 8.0,
                    ),
                    child: _textAuxiliar,
                  )
                : _textAuxiliar,
            Container(
              //width: (size.width / 2) - 100,
              child: Text(
                (this.trailing) ?? '',
                maxLines: 1,
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 10,
                ),
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.right,
              ),
            ),
          ],
        ),
      ),
    );

    Widget row = Row(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.only(left: 8.0, top: 5.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                (this.leading == null && this.trailing == null)
                    ? Container()
                    : auxiliar,
                title,
                body,
              ],
            ),
          ),
        ),
      ],
    );

    Widget card = Container(
      margin: EdgeInsets.symmetric(
        horizontal: 5.0,
        //vertical: 2.0,
      ),
      width: size.width - 60,
      decoration: BoxDecoration(
        gradient: DAGradients.white(),
        borderRadius: BorderRadius.all(
          Radius.circular(15.0),
        ),
        boxShadow: [
          BoxShadow(
            blurRadius: 10.0,
            spreadRadius: 2.0,
            color: Colors.black12.withValues(alpha: 0.08),
          )
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 12.0),
        child: row,
      ),
    );

    Widget gesture = GestureDetector(
      child: card,
      onTap: () => this.onTap!(this.metadata),
    );

    return (this.metadata != null && this.metadata != '' && this.onTap != null)
        ? gesture
        : card;
  }
}

class DACard extends StatelessWidget {
  final String? heroTag;
  final String? title;
  final String? subtitle;
  final String? trailing;
  final String? leading;
  final String? icon;
  final EdgeInsetsGeometry? padding;
  final String? metadata;

  DACard({
    required this.heroTag,
    required this.title,
    this.subtitle,
    this.trailing,
    this.leading,
    this.icon,
    this.padding,
    this.metadata,
  });

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    Random random = new Random();
    int _randomNumber = random.nextInt(100);

    Widget svgContainer = (this.icon == null)
        ? Container()
        : DAImagesType.getSvgCard(context, icon: this.icon);
    Gradient _gradient = DAGradients.primary(context);

    Widget title = Container(
      //padding: EdgeInsets.only(left: 5),
      width: size.width - 200,
      child: Text(
        (this.title) ?? 'Cargando...',
        maxLines: 2,
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 14.0,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );

    Widget subtitle = Padding(
      padding: const EdgeInsets.only(top: 8.0, left: 0, bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            width: size.width - 200,
            child: Text(
              (this.subtitle) ?? 'SubTítulo',
              maxLines: 1,
              style: TextStyle(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.grey[600],
                fontSize: 10,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );

    Widget auxiliar = (this.leading == null && this.trailing == null)
        ? Container()
        : SizedBox(
            width: double.infinity,
            //width: (size.width * .6),
            //color: Colors.red,
            //padding: const EdgeInsets.only(bottom: 8.0, left: 5.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  (this.leading) ?? '',
                  maxLines: 1,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 10,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Container(
                  //width: (size.width / 2) - 100,
                  child: Text(
                    (this.trailing) ?? '',
                    maxLines: 1,
                    style: TextStyle(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.grey.shade600,
                      fontSize: 10,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          );

    Widget row = Container(
      width: double.infinity,
      child: Row(
        children: [
          svgContainer,
          Container(
            height: (this.icon == null) ? 65.0 : 90.0,
            width: 2.5,
            margin: EdgeInsets.only(right: 8.0),
            decoration: BoxDecoration(
              gradient: _gradient,
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 4,
                vertical: 8,
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    auxiliar,
                    SizedBox(height: 8.0),
                    title,
                    (this.subtitle == null) ? Container() : subtitle,
                  ],
                ),
              ),
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade700
                  : Color.fromRGBO(244, 243, 243, 1),
            ),
          ),
          SizedBox(
            width: 8.0,
          ),
        ],
      ),
    );

    var card = Hero(
      tag: (this.heroTag) ?? _randomNumber.toString(),
      child: Container(
        margin:
            (this.padding) ?? EdgeInsets.symmetric(vertical: 5, horizontal: 20),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade700
              : Color.fromRGBO(244, 243, 243, 1),
          borderRadius: BorderRadius.all(Radius.circular(15.0)),
          boxShadow: [
            BoxShadow(
              blurRadius: 10.0,
              spreadRadius: 2.0,
              color: Colors.black12.withValues(alpha: 0.03),
            )
          ],
        ),
        child: row,
      ),
    );

    return SizedBox(
      width: double.infinity,
      child: card,
    );
  }
}

class DACardList extends StatelessWidget {
  final List<dynamic> data;
  final DADefListModel config;
  final FormFieldSetter<DADefListModel>? onTap;
  final String? tipoMsg;

  DACardList({
    required this.data,
    required this.config,
    this.onTap,
    this.tipoMsg,
  });

  @override
  Widget build(BuildContext context) {
    _onTap(value) {
      this.onTap!(value);
    }

    _tarjeta(DADefListModel card) {
      Random random = new Random();
      int _randomNumber = random.nextInt(100000);
      //String _tag = 'effect-' + ((card.id == null) ? _randomNumber.toString() : card.id.toString());
      String _tag = 'effect-' + _randomNumber.toString();

      Widget _card = GestureDetector(
        child: DACard(
          leading: card.leading.toString(),
          trailing: card.trailing.toString(),
          title: card.title.toString(),
          subtitle: card.subtitle.toString(),
          metadata: card.metadata.toString(),
          icon: this.config.icon,
          heroTag: _tag,
        ),
        onTap: () async => _onTap(card),
      );

      return badges.Badge(
        badgeStyle: badges.BadgeStyle(
          badgeColor: Theme.of(context).primaryColor,
          elevation: 0,
        ),
        showBadge: card.showBadge == 'true' ? true : false,
        //padding: EdgeInsets.all(0),
        position: badges.BadgePosition.topStart(
          start: 12,
          top: 0,
        ),
        badgeContent: (card.changed == true)
            ? Container(
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                  gradient: DAGradients.primary(context),
                  borderRadius: BorderRadius.circular(5.0),
                ),
                child: SizedBox(
                  height: 5,
                  width: 5,
                ),
              )
            : Container(),
        child: _card,
      );
    }

    if (this.data.length > 0) {
      Widget list = ListView.builder(
        padding: EdgeInsets.all(0.0),
        physics: NeverScrollableScrollPhysics(),
        itemCount: this.data.length,
        itemBuilder: (BuildContext context, int index) {
          dynamic _dataCard = this.data[index];
          DADefListModel card = new DADefListModel(changed: false);

          if (_dataCard.containsKey(this.config.id)) {
            card.id = _dataCard[this.config.id].toString();
          }

          if (_dataCard.containsKey(this.config.title)) {
            card.title = _dataCard[this.config.title].toString();
          }

          if (_dataCard.containsKey(this.config.subtitle)) {
            card.subtitle = _dataCard[this.config.subtitle].toString();
          }

          if (_dataCard.containsKey(this.config.leading)) {
            card.leading = _dataCard[this.config.leading].toString();
          }

          if (_dataCard.containsKey(this.config.trailing)) {
            card.trailing = _dataCard[this.config.trailing].toString();
          }

          if (_dataCard.containsKey(this.config.showBadge)) {
            card.showBadge = _dataCard[this.config.showBadge].toString();
          }

          card.metadata = json.encode(_dataCard);

          return _tarjeta(card);
        },
        shrinkWrap: true,
      );

      return list;
    }

    return DANoData(tipo: this.tipoMsg);
  }
}

class DACardPageList extends StatelessWidget {
  final List<dynamic> data;
  final DADefListModel config;
  final FormFieldSetter<dynamic>? onTap;
  final String? heroTag;
  final String? tipoMsg;
  final PageController? animationController;

  DACardPageList({
    required this.data,
    required this.config,
    this.onTap,
    this.heroTag,
    this.tipoMsg,
    this.animationController,
  });

  @override
  Widget build(BuildContext context) {
    PageController _defPageController =
        new PageController(initialPage: 1, viewportFraction: 0.8);

    tarjeta(dynamic cardData) {
      Random random = new Random();
      int _randomNumber = random.nextInt(100);
      String _tagHero =
          (this.heroTag) ?? 'effectmap-' + _randomNumber.toString();

      Widget _card = GestureDetector(
        child: DACard(
          title: cardData[this.config.title],
          subtitle: cardData[this.config.subtitle],
          leading: cardData[this.config.leading],
          trailing: cardData[this.config.trailing],
          padding: EdgeInsets.all(0.0),
          heroTag: _tagHero,
        ),
        onTap: () async => this.onTap!(cardData),
      );

      return SizedBox(
        width: double.infinity,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 5.0),
          child: _card,
        ),
      );
    }

    locationsList(index) {
      try {
        if (this.animationController == null) {
          return tarjeta(this.data[index]);
        } else {
          return AnimatedBuilder(
            animation: this.animationController!,
            builder: (BuildContext context, Widget? widget) {
              double value = 1;
              if (this.animationController!.position.haveDimensions) {
                value = (this.animationController!.page! - index);
                value = (1 - (value.abs() * 0.3) + 0.06).clamp(0.0, 1.0);
              }
              return (widget) ?? Text('nulo');
            },
            child: tarjeta(this.data[index]),
          );
        }
      } catch (e) {
        print('_locationsList: ' + e.toString());
        return Visibility(visible: false, child: Container());
      }
    }

    cardBuilder() {
      Widget cars = PageView.builder(
        controller: (this.animationController) ?? _defPageController,
        itemCount: this.data.length,
        itemBuilder: (BuildContext context, int index) {
          try {
            return locationsList(index);
          } catch (e) {
            print(e.toString());
            return Container();
          }
        },
      );

      return cars;
    }

    try {
      Widget _list = Container(
        height: 120,
        margin: EdgeInsets.only(top: MediaQuery.of(context).size.height - 150),
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            Expanded(
              child: Align(
                alignment: FractionalOffset.bottomCenter,
                child: Container(
                  margin: EdgeInsets.only(bottom: 20.0),
                  height: 90.0,
                  width: MediaQuery.of(context).size.width,
                  child: cardBuilder(),
                ),
              ),
            ),
          ],
        ),
      );

      return _list;
    } catch (e) {
      print('tarjetasMapa: ' + e.toString());
      return DANoData(tipo: this.tipoMsg);
    }
  }
}

class DAWidgetPageList extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry? daScreenAlignment;
  final double? viewportFraction;

  DAWidgetPageList({
    required this.children,
    this.daScreenAlignment,
    this.viewportFraction,
  });

  @override
  Widget build(BuildContext context) {
    try {
      widgetBuilder() {
        PageController widgetsCtrl = PageController(
            initialPage: (this.children.length > 0) ? 1 : 0,
            viewportFraction: (this.viewportFraction) ?? 0.82);

        Widget cars = PageView.builder(
          controller: widgetsCtrl,
          itemCount: this.children.length,
          itemBuilder: (BuildContext context, int index) {
            try {
              return this.children[index];
            } catch (e) {
              print(e.toString());
              return Container();
            }
          },
        );
        return cars;
      }

      Widget _list = Container(
        //color: Colors.green,
        height: 120,
        margin: (this.daScreenAlignment ?? DAScreenAlignment.bottom)
            as EdgeInsetsGeometry?,
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            Expanded(
              child: Align(
                alignment: FractionalOffset.center,
                child: Container(
                  height: 120.0,
                  width: (MediaQuery.of(context).size.width * .9),
                  child: widgetBuilder(),
                ),
              ),
            ),
          ],
        ),
      );

      return _list;
    } catch (e) {
      print('tarjetasMapa: ' + e.toString());
      return Container();
    }
  }
}

class DALabelList extends StatelessWidget {
  final Map<String, dynamic> data;
  final List<DADefListModel> config;

  DALabelList({
    required this.data,
    required this.config,
  });

  @override
  Widget build(BuildContext context) {
    _fixData(String? key) {
      if (key == null) {
        return key;
      }

      if (key.indexOf('{{') >= 0 && key.indexOf('}}') >= 0) {
        key =
            this.data[key.replaceAll('{{', '').replaceAll('}}', '')].toString();
      }
      return key;
    }

    if (this.config.length > 0) {
      Widget list = ListView.builder(
        padding: EdgeInsets.all(0.0),
        physics: NeverScrollableScrollPhysics(),
        itemCount: this.config.length,
        itemBuilder: (BuildContext context, int index) {
          DADefListModel _dataLabel = this.config[index];

          DADefListModel label = new DADefListModel();

          if (_dataLabel.leading != null) {
            label.leading = _fixData(_dataLabel.leading);
          }

          if (_dataLabel.title != null) {
            label.title = _fixData(_dataLabel.title);
          }

          if (_dataLabel.trailing != null) {
            label.trailing = _fixData(_dataLabel.trailing);
          }

          Widget _label = new DALabel(
            leading: label.leading,
            label: (label.title) ?? '',
            trailing: label.trailing,
          );

          return _label;
        },
        shrinkWrap: true,
      );

      return list;
    }

    return Container();
  }
}

class DAInputList extends StatelessWidget {
  final List<dynamic> data;
  final DADefListModel config;
  final FormFieldSetter<DADefListModel>? onTap;
  final String? tipoMsg;

  DAInputList({
    required this.data,
    required this.config,
    this.onTap,
    this.tipoMsg,
  });

  @override
  Widget build(BuildContext context) {
    _onTap(value) {
      this.onTap!(value);
    }

    _tarjeta(DADefListModel card) {
      Random random = new Random();
      int _randomNumber = random.nextInt(1000);
      //String _tag = 'effect-' + ((card.id) ?? _randomNumber.toString());
      String _tag = 'effect-' + _randomNumber.toString();

      Widget _card = GestureDetector(
        child: DACard(
          leading: card.leading,
          trailing: card.trailing,
          title: card.title,
          subtitle: card.subtitle,
          icon: this.config.icon,
          heroTag: _tag,
        ),
        onTap: () async => _onTap(card),
      );

      return badges.Badge(
        badgeStyle: badges.BadgeStyle(
          badgeColor: Theme.of(context).primaryColor,
          elevation: 0,
        ),
        showBadge: card.showBadge == 'true' ? true : false,
        //padding: EdgeInsets.all(0),
        position: badges.BadgePosition.topStart(
          start: 12,
          top: 0,
        ),
        badgeContent: (card.changed == true)
            ? Container(
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                  gradient: DAGradients.primary(context),
                  borderRadius: BorderRadius.circular(5.0),
                ),
                child: SizedBox(
                  height: 5,
                  width: 5,
                ),
              )
            : Container(),
        child: _card,
      );
    }

    if (this.data.length > 0) {
      Widget list = ListView.builder(
        padding: EdgeInsets.all(0.0),
        physics: NeverScrollableScrollPhysics(),
        itemCount: this.data.length,
        itemBuilder: (BuildContext context, int index) {
          dynamic _dataCard = this.data[index];
          DADefListModel card = new DADefListModel(changed: false);

          if (_dataCard.containsKey(this.config.id)) {
            card.id = _dataCard[this.config.id];
          }

          if (_dataCard.containsKey(this.config.title)) {
            card.title = _dataCard[this.config.title];
          }

          if (_dataCard.containsKey(this.config.subtitle)) {
            card.subtitle = _dataCard[this.config.subtitle];
          }

          if (_dataCard.containsKey(this.config.leading)) {
            card.leading = _dataCard[this.config.leading];
          }

          if (_dataCard.containsKey(this.config.trailing)) {
            card.trailing = _dataCard[this.config.trailing];
          }

          if (_dataCard.containsKey(this.config.showBadge)) {
            card.showBadge = _dataCard[this.config.showBadge].toString();
          }

          return _tarjeta(card);
        },
        shrinkWrap: true,
      );

      return list;
    }

    return DANoData(tipo: this.tipoMsg);
  }
}

class DAFloatingActionButton extends StatelessWidget {
  final IconData icon;
  final Function()? onPressed;
  final String heroTag;

  DAFloatingActionButton({
    required this.icon,
    this.onPressed,
    required this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    var btn = FloatingActionButton(
      elevation: 0,
      heroTag: this.heroTag,
      child: Container(
        width: 60,
        height: 60,
        child: Icon(
          this.icon,
          color: Colors.white,
          //size: 32,
        ),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: (this.onPressed == null)
              ? DAGradients.disabled()
              : DAGradients.primary(context),
        ),
      ),
      onPressed: onPressed,
    );
    return btn;
  }
}

class DANoData extends StatelessWidget {
  final String? tipo;
  final bool? replaceAll;

  DANoData({
    this.tipo,
    this.replaceAll,
  });

  @override
  Widget build(BuildContext context) {
    Widget noData = Container(
      margin: EdgeInsets.only(top: 50),
      child: Center(
        child: Text(
          (replaceAll == null || replaceAll == false)
              ? 'Sin ${tipo ?? 'registros'} disponibles'
              : (this.tipo) ?? '',
          style: TextStyle(color: Colors.grey),
        ),
      ),
    );

    return noData;
  }
}

// ignore: must_be_immutable
class DAOnbordingPage extends StatefulWidget {
  List<DAUnbordingContent> contents;
  Route<dynamic> nextPage; // MaterialPageRoute(builder: (_) => DALoginPage(),)

  DAOnbordingPage({
    required this.contents,
    required this.nextPage,
  });

  @override
  _DAOnbordingPageState createState() => _DAOnbordingPageState();
}

class _DAOnbordingPageState extends State<DAOnbordingPage> {
  int currentIndex = 0;
  late PageController _controller;

  @override
  void initState() {
    _controller = PageController(initialPage: 0);
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _controller,
              itemCount: widget.contents.length,
              onPageChanged: (int index) {
                setState(() {
                  currentIndex = index;
                });
              },
              itemBuilder: (_, i) {
                return Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10),
                        child: SvgPicture.asset(
                          widget.contents[i].image,
                          height: 280,
                        ),
                      ),
                      Text(
                        widget.contents[i].title,
                        maxLines: 2,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 20),
                      Container(
                        height: 180,
                        child: SingleChildScrollView(
                          child: Text(
                            widget.contents[i].discription,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                );
              },
            ),
          ),
          Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                widget.contents.length,
                (index) => buildDot(index, context),
              ),
            ),
          ),
          Container(
            height: 60,
            margin: EdgeInsets.all(40),
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: DAGradients.primary(context),
              borderRadius: BorderRadius.circular(5.0),
            ),
            child: TextButton(
              child: Text(currentIndex == widget.contents.length - 1
                  ? "Continuar"
                  : "Siguiente"),
              onPressed: () {
                if (currentIndex == widget.contents.length - 1) {
                  Navigator.pushReplacement(
                    context,
                    widget.nextPage,
                  );
                }
                _controller.nextPage(
                  duration: Duration(milliseconds: 100),
                  curve: Curves.bounceIn,
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.transparent,
                textStyle: TextStyle(
                  color: Colors.white,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Container buildDot(int index, BuildContext context) {
    return Container(
      height: 10,
      width: currentIndex == index ? 25 : 10,
      margin: EdgeInsets.only(right: 5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Theme.of(context).primaryColor,
      ),
    );
  }
}

// Colors
class DAGradients {
  static LinearGradient green() {
    return LinearGradient(
      begin: FractionalOffset(0.0, 0.95),
      end: FractionalOffset(1.0, 1.0),
      colors: <Color>[
        Color.fromRGBO(1, 196, 170, 1.0),
        Color.fromRGBO(157, 229, 122, 1.0),
      ],
    );
  }

  static LinearGradient orange() {
    return LinearGradient(
      begin: FractionalOffset(0.0, 0.95),
      end: FractionalOffset(1.0, 1.0),
      colors: <Color>[
        Color.fromRGBO(255, 158, 126, 1.0),
        Color.fromRGBO(255, 196, 120, 1.0),
      ],
    );
  }

  static LinearGradient grey() {
    return LinearGradient(
      begin: FractionalOffset(0.0, 0.5),
      end: FractionalOffset(0.25, 0.5),
      colors: <Color>[
        Color.fromRGBO(150, 150, 150, 0.99),
        Color.fromRGBO(255, 255, 255, 1.0),
      ],
    );
  }

  static LinearGradient white() {
    return LinearGradient(
      begin: FractionalOffset(0.0, 0.9),
      end: FractionalOffset(0.3, 1.0),
      colors: <Color>[
        Color.fromRGBO(255, 255, 255, 1.0),
        Color.fromRGBO(255, 255, 255, 1.0),
      ],
    );
  }

  static LinearGradient greyMin() {
    return LinearGradient(
      begin: FractionalOffset(0.0, 0.9),
      end: FractionalOffset(0.3, 1.0),
      colors: <Color>[
        Color.fromRGBO(220, 220, 220, 1.0),
        Color.fromRGBO(255, 255, 255, 1.0),
      ],
    );
  }

  static LinearGradient disabled() {
    return LinearGradient(
      begin: FractionalOffset(0.0, 0.95),
      end: FractionalOffset(1.0, 1.0),
      colors: <Color>[
        Color.fromRGBO(180, 180, 180, 1.0),
        Color.fromRGBO(180, 180, 180, 1.0),
      ],
    );
  }

  static LinearGradient primary(BuildContext context) {
    return LinearGradient(
      begin: FractionalOffset(0.0, 0.95),
      end: FractionalOffset(1.0, 1.0),
      colors: <Color>[
        Theme.of(context).colorScheme.secondary,
        Theme.of(context).primaryColor,
      ],
    );
  }

  static Gradient getGradient(BuildContext context, bool value) {
    try {
      if (value) {
        return DAGradients.green();
      } else {
        return DAGradients.primary(context);
      }
    } catch (e) {
      return DAGradients.primary(context);
    }
  }
}

// Images
class DAImagesType {
  static Widget getSvgCard(BuildContext context,
      {double? heightBox, double? widthBox, double? paddingBox, String? icon}) {
    double _padding = (paddingBox == null) ? 20 : paddingBox - 5;
    Widget _svg = SvgPicture.asset(
      'assets/icon/' + icon! + '.svg',
      colorFilter:
          ColorFilter.mode(Theme.of(context).primaryColor, BlendMode.srcIn),
      //color: Theme.of(context).primaryColor,
    );

    Widget svgContainer = Material(
      color: Colors.transparent,
      child: Container(
        padding: EdgeInsets.all(_padding),
        height: (heightBox) ?? 90.0,
        width: (widthBox) ?? 97.5,
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade700
              : Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15.0),
            bottomLeft: Radius.circular(15.0),
          ),
        ),
        child: SizedBox(
          child: _svg,
          height: 25.0,
          width: 100.0,
        ),
      ),
    );

    return svgContainer;
  }

  static ImageProvider getImageMin(BuildContext context, bool value) {
    try {
      if (value) {
        return AssetImage('assets/app/logo.png');
        // final Uint8List resAvatar = Base64Codec().decode(avatar.replaceAll(
        //     avatar.substring(0, avatar.indexOf(',') + 1), ""));
        // ImageProvider res = Image.memory(resAvatar, fit: BoxFit.cover).image;
        // return res;
      } else {
        return AssetImage('assets/app/logo.png');
      }
    } catch (e) {
      return AssetImage('assets/app/logo.png');
    }
  }
}

class DAMainMenu extends StatelessWidget {
  final Function() avatarTap;
  final dynamic defLogoAvatar;
  final List<Widget>? tiles;

  DAMainMenu({
    required this.avatarTap,
    required this.defLogoAvatar,
    this.tiles,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> _navBarOpt = [];

    // Menú Header
    _navBarOpt.add(
      DAHeaderUser(
        defLogoAvatar: this.defLogoAvatar,
        onTap: this.avatarTap,
      ),
    );

    // Agregamos Tiles
    if (tiles != null && tiles!.length > 0) {
      for (var i = 0; i < tiles!.length; i++) {
        _navBarOpt.add(this.tiles![i]);
      }
    }

    // Footer Versión
    _navBarOpt.add(
      Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          margin: EdgeInsets.only(bottom: 10),
          child: Text(
            'Powered by Intelisis',
            style: TextStyle(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.grey,
            ),
          ),
        ),
      ),
    );

    var drawer = Drawer(
      child: Container(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade800
            : Colors.white,
        child: ListView(
          children: _navBarOpt,
        ),
      ),
    );

    return drawer;
  }
}

class DAHeaderUser extends StatelessWidget {
  final dynamic defLogoAvatar;
  final Function()? onTap;

  DAHeaderUser({
    required this.defLogoAvatar,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    //final _userProv =
    Provider.of<DAUsuarioProvider>(context);
    DAUsuarioProvider user = DAUsuarioProvider();
    DASessionProvider prov = DASessionProvider();
    SessionModel session = prov.session;

    dynamic _userAvatar = (user.avatar) ?? this.defLogoAvatar;
    Widget _avatar = GestureDetector(
      child: Align(
        alignment: Alignment.center,
        child: Container(
          width: 100.0,
          height: 100.0,
          decoration: new BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
            image: new DecorationImage(
              fit: BoxFit.fill,
              image: _userAvatar,
            ),
          ),
        ),
      ),
      onTap: this.onTap,
    );

    Widget _usuario = Container(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade800
            : Colors.white,
        alignment: Alignment.topLeft,
        padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
        height: 260,
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 30),
            _avatar,
            SizedBox(height: 20),
            Text(
              (session.nombre) ?? 'Sesión Inválida',
              style: TextStyle(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
                fontWeight: FontWeight.w500,
                fontSize: 18,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
            SizedBox(height: 5),
            Align(
              alignment: Alignment.topCenter,
              child: Text(
                (session.usuario) ?? 'Cierre Sesión',
                style: TextStyle(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white
                      : Colors.black,
                  fontWeight: FontWeight.w300,
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            SizedBox(height: 20),
          ],
        ));

    Widget _header = Stack(
      children: [
        _usuario,
        Column(
          children: [
            Container(
              alignment: Alignment.topLeft,
              color: Colors.transparent,
              height: 200,
              width: 1,
              child: null,
            ),
            Container(
              child: DABottomWaveContainerMap(),
            ),
          ],
        )
      ],
    );

    return _header;
  }
}

/// Un widget personalizado que proporciona un campo de texto con capacidad de autocompletado.
///
/// [DAAutoCompleteTextField] permite mostrar una lista de sugerencias basada en el texto ingresado
/// por el usuario. Se puede proporcionar una lista de sugerencias, un texto de indicación, un
/// controlador de texto y otros parámetros para personalizar el comportamiento del campo de texto.
class DAAutoCompleteTextField extends StatefulWidget {
  /// La lista de sugerencias que se mostrarán en el desplegable de autocompletado.
  final List<String> suggestions;

  /// El texto de indicación que se mostrará dentro del campo de texto cuando esté vacío.
  final String hintText;

  /// El controlador utilizado para controlar el texto que se está editando.
  final TextEditingController? controller;

  /// El valor inicial que se establecerá en el campo de texto cuando se renderice por primera vez.
  final String? initialValue;

  /// Una función de retorno que se invoca cada vez que cambia el valor del campo de texto.
  final ValueChanged<String>? onChanged;

  const DAAutoCompleteTextField({
    super.key,
    required this.suggestions,
    required this.hintText,
    this.controller,
    this.initialValue,
    this.onChanged,
  });

  @override
  State<DAAutoCompleteTextField> createState() =>
      _DAAutoCompleteTextFieldState();
}

class _DAAutoCompleteTextFieldState extends State<DAAutoCompleteTextField> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();
  String? _selected;

  @override
  void initState() {
    super.initState();
    _controller =
        widget.controller ?? TextEditingController(text: widget.initialValue);
    _controller.addListener(() {
      setState(() {});
      if (widget.onChanged != null) {
        widget.onChanged!(_controller.text);
      }
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_controller.text.isNotEmpty)
                Text(
                  widget.hintText,
                  style: TextStyle(color: Colors.grey.withValues(alpha: 0.5)),
                ),
              TextField(
                decoration: InputDecoration(
                  hintText: _controller.text.isEmpty ? widget.hintText : null,
                  suffixIcon: _controller.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _controller.clear();
                            _selected = null;
                            setState(() {});
                          },
                        )
                      : null,
                ),
                controller: _controller,
                focusNode: _focusNode,
                onChanged: (text) {
                  if (_selected == null || _selected != text) {
                    _selected = null;
                  }
                  setState(() {});
                  if (widget.onChanged != null) {
                    widget.onChanged!(text);
                  }
                },
              ),
            ],
          ),
        ),
        if (_controller.text.isNotEmpty && _selected == null)
          Container(
            height: widget.suggestions
                        .where((suggestion) => suggestion
                            .toLowerCase()
                            .startsWith(_controller.text.toLowerCase()))
                        .length ==
                    0
                ? 0
                : 300,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: widget.suggestions.length,
              itemBuilder: (context, index) {
                final suggestion = widget.suggestions[index];
                if (suggestion
                    .toLowerCase()
                    .startsWith(_controller.text.toLowerCase())) {
                  return InkWell(
                    onTap: () {
                      _controller.text = suggestion;
                      _selected = suggestion;
                      _focusNode.unfocus();
                      setState(() {});
                      if (widget.onChanged != null) {
                        widget.onChanged!(suggestion);
                      }
                    },
                    child: Container(
                      height: 40,
                      padding: const EdgeInsets.only(left: 10),
                      alignment: Alignment.centerLeft,
                      child: Text(suggestion),
                    ),
                  );
                }
                return Container();
              },
            ),
          ),
      ],
    );
  }
}
