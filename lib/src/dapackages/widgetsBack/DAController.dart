// ignore: import_of_legacy_library_into_null_safe
import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:page_transition/page_transition.dart';

import '../main_DAPackages.dart';

// ignore: must_be_immutable
class DAController extends StatefulWidget {
  @override
  _DAController createState() => _DAController();

  static String sessionUrl(String uriReq) {
    _fixUri(String value) {
      try {
        if (value.indexOf('}}') >= 0) {
          DASessionProvider _eProv = DASessionProvider();

          String varSp = jsonDecode(jsonEncode(_eProv.session))[
              value.substring(0, value.indexOf('}}'))];
          uriReq = uriReq.replaceAll(
              '{{' + value.substring(0, value.indexOf('}}')) + '}}', varSp);
        }
      } catch (e) {
        if (value == '') {
          value = 'valor';
        }
        String err = value + ' no definido';
        throw err;
      }
    }

    uriReq.split('{{').forEach((value) => _fixUri(value));
    return uriReq;
  }

  static Map<String, dynamic> sessionRequest(Map<String, dynamic>? bodyReq) {
    // ignore: unnecessary_question_mark
    _fixBody(String key, dynamic? value) {
      try {
        if (value == null) {
          bodyReq![key] = '';
        }

        DASessionProvider _eProv = DASessionProvider();
        if (value!.toString().indexOf('{{') >= 0 &&
            value.toString().indexOf('}}') >= 0) {
          String varSp = jsonDecode(jsonEncode(_eProv.session))[
              value.toString().replaceAll('{{', '').replaceAll('}}', '')];
          bodyReq![key] = varSp;
        }
      } catch (e) {
        String err =
            key.replaceAll('{{', '').replaceAll('}}', '') + ' no definido';
        Fluttertoast.cancel();
        Fluttertoast.showToast(
            msg: err,
            toastLength: Toast.LENGTH_LONG,
            gravity: ToastGravity.SNACKBAR,
            timeInSecForIosWeb: 3,
            backgroundColor: Colors.black,
            textColor: Colors.white,
            fontSize: 16.0);
        throw err;
      }
    }

    bodyReq!.forEach((key, value) => _fixBody(key, value));
    return bodyReq;
  }

  static Map<String, dynamic> fixRequest(
      Map<String, dynamic> bodyReq, Map<String, dynamic> dataReq) {
    String varSp = jsonEncode(bodyReq)
        .replaceAll('<Finder>', "query")
        .replaceAll('<ID>', "(this.keyFinderID)");
    return jsonDecode(varSp);
  }

  static Future<void> refAvatar(BuildContext context, VoidCallback fn) async {
    try {
      DAUsuarioProvider user = DAUsuarioProvider();
      await user.refAvatar();
    } catch (e) {
      print('refAvatar: ' + e.toString());
    }
  }

  static Future<void> saveAvatar(BuildContext context,
      final GlobalKey<ScaffoldState> _scaffoldKey, VoidCallback fn) async {
    try {
      DAUsuarioProvider usrProv = DAUsuarioProvider();
      await usrProv.saveAvatar(context, _scaffoldKey, null);
    } catch (e) {
      DAToast(context, e.toString());
    }
  }

  static String formatoFecvhaNom(String value) {
    String nombreMes(int mes) {
      switch (mes) {
        case 1:
          return 'Enero';
        case 2:
          return 'Febrero';
        case 3:
          return 'Marzo';
        case 4:
          return 'Abril';
        case 5:
          return 'Mayo';
        case 6:
          return 'Junio';
        case 7:
          return 'Julio';
        case 8:
          return 'Agosto';
        case 9:
          return 'Septiembre';
        case 10:
          return 'Octubre';
        case 11:
          return 'Noviembre';
        case 12:
          return 'Diciembre';
        default:
          return '';
      }
    }

    String formatoFecha(String fecha) {
      DateTime temp = DateTime.parse(fecha);
      String mes = nombreMes(temp.month);
      return '${temp.day} de $mes del ${temp.year}';
    }

    return formatoFecha(value);
  }

  static openUrlMap(
      BuildContext context, double latitude, double longitude) async {
    try {
      final url = 'https://maps.google.com/maps?q=$latitude,$longitude';
      // if (await canLaunch(url)) {

      return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          Widget _dialog = DAInputDialog(
            title: '¿Desea abrir la ubicación?',
            subtitle: 'Esta acción saldrá de la app momentaneamente.',
            okText: 'Confirmar',
            input: [
              Container(
                height: 120.0,
                width: 180.0,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    fit: BoxFit.cover,
                    image: AssetImage('assets/system/MapDialog.png'),
                  ),
                ),
              ),
            ],
            onPressed: () async {
              await launchUrl(Uri.parse(url));
              Navigator.pop(context);
            },
          );
          return _dialog;
        },
      );

      // } else {
      //   DAToast(context, 'No se tiene configurada una dirección válida');
      // }
    } catch (e) {
      DAToast(context, 'No se generó una coordenada válida');
    }
  }

  static ImageProvider getImageFromBase64(
      {required String image64, String? defPng}) {
    try {
      String? signImage = image64;
      final Uint8List resAvatar = Base64Codec().decode(signImage.replaceAll(
          signImage.substring(0, signImage.indexOf(',') + 1), ""));
      ImageProvider res = Image.memory(resAvatar, fit: BoxFit.cover).image;
      return res;
    } catch (e) {
      return AssetImage((defPng) ?? 'assets/system/Foto.png');
    }
  }

  static getCurrentLatLang() async {
    try {
      Position currPosition = await getCurrentLocation();
      LatLng resLatLan = new LatLng(
        currPosition.latitude,
        currPosition.longitude,
      );

      return resLatLan;
    } catch (e) {
      print(e.toString());
      throw 'Error al obtener permisos de ubicación';
    }
  }

  static Future<Position> getCurrentLocation() async {
    try {
      bool isLocationServiceEnabled =
          await Geolocator.isLocationServiceEnabled();

      if (!isLocationServiceEnabled) {
        await Geolocator.openLocationSettings();
        throw 'Los servicios de ubicación están desactivados. Por favor, actívalos.';
      }

      LocationPermission permission = await Geolocator.checkPermission();

      switch (permission) {
        case LocationPermission.denied:
          permission = await Geolocator.requestPermission();
          if (permission == LocationPermission.denied) {
            throw 'Permisos de ubicación denegados. Por favor, otorga los permisos necesarios.';
          }
          break;
        case LocationPermission.deniedForever:
          await Geolocator.openAppSettings();
          throw 'Permisos de ubicación denegados permanentemente. Por favor, cambia los permisos en la configuración de la aplicación.';
        case LocationPermission.whileInUse:
          break;
        case LocationPermission.always:
          break;
        case LocationPermission.unableToDetermine:
          throw 'No se puede determinar el estado de los permisos de ubicación.';
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.bestForNavigation,
        timeLimit: Duration(seconds: 15),
      );

      return position;
    } catch (e) {
      if (e is LocationServiceDisabledException) {
        print('Error: Los servicios de ubicación están desactivados.');
        throw 'Error: Los servicios de ubicación están desactivados.';
      } else if (e is PermissionDeniedException) {
        print('Error: Permisos de ubicación denegados.');
        throw 'Error: Permisos de ubicación denegados.';
      } else if (e is TimeoutException) {
        print('Error: Tiempo de espera agotado al obtener la ubicación.');
        throw 'Error: Tiempo de espera agotado al obtener la ubicación.';
      } else {
        print('Error desconocido: ${e.toString()}');
        throw 'Error desconocido al obtener la ubicación.';
      }
    }
  }

  static defTransition(Widget page, RouteSettings settings) {
    return PageTransition(
      child: page,
      settings: settings,
      type: PageTransitionType.fade,
      duration: Duration(milliseconds: 500),
    );
  }

  static Future<String> scanQR(BuildContext context) async {
    var options = ScanOptions(
      strings: const {
        "cancel": "Cancelar",
        "flash_on": "Con Flash",
        "flash_off": "Sin Flash",
      },
    );

    var result = await BarcodeScanner.scan(options: options);
    return result.rawContent.toString();
  }

  static getDARoute({
    required String routeName,
    required RouteSettings settings,
    required dynamic defLogoAvatar,
    required List<Widget> appMenu,
    required Widget defPage,
  }) {
    switch (routeName) {
      case '/MenuCards':
        return defTransition(
            DAMenuCardsPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/ListPage':
        return defTransition(
            DALayoutListCardPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/WidgetsForm':
        return defTransition(
            DALayoutFormWidgetsPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/ChatPage':
        return defTransition(
            DALayoutChatPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/PagosPage':
        return defTransition(
            DALayoutPaymentPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/MovDet':
        return defTransition(
            new DALayoutHdrDetPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/SerieLote':
        return defTransition(
            new DALayoutHdrDetPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/MapListPage':
        return defTransition(
            new DALayoutListMapPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      case '/MapWidgetPage':
        return defTransition(
            new DALayoutMapViewPage(
              defLogoAvatar: defLogoAvatar,
              appMenu: appMenu,
            ),
            settings);
      default:
        return defTransition(defPage, settings);
    }
  }

  static Map<String, dynamic>? getWidgetValue(Widget element) {
    try {
      Map<String, dynamic> res = {};

      switch (element.runtimeType.toString()) {
        case 'DADataTablePro':
          DADataTablePro dt = element as DADataTablePro;
          Map<String, dynamic> dtWi = {dt.elID: dt.data};
          res = dtWi;
          break;
        case 'DADropdown':
          DADropdown dd = element as DADropdown;
          dynamic myVal;
          for (final dynamic val in dd.data) {
            if (val[dd.value].toString() == dd.controllerValue.toString()) {
              myVal = val[dd.value].toString().trim();
            }
          }
          res[dd.refID] = myVal;
          break;
        case 'DAInput':
          DAInput input = element as DAInput;
          res[input.refID] = input.controller.text;
          break;
        case 'DADatePicker':
          DADatePicker dp = element as DADatePicker;
          String myVal = '';
          try {
            myVal =
                DateFormat('yyyyMMdd hh:mm:ss').format(dp.value as DateTime);
          } catch (e) {
            print(e.toString());
          }
          res[dp.refID] = myVal;
          break;
        case 'DARadioList':
          print('TODO DARadioList');
          break;
        case 'DASignature':
          print('TODO DASignature');
          break;
        case 'DaImageUpload':
          print('TODO DaImageUpload');
          break;
        default:
          break;
      }

      return res;
    } catch (e) {
      print(e.toString());
      return null;
    }
  }

  static bool isNumeric(String? str) {
    if (str == null) {
      return false;
    }

    try {
      double.parse(str);
    } on FormatException {
      return false;
    } catch (e) {
      return false;
    }

    return true;
  }
}

class _DAController extends State<DAController> {
  @override
  // ignore: must_call_super
  void initState() {}

  @override
  // ignore: must_call_super
  void dispose() {}

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
