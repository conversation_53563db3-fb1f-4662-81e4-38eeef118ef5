import 'dart:convert';

formatEstatus(String _) {
  switch (_.toUpperCase().trim()) {
    case 'PENDIENTE':
      return 'Pendiente';
    case 'CONCLUIDO':
      return 'Concluido';
    case 'CANCELADO':
      return 'Cancelado';
    case 'SINAFECTAR':
      return 'Sin Afectar';
    default:
      return 'Desconocido';
  }
}

rfcValido(String rfc, [bool aceptarGenerico = true]) {
  // Expresión regular para el RFC de Mexico
  RegExp exp = new RegExp(
      r'^([A-ZÑ&]{3,4}([0-9]{2})(0[1-9]|1[0-2])(0[1-9]|1[0-9]|2[0-9]|3[0-1]))([A-Z\d]{3})?$');
  // Verificamos si el RFC coincide con la expresión regular
  var validado = exp.hasMatch(rfc.trim().toUpperCase());
  if (!validado)
    return false;
  else {
    return true;
  }
}

telefonoValido(String telefono) {
  // Expresión regular para los números de teléfono de México
  RegExp exp = new RegExp(r'^[0-9]{10}$');
  var validado = exp.hasMatch(telefono.trim());
  if (!validado)
    return false;
  else {
    return true;
  }
}

emailValido(String email) {
  // Expresión regular para los emails
  RegExp exp = new RegExp(r'^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$');
  var validado = exp.hasMatch(email.trim());
  if (!validado)
    return false;
  else {
    return true;
  }
}

codigoPostalValido(String codigoPostal) {
  // Expresión regular para los códigos postales de México
  RegExp exp = new RegExp(r'^[0-9]{5}$');
  var validado = exp.hasMatch(codigoPostal.trim());
  if (!validado)
    return false;
  else {
    return true;
  }
}

String formatoMonetarioMexico(num cantidad) {
  return "\$ ${cantidad.toStringAsFixed(2).replaceAllMapped(new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}";
}

// Función para eliminar elementos de una lista que no están en una lista
void removerDeLista(List<dynamic> list, List<dynamic> listToRemove) {
  list.removeWhere((element) => !listToRemove.contains(element['Articulo']));
}

List<Map<String, dynamic>> parseDynamicList(dynamic input) {
  if (input is List<Map<String, dynamic>>) {
    return _removeNullFieldsList(input);
  } else if (input is List) {
    if (input.every((item) => item is Map<String, dynamic>)) {
      return _removeNullFieldsList(List<Map<String, dynamic>>.from(input));
    }
  } else if (input is String) {
    try {
      return _removeNullFieldsList(
          List<Map<String, dynamic>>.from(jsonDecode(input)));
    } catch (e) {
      print('Error al decodificar JSON: $e');
      return [];
    }
  }

  print('El tipo de dato proporcionado no es compatible.');
  return [];
}

List<Map<String, dynamic>> _removeNullFieldsList(
    List<Map<String, dynamic>> input) {
  return input.map((map) {
    return map..removeWhere((key, value) => value == null);
  }).toList();
}

Map<String, dynamic> parseDynamic(dynamic input) {
  if (input is Map<String, dynamic>) {
    return _removeNullFields(input);
  } else if (input is Map) {
    return _removeNullFields(Map<String, dynamic>.from(input));
  } else if (input is String) {
    try {
      return _removeNullFields(Map<String, dynamic>.from(jsonDecode(input)));
    } catch (e) {
      print('Error al decodificar JSON: $e');
      return {};
    }
  }

  print('El tipo de dato proporcionado no es compatible.');
  return {};
}

Map<String, dynamic> _removeNullFields(Map<String, dynamic> input) {
  input.removeWhere((key, value) => value == null);
  return input;
}
