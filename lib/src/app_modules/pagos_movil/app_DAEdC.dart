import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAAyuda.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAFacturas.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAPagos.dart';
import 'package:pagosmovil/src/controllers/desarrollos_controller.dart';
import 'package:pagosmovil/src/controllers/ticket_edc_controller.dart';
import 'package:pagosmovil/src/widgets/payment_table.dart';

import '/src/DAPackagesRef.dart';
import '/src/helpers.dart';
import '/src/app_config/api_request.dart';
import 'app_DANotificaciones.dart';

class AppDAEdC {
  /* Lista de Pagos */
  static index(BuildContext context) {
    String _refID = 'estados_de_cuenta';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    DatosDesarrollos _desarrollos = DatosDesarrollos();

    /* FutureOr onGoBack(Object? value) {
      _layoutListProv.isLoading = true;
      _layoutListProv.notify();
    }

    removeNull(String? value) {
      if (value == null || value == 'null') {
        return '';
      }
      return value;
    } */

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'EdC | Seleccionar Desarrollo',
      hasBackButton: false,
      onNotification: () async {
        // Implementación del botón de notificaciones
        try {
          // Mostrar pantalla de notificaciones
          Navigator.pushNamed(context, '/WidgetsForm',
              arguments: AppDANotificaciones.index(context));
        } catch (e) {
          DAToast(context, "Error al mostrar notificaciones: ${e.toString()}",
              useFlutterToast: true);
        }
      },
      formBody: [],
      bottomNavigationBar: DABottomAppBar(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAPagos.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.payment),
                ),
                tooltip: 'Pago',
              ),
              Positioned(
                bottom: 0,
                child: Text('Pago', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {},
                icon: Icon(Icons.book),
                tooltip: 'Estado de Cuenta',
              ),
              Positioned(
                bottom: 0,
                child: Text('EdC', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAFacturas.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.receipt),
                ),
                tooltip: 'Facturas',
              ),
              Positioned(
                bottom: 0,
                child: Text('Facturas', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  _layoutListProv.scaffoldKey.currentState.openEndDrawer();
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.person),
                ),
                tooltip: 'Perfil',
              ),
              Positioned(
                bottom: 0,
                child: Text('Perfil', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(context, '/WidgetsForm',
                      arguments: AppDAAyuda.index(context));
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.help),
                ),
                tooltip: 'Ayuda',
              ),
              Positioned(
                bottom: 0,
                child: Text('Ayuda', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );

    createWidgets() async {
      List<Widget> _formBody = [
        if (_desarrollos.containsDesarrollo('PDI'))
          DABigMenuButton(
            label: 'Zaru',
            image: 'Zaru',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('PDI')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAEdC.terrenos(context, _args),
              );
            },
          ),
        if (_desarrollos.containsDesarrollo('FZB'))
          DABigMenuButton(
            label: 'Zibata',
            image: 'Zibata',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('FZB')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAEdC.terrenos(context, _args),
              );
            },
          ),
        if (_desarrollos.containsDesarrollo('FZR'))
          DABigMenuButton(
            label: 'Zirandaro',
            image: 'Zirandaro',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('FZR')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAEdC.terrenos(context, _args),
              );
            },
          ),
        if (_desarrollos.containsDesarrollo('PDI'))
          DABigMenuButton(
            label: 'Nuevo Refugio',
            image: 'NuevoRefugio',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('PDI')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAEdC.terrenos(context, _args),
              );
            },
          ),
      ];
      List<Widget> result = [
        SingleChildScrollView(
          child: Stack(
            children: [
              SafeArea(child: Image.asset('assets/app/fondo.jpg')),
              Column(
                children: _formBody,
              ),
            ],
          ),
        )
      ];
      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      _formBodyRes = await createWidgets();
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }

  static terrenos(BuildContext context, dynamic data) {
    List<Map<String, dynamic>> _data = parseDynamicList(data);
    String _refID = 'terrenos_edc';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    /* FutureOr onGoBack(Object? value) {
      _layoutListProv.isLoading = true;
      _layoutListProv.notify();
    }

    removeNull(String? value) {
      if (value == null || value == 'null') {
        return '';
      }
      return value;
    } */

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Seleccionar Terreno',
      hasBackButton: true,
      onNotification: () async {
        // Implementación del botón de notificaciones
        try {
          // Mostrar pantalla de notificaciones
          Navigator.pushNamed(context, '/WidgetsForm',
              arguments: AppDANotificaciones.index(context));
        } catch (e) {
          DAToast(context, "Error al mostrar notificaciones: ${e.toString()}",
              useFlutterToast: true);
        }
      },
      formBody: [],
      bottomNavigationBar: DABottomAppBar(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAPagos.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.payment),
                ),
                tooltip: 'Pago',
              ),
              Positioned(
                bottom: 0,
                child: Text('Pago', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAEdC.index(context),
                  );
                },
                icon: Icon(Icons.book),
                tooltip: 'Estado de Cuenta',
              ),
              Positioned(
                bottom: 0,
                child: Text('EdC', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAFacturas.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.receipt),
                ),
                tooltip: 'Facturas',
              ),
              Positioned(
                bottom: 0,
                child: Text('Facturas', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  _layoutListProv.scaffoldKey.currentState.openEndDrawer();
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.person),
                ),
                tooltip: 'Perfil',
              ),
              Positioned(
                bottom: 0,
                child: Text('Perfil', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(context, '/WidgetsForm',
                      arguments: AppDAAyuda.index(context));
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.help),
                ),
                tooltip: 'Ayuda',
              ),
              Positioned(
                bottom: 0,
                child: Text('Ayuda', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );

    createWidgets() async {
      List<Widget> _formBody = [
        PaymentTable(
          data: _data,
          columns: ['Inmueble', 'Local', 'Nombre'],
          confirmationColumns: ['Inmueble', 'Local'],
          selectable: false,
          onConfirm: (p0, sum, facturarA) => Navigator.pushNamed(
            context,
            '/PagosPage',
            arguments: AppDAEdC.documentos(context, jsonEncode(p0)),
          ),
        ),
      ];
      List<Widget> result = [
        SingleChildScrollView(
          child: Column(
            children: _formBody,
          ),
        )
      ];
      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      _formBodyRes = await createWidgets();
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }

  static documentos(BuildContext context, dynamic data) {
    Map<String, dynamic> _data = parseDynamic(data);
    String _refID = 'docs_edc';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    TicketEdcController ticketController = TicketEdcController();

    printHistory() async {
      final pdf = pw.Document(version: PdfVersion.pdf_1_5, compress: true);
      DASessionProvider _eProv = DASessionProvider();

      // Obtener datos del estado de cuenta
      DARequestModel _reqDatosEdc = ApiRequest.getDatosEstadoCuenta(
        empresa: _data['Empresa'],
        inmueble: _data['Inmueble'],
        local: _data['Local'],
      );
      dynamic _resDatosEdc =
          await ApiRequest.execAPI(_reqDatosEdc.uriReq, _reqDatosEdc.bodyReq);

      String _formatDate(DateTime date) {
        return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
      }

      // Obtener datos del cliente
      DARequestModel _reqDatosCliente = ApiRequest.getDatosClienteEstadoCuenta(
        empresa: _data['Empresa']!,
      );
      dynamic _resDatosCliente = await ApiRequest.execAPI(
          _reqDatosCliente.uriReq, _reqDatosCliente.bodyReq);

      // Obtener datos del cliente web (propiedades)
      DARequestModel _reqDatosClienteWeb =
          ApiRequest.getDatosClienteWebEstadoCuenta(
        empresa: _data['Empresa']!,
        inmueble: _data['Inmueble']!,
        local: _data['Local']!,
      );
      dynamic _resDatosClienteWeb = await ApiRequest.execAPI(
          _reqDatosClienteWeb.uriReq, _reqDatosClienteWeb.bodyReq);

      // Get company logo based on company code
      Future<pw.MemoryImage> _getCompanyLogo(String empresa) async {
        String imageName;
        switch (empresa) {
          case 'PDI':
            imageName = 'Zaru';
            break;
          case 'FZB':
            imageName = 'Zibata';
            break;
          case 'FZR':
            imageName = 'Zirandaro';
            break;
          default:
            imageName = 'NuevoRefugio'; // Default image if company not found
        }

        // Load the image from assets using rootBundle
        final ByteData imageData =
            await rootBundle.load('assets/app/$imageName.png');
        final Uint8List bytes = imageData.buffer.asUint8List();
        return pw.MemoryImage(bytes);
      }

      ticketController.fechaVenta = _formatDate(DateTime.now());

      final pw.MemoryImage companyLogo =
          await _getCompanyLogo(_data['Empresa']!);

      try {
        final products = Get.find<TicketEdcController>().products;
        final firstPageItems = 20; // Items for first page
        final otherPagesItems = 30; // Items for subsequent pages

        // Handle first page separately
        final firstPageEndIndex =
            products.length > firstPageItems ? firstPageItems : products.length;
        final firstPageProducts = products.sublist(0, firstPageEndIndex);

        // Add first page
        pdf.addPage(
          pw.MultiPage(
            pageFormat: PdfPageFormat.letter,
            maxPages: 20,
            build: (pw.Context context) {
              return [
                pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    // Header with date line
                    pw.Container(
                      width: double.infinity,
                      decoration: pw.BoxDecoration(
                        border: pw.Border(
                          top: pw.BorderSide(width: 1),
                          bottom: pw.BorderSide(width: 1),
                        ),
                      ),
                      padding: pw.EdgeInsets.symmetric(vertical: 5),
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Text(
                            'Fecha de Impresión ${ticketController.fechaVenta}',
                            style: pw.TextStyle(fontSize: 10),
                          ),
                        ],
                      ),
                    ),
                    pw.SizedBox(height: 20),

                    // Company logo and title section
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // Company logo and name
                        pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.center,
                          children: [
                            pw.Container(
                              width: 80,
                              height: 80,
                              child: pw.Image(
                                companyLogo,
                                fit: pw.BoxFit.contain,
                              ),
                            ),
                          ],
                        ),
                        // Title
                        pw.Expanded(
                          child: pw.Center(
                            child: pw.Text(
                              'ESTADO DE CUENTA',
                              style: pw.TextStyle(
                                fontSize: 16,
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        // Property information on the right
                        pw.Container(
                          width: 80,
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.end,
                            children: [
                              pw.Text(
                                'Local: ${_resDatosClienteWeb.length > 0 ? _resDatosClienteWeb[0]['Local'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 8),
                                textAlign: pw.TextAlign.right,
                              ),
                              pw.SizedBox(height: 2),
                              pw.Text(
                                'Medida: ${_resDatosClienteWeb.length > 0 ? _resDatosClienteWeb[0]['medida'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 8),
                                textAlign: pw.TextAlign.right,
                              ),
                              pw.SizedBox(height: 2),
                              pw.Text(
                                'Descripción: ${_resDatosClienteWeb.length > 0 ? _resDatosClienteWeb[0]['descripcion'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 8),
                                textAlign: pw.TextAlign.right,
                              ),
                              pw.SizedBox(height: 2),
                              pw.Text(
                                'Carta Oferta: ${_resDatosClienteWeb.length > 0 ? _resDatosClienteWeb[0]['CartaOferta'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 8),
                                textAlign: pw.TextAlign.right,
                              ),
                              pw.SizedBox(height: 2),
                              pw.Text(
                                'Valor Total del Terreno: \$${_resDatosClienteWeb.length > 0 ? formatCurrency(_resDatosClienteWeb[0]['ValorTerreno'] ?? 0) : '0.00'}',
                                style: pw.TextStyle(
                                    fontSize: 8,
                                    fontWeight: pw.FontWeight.bold),
                                textAlign: pw.TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 20),

                    // Company RFC and service info
                    pw.Column(
                      children: [
                        pw.Text(
                          _resDatosCliente.length > 0 &&
                                  _resDatosCliente[0].length > 0
                              ? _resDatosCliente[0][0]['Nombre'] ?? ''
                              : '',
                          style: pw.TextStyle(
                              fontSize: 12, fontWeight: pw.FontWeight.bold),
                          textAlign: pw.TextAlign.center,
                        ),
                        pw.Text(
                          'RFC: ${_resDatosCliente.length > 0 && _resDatosCliente[0].length > 0 ? _resDatosCliente[0][0]['RFC'] ?? '' : ''}',
                          style: pw.TextStyle(fontSize: 10),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 15),

                    // Client information
                    pw.Align(
                      alignment: pw.Alignment.centerLeft,
                      child: pw.Text(
                        'Cliente: ${_resDatosCliente.length > 1 && _resDatosCliente[1].length > 0 ? _resDatosCliente[1][0]['Nombre'] ?? '' : ''}',
                        style: pw.TextStyle(
                            fontSize: 12, fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.SizedBox(height: 10),

                    // Client details and account summary in two columns
                    pw.Row(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // Left column - Client information
                        pw.Expanded(
                          flex: 1,
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                'INFORMACIÓN GENERAL:',
                                style: pw.TextStyle(
                                    fontSize: 10,
                                    fontWeight: pw.FontWeight.bold),
                              ),
                              pw.SizedBox(height: 5),
                              pw.Text(
                                'Dirección: ${_resDatosCliente.length > 1 && _resDatosCliente[1].length > 0 ? _resDatosCliente[1][0]['Direccion'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 9),
                              ),
                              pw.SizedBox(height: 3),
                              pw.Text(
                                'Teléfono: ${_resDatosCliente.length > 1 && _resDatosCliente[1].length > 0 ? _resDatosCliente[1][0]['Telefonos'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 9),
                              ),
                              pw.SizedBox(height: 3),
                              pw.Text(
                                'RFC: ${_resDatosCliente.length > 1 && _resDatosCliente[1].length > 0 ? _resDatosCliente[1][0]['RFC'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 9),
                              ),
                              pw.SizedBox(height: 3),
                              pw.Text(
                                'Régimen: ${_resDatosCliente.length > 1 && _resDatosCliente[1].length > 0 ? _resDatosCliente[1][0]['FiscalRegimen'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 9),
                              ),
                              pw.SizedBox(height: 3),
                              pw.Text(
                                'Email: ${_resDatosCliente.length > 1 && _resDatosCliente[1].length > 0 ? _resDatosCliente[1][0]['eMail1'] ?? '' : ''}',
                                style: pw.TextStyle(fontSize: 9),
                              ),
                            ],
                          ),
                        ),
                        pw.SizedBox(width: 20),
                        // Right column - Account summary
                        pw.Expanded(
                          flex: 1,
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              // Estado de cuenta data
                              ..._resDatosEdc
                                  .map((dato) => pw.Padding(
                                        padding: pw.EdgeInsets.only(bottom: 3),
                                        child: pw.Row(
                                          mainAxisAlignment:
                                              pw.MainAxisAlignment.spaceBetween,
                                          children: [
                                            pw.Text(
                                              '${dato['Concepto']}:',
                                              style: pw.TextStyle(fontSize: 9),
                                            ),
                                            pw.Text(
                                              '\$${formatCurrency(dato['Valor'])}',
                                              style: pw.TextStyle(
                                                  fontSize: 9,
                                                  fontWeight:
                                                      pw.FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                      ))
                                  .toList(),
                            ],
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 15),

                    // Note section
                    pw.Container(
                      width: double.infinity,
                      padding: pw.EdgeInsets.all(8),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(width: 1),
                      ),
                      child: pw.Text(
                        'NOTA: La siguiente información es válida únicamente para la fecha de impresión. La información contenida en el presente documento es exclusivamente para efectos informativos, por lo que la misma carece de cualquier valor probatorio, debiendo corroborar su contenido con los comprobantes de pago a cargo del cliente',
                        style: pw.TextStyle(fontSize: 8),
                        textAlign: pw.TextAlign.justify,
                      ),
                    ),
                    pw.SizedBox(height: 15),

                    // Table title
                    pw.Container(
                      width: double.infinity,
                      decoration: pw.BoxDecoration(
                        border: pw.Border(
                          top: pw.BorderSide(width: 1),
                          bottom: pw.BorderSide(width: 1),
                        ),
                      ),
                      padding: pw.EdgeInsets.symmetric(vertical: 8),
                      child: pw.Center(
                        child: pw.Text(
                          'DETALLE COBRANZA',
                          style: pw.TextStyle(
                            fontSize: 12,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    pw.SizedBox(height: 10),

                    // Table with first page products
                    pw.Table(
                      border: pw.TableBorder.all(),
                      columnWidths: {
                        0: pw.FlexColumnWidth(2),
                        1: pw.FlexColumnWidth(2),
                        2: pw.FlexColumnWidth(3),
                      },
                      children: [
                        // Table headers
                        pw.TableRow(
                          decoration: pw.BoxDecoration(
                            color: PdfColors.grey300,
                          ),
                          children: [
                            pw.Padding(
                              padding: pw.EdgeInsets.all(5),
                              child: pw.Text(
                                'Movimiento',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                            pw.Padding(
                              padding: pw.EdgeInsets.all(5),
                              child: pw.Text(
                                'Fecha Emisión',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                            pw.Padding(
                              padding: pw.EdgeInsets.all(5),
                              child: pw.Text(
                                'Referencia',
                                style: pw.TextStyle(
                                  fontSize: 10,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                        // Data rows for this page
                        ...firstPageProducts
                            .map((product) => pw.TableRow(
                                  children: [
                                    pw.Padding(
                                      padding: pw.EdgeInsets.all(5),
                                      child: pw.Text(
                                        product.getIndex(0),
                                        style: pw.TextStyle(fontSize: 10),
                                        textAlign: pw.TextAlign.center,
                                      ),
                                    ),
                                    pw.Padding(
                                      padding: pw.EdgeInsets.all(5),
                                      child: pw.Text(
                                        product.getIndex(1),
                                        style: pw.TextStyle(fontSize: 10),
                                        textAlign: pw.TextAlign.center,
                                      ),
                                    ),
                                    pw.Padding(
                                      padding: pw.EdgeInsets.all(5),
                                      child: pw.Text(
                                        product.getIndex(2),
                                        style: pw.TextStyle(fontSize: 10),
                                        textAlign: pw.TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ))
                            .toList(),
                      ],
                    ),
                  ],
                ),
              ];
            },
          ),
        );

        // Handle remaining pages with 30 items each
        if (products.length > firstPageItems) {
          for (var i = firstPageItems;
              i < products.length;
              i += otherPagesItems) {
            final endIndex = (i + otherPagesItems < products.length)
                ? i + otherPagesItems
                : products.length;
            final pageProducts = products.sublist(i, endIndex);

            pdf.addPage(
              pw.MultiPage(
                pageFormat: PdfPageFormat.letter,
                maxPages: 20,
                build: (pw.Context context) {
                  return [
                    pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                      children: [
                        // Table only (no header info on subsequent pages)
                        pw.Table(
                          border: pw.TableBorder.all(),
                          columnWidths: {
                            0: pw.FlexColumnWidth(2),
                            1: pw.FlexColumnWidth(2),
                            2: pw.FlexColumnWidth(3),
                          },
                          children: [
                            // Data rows for this page
                            ...pageProducts
                                .map((product) => pw.TableRow(
                                      children: [
                                        pw.Padding(
                                          padding: pw.EdgeInsets.all(5),
                                          child: pw.Text(
                                            product.getIndex(0),
                                            style: pw.TextStyle(fontSize: 10),
                                            textAlign: pw.TextAlign.center,
                                          ),
                                        ),
                                        pw.Padding(
                                          padding: pw.EdgeInsets.all(5),
                                          child: pw.Text(
                                            product.getIndex(1),
                                            style: pw.TextStyle(fontSize: 10),
                                            textAlign: pw.TextAlign.center,
                                          ),
                                        ),
                                        pw.Padding(
                                          padding: pw.EdgeInsets.all(5),
                                          child: pw.Text(
                                            product.getIndex(2),
                                            style: pw.TextStyle(fontSize: 10),
                                            textAlign: pw.TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    ))
                                .toList(),
                          ],
                        ),
                      ],
                    ),
                  ];
                },
              ),
            );
          }
        }

        final output = await getTemporaryDirectory();
        final file = File('${output.path}/EDC ${_eProv.session.nombre}.pdf');
        await file.writeAsBytes(await pdf.save());

        await Printing.sharePdf(
          bytes: file.readAsBytesSync(),
          filename: 'EDC ${_eProv.session.nombre}.pdf',
        );
      } catch (e) {
        print('Error generando PDF: $e');
      }
    }

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Estado de Cuenta',
      hasBackButton: true,
      onNotification: () async {
        // Implementación del botón de notificaciones
        try {
          // Mostrar pantalla de notificaciones
          Navigator.pushNamed(context, '/WidgetsForm',
              arguments: AppDANotificaciones.index(context));
        } catch (e) {
          DAToast(context, "Error al mostrar notificaciones: ${e.toString()}",
              useFlutterToast: true);
        }
      },
      formBody: [],
      bottomNavigationBar: DABottomAppBar(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAPagos.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.payment),
                ),
                tooltip: 'Pago',
              ),
              Positioned(
                bottom: 0,
                child: Text('Pago', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {},
                icon: Icon(Icons.book),
                tooltip: 'Estado de Cuenta',
              ),
              Positioned(
                bottom: 0,
                child: Text('EdC', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAFacturas.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.receipt),
                ),
                tooltip: 'Facturas',
              ),
              Positioned(
                bottom: 0,
                child: Text('Facturas', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  _layoutListProv.scaffoldKey.currentState.openEndDrawer();
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.person),
                ),
                tooltip: 'Perfil',
              ),
              Positioned(
                bottom: 0,
                child: Text('Perfil', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(context, '/WidgetsForm',
                      arguments: AppDAAyuda.index(context));
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.help),
                ),
                tooltip: 'Ayuda',
              ),
              Positioned(
                bottom: 0,
                child: Text('Ayuda', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );

    createWidgets(List<Map<String, dynamic>> _data) async {
      List<Widget> _formBody = [
        /* PaymentTable(
          data: _data,
          columns: ['Mov', 'FechaEmision', 'Referencia'],
          selectable: true,
        ), */
      ];
      List<Widget> result = [
        SingleChildScrollView(
          child: Column(
            children: _formBody,
          ),
        )
      ];
      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      DARequestModel _req = ApiRequest.getEstadosDeCuenta(
        empresa: _data['Empresa'],
        cliente: _data['Cliente'],
        inmueble: _data['Inmueble'],
        local: _data['Local'],
      );
      dynamic _res = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
      List<Map<String, dynamic>> _docs = parseDynamicList(_res);

      ticketController.clearProducts();

      // Agregamos los articulos al controller
      _docs.forEach((element) {
        dynamic articulo = element['Mov'];
        dynamic descripcion1 = element['FechaEmision'];
        dynamic existencia = element['Referencia'];
        Get.find<TicketEdcController>()
            .addProductI(new Mensualidad(articulo, descripcion1, existencia));
      });

      printHistory();

      _formBodyRes = await createWidgets(_docs);
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }
}

// Función auxiliar para formatear moneda
String formatCurrency(dynamic value) {
  if (value == null) return '0.00';
  return value.toStringAsFixed(2).replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
        (Match m) => '${m[1]},',
      );
}
