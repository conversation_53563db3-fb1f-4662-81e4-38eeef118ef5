import 'package:flutter/material.dart';
import 'package:no_context_navigation/no_context_navigation.dart';

import '/src/DAPackagesRef.dart';
import '/src/controllers/notificaciones_controller.dart';

class AppDANotificaciones {
  static index(BuildContext context) {
    String _refID = 'notificaciones';
    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];
    DatosNotificaciones _notificaciones = DatosNotificaciones();

    try {
      DALayoutFormFilters _modelLayoutConfiguracion = new DALayoutFormFilters(
        heroTag: 'notificacionesScreen',
        refID: _refID,
        prefix: 'Mis Notificaciones',
        title: '',
        hasBackButton: true,
        iconSubmit: Icons.check,
        formSubmit: (value) async {
          Navigator.of(context).pop();
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(children: []),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        List<Widget> _formBody = [];

        if (_notificaciones.notificaciones.isEmpty) {
          _formBody.add(
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  'No tienes notificaciones',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          );
        } else {
          for (int index = 0;
              index < _notificaciones.notificaciones.length;
              index++) {
            var notificacion = _notificaciones.notificaciones[index];
            _formBody.add(
              Dismissible(
                key: Key(index.toString()), // Clave única para cada elemento
                direction: DismissDirection
                    .endToStart, // Solo deslizar de derecha a izquierda
                background: Container(
                  color: Colors.red,
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Icon(
                    Icons.delete,
                    color: Colors.white,
                  ),
                ),
                onDismissed: (direction) {
                  // Eliminar la notificación
                  _notificaciones.removeNotification(index);
                  DAToast(context, 'Notificación eliminada',
                      useFlutterToast: true);
                },
                confirmDismiss: (direction) async {
                  return await showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text("Confirmar"),
                        content: Text(
                            "¿Estás seguro de eliminar esta notificación?"),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: Text("Cancelar"),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            child: Text("Eliminar"),
                          ),
                        ],
                      );
                    },
                  );
                },
                child: Card(
                  margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    leading: Icon(
                      Icons.notifications,
                      color: Theme.of(context).primaryColor,
                    ),
                    title: Text(notificacion['Titulo'] ?? 'Notificación'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(notificacion['Mensaje'] ?? ''),
                        SizedBox(height: 4),
                        Text(
                          notificacion['Fecha'] ?? '',
                          style: TextStyle(
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                    onTap: () {
                      // Acción al tocar la notificación
                      if (notificacion['Tipo'] == 'desarrollo') {
                        navService.pushNamed('/detalle_desarrollo',
                            args: notificacion['Id']);
                      }
                    },
                  ),
                ),
              ),
            );
          }
        }

        return _formBody;
      }

      // Descargamos datos y asignamos valores
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        _formBodyRes = await createWidgets();
        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();
      return _modelLayoutConfiguracion;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
      return null;
    }
  }
}
