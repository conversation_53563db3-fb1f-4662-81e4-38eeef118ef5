import 'package:flutter/material.dart';
import 'package:file_saver/file_saver.dart';
import 'package:http/http.dart' as http;

import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAAyuda.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAEdC.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAPagos.dart';
import 'package:pagosmovil/src/controllers/desarrollos_controller.dart';
import 'package:pagosmovil/src/controllers/factura_controller.dart';
import 'package:pagosmovil/src/widgets/payment_table.dart';
import 'package:share_plus/share_plus.dart';

import '/src/DAPackagesRef.dart';
import '/src/helpers.dart';
import '/src/app_config/api_request.dart';
import 'app_DANotificaciones.dart';

class AppDAFacturas {
  /* Lista de Pagos */
  static index(BuildContext context) {
    String _refID = 'facturas';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    DatosDesarrollos _desarrollos = DatosDesarrollos();

    /* FutureOr onGoBack(Object? value) {
      _layoutListProv.isLoading = true;
      _layoutListProv.notify();
    }

    removeNull(String? value) {
      if (value == null || value == 'null') {
        return '';
      }
      return value;
    } */

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Facturas | Seleccionar Desarrollo',
      hasBackButton: false,
      onNotification: () async {
        // Implementación del botón de notificaciones
        try {
          // Mostrar pantalla de notificaciones
          Navigator.pushNamed(context, '/WidgetsForm',
              arguments: AppDANotificaciones.index(context));
        } catch (e) {
          DAToast(context, "Error al mostrar notificaciones: ${e.toString()}",
              useFlutterToast: true);
        }
      },
      formBody: [],
      bottomNavigationBar: DABottomAppBar(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAPagos.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.payment),
                ),
                tooltip: 'Pago',
              ),
              Positioned(
                bottom: 0,
                child: Text('Pago', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAEdC.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.book),
                ),
                tooltip: 'Estado de Cuenta',
              ),
              Positioned(
                bottom: 0,
                child: Text('EdC', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAFacturas.index(context),
                  );
                },
                icon: Icon(Icons.receipt),
                tooltip: 'Facturas',
              ),
              Positioned(
                bottom: 0,
                child: Text('Facturas', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  _layoutListProv.scaffoldKey.currentState.openEndDrawer();
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.person),
                ),
                tooltip: 'Perfil',
              ),
              Positioned(
                bottom: 0,
                child: Text('Perfil', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(context, '/WidgetsForm',
                      arguments: AppDAAyuda.index(context));
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.help),
                ),
                tooltip: 'Ayuda',
              ),
              Positioned(
                bottom: 0,
                child: Text('Ayuda', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );

    createWidgets() async {
      List<Widget> _formBody = [
        if (_desarrollos.containsDesarrollo('PDI'))
          DABigMenuButton(
            label: 'Zaru',
            image: 'Zaru',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('PDI')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAFacturas.terrenos(context, _args),
              );
            },
          ),
        if (_desarrollos.containsDesarrollo('FZB'))
          DABigMenuButton(
            label: 'Zibata',
            image: 'Zibata',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('FZB')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAFacturas.terrenos(context, _args),
              );
            },
          ),
        if (_desarrollos.containsDesarrollo('FZR'))
          DABigMenuButton(
            label: 'Zirandaro',
            image: 'Zirandaro',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('FZR')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAFacturas.terrenos(context, _args),
              );
            },
          ),
        if (_desarrollos.containsDesarrollo('PDI'))
          DABigMenuButton(
            label: 'Nuevo Refugio',
            image: 'NuevoRefugio',
            opacity: 0.25,
            onPressed: () async {
              List<dynamic> _args = [];
              for (var item in _desarrollos.getContratos('PDI')) {
                print(item.contratoId);
                DARequestModel _req =
                    ApiRequest.getTerrenosDesarrollos(item.contratoId);
                dynamic _data =
                    await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
                _args.add(_data[0]);
              }

              Navigator.pushNamed(
                context,
                '/PagosPage',
                arguments: AppDAFacturas.terrenos(context, _args),
              );
            },
          ),
      ];
      List<Widget> result = [
        SingleChildScrollView(
          child: Stack(
            children: [
              SafeArea(child: Image.asset('assets/app/fondo.jpg')),
              Column(
                children: _formBody,
              ),
            ],
          ),
        )
      ];
      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      _formBodyRes = await createWidgets();
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }

  static terrenos(BuildContext context, dynamic data) {
    List<Map<String, dynamic>> _data = parseDynamicList(data);
    String _refID = 'terrenos_facturas';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    /* FutureOr onGoBack(Object? value) {
      _layoutListProv.isLoading = true;
      _layoutListProv.notify();
    }

    removeNull(String? value) {
      if (value == null || value == 'null') {
        return '';
      }
      return value;
    } */

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Seleccionar Terreno',
      hasBackButton: true,
      onNotification: () async {
        // Implementación del botón de notificaciones
        try {
          // Mostrar pantalla de notificaciones
          Navigator.pushNamed(context, '/WidgetsForm',
              arguments: AppDANotificaciones.index(context));
        } catch (e) {
          DAToast(context, "Error al mostrar notificaciones: ${e.toString()}",
              useFlutterToast: true);
        }
      },
      formBody: [],
      bottomNavigationBar: DABottomAppBar(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {},
                icon: Icon(Icons.payment),
                tooltip: 'Pago',
              ),
              Positioned(
                bottom: 0,
                child: Text('Pago', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAEdC.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.book),
                ),
                tooltip: 'Estado de Cuenta',
              ),
              Positioned(
                bottom: 0,
                child: Text('EdC', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAFacturas.index(context),
                  );
                },
                icon: Icon(Icons.receipt),
                tooltip: 'Facturas',
              ),
              Positioned(
                bottom: 0,
                child: Text('Facturas', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  _layoutListProv.scaffoldKey.currentState.openEndDrawer();
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.person),
                ),
                tooltip: 'Perfil',
              ),
              Positioned(
                bottom: 0,
                child: Text('Perfil', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(context, '/WidgetsForm',
                      arguments: AppDAAyuda.index(context));
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.help),
                ),
                tooltip: 'Ayuda',
              ),
              Positioned(
                bottom: 0,
                child: Text('Ayuda', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );

    createWidgets() async {
      List<Widget> _formBody = [
        PaymentTable(
          data: _data,
          columns: ['Inmueble', 'Local', 'Nombre'],
          confirmationColumns: ['Inmueble', 'Local'],
          selectable: false,
          onConfirm: (p0, sum, facturarA) {
            if (facturarA != null) {
              p0['FacturarA'] = facturarA;
            }

            return Navigator.pushNamed(
              context,
              '/PagosPage',
              arguments: AppDAFacturas.documentos(context, jsonEncode(p0)),
            );
          },
        ),
      ];
      List<Widget> result = [
        SingleChildScrollView(
          child: Column(
            children: _formBody,
          ),
        )
      ];
      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      _formBodyRes = await createWidgets();
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }

  static documentos(BuildContext context, dynamic data) {
    Map<String, dynamic> _data = parseDynamic(data);
    Map<String, dynamic> _datos = parseDynamic(data);
    String _refID = 'documentos_facturas';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    FacturaController _facturaController = FacturaController();

    /* FutureOr onGoBack(Object? value) {
      _layoutListProv.isLoading = true;
      _layoutListProv.notify();
    }

    removeNull(String? value) {
      if (value == null || value == 'null') {
        return '';
      }
      return value;
    } */

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Seleccionar Documentos',
      hasBackButton: true,
      onNotification: () async {
        // Implementación del botón de notificaciones
        try {
          // Mostrar pantalla de notificaciones
          Navigator.pushNamed(context, '/WidgetsForm',
              arguments: AppDANotificaciones.index(context));
        } catch (e) {
          DAToast(context, "Error al mostrar notificaciones: ${e.toString()}",
              useFlutterToast: true);
        }
      },
      formBody: [],
      bottomNavigationBar: DABottomAppBar(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAPagos.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.payment),
                ),
                tooltip: 'Pago',
              ),
              Positioned(
                bottom: 0,
                child: Text('Pago', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAEdC.index(context),
                  );
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.book),
                ),
                tooltip: 'Estado de Cuenta',
              ),
              Positioned(
                bottom: 0,
                child: Text('EdC', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    '/PagosPage',
                    arguments: AppDAFacturas.index(context),
                  );
                },
                icon: Icon(Icons.receipt),
                tooltip: 'Facturas',
              ),
              Positioned(
                bottom: 0,
                child: Text('Facturas', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  _layoutListProv.scaffoldKey.currentState.openEndDrawer();
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.person),
                ),
                tooltip: 'Perfil',
              ),
              Positioned(
                bottom: 0,
                child: Text('Perfil', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                style: ButtonStyle(
                    backgroundColor:
                        WidgetStateProperty.all(Colors.transparent)),
                onPressed: () {
                  Navigator.pushNamed(context, '/WidgetsForm',
                      arguments: AppDAAyuda.index(context));
                },
                icon: Opacity(
                  opacity: 0.25,
                  child: Icon(Icons.help),
                ),
                tooltip: 'Ayuda',
              ),
              Positioned(
                bottom: 0,
                child: Text('Ayuda', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );

    createWidgets(List<Map<String, dynamic>> _data) async {
      List<Widget> _formBody = [
        PaymentTable(
          data: _data,
          columns: ['Mov', 'FechaEmision', 'Referencia'],
          confirmationColumns: ['Mov', 'MovID', 'Ejercicio', 'Periodo'],
          selectable: false,
          onConfirm: (p0, sum, facturarA) async {
            _facturaController.addToRoute(_datos['Empresa'].toString().trim());
            _facturaController.addToRoute(p0['Ejercicio'].toString());
            _facturaController.addToRoute(p0['Periodo'].toString());
            _facturaController.addToRoute('${p0['Mov']} ${p0['MovID']}',
                isFinal: true);

            try {
              DASessionProvider prov = DASessionProvider();
              http.Response _res = await http.get(Uri.parse(
                  "${prov.apiUri}/filesystem?path=${_facturaController.route}"));

              // Verificamos si la respuesta indica que no se encontró archivo
              final Map<String, dynamic>? resJson = _res.body.isNotEmpty
                  ? (RegExp(r'^\s*\{').hasMatch(_res.body)
                      ? (jsonDecode(_res.body) as Map<String, dynamic>?)
                      : null)
                  : null;

              if (resJson != null && resJson['Ok'] == -1) {
                throw Exception(
                    resJson['OkRef'] ?? 'No se encontró el archivo');
              }

              // Get the content type from response headers
              String? contentType = _res.headers['content-type'];
              String fileName = 'factura_${p0['MovID']}';

              if (contentType?.contains('application/pdf') ?? false) {
                fileName += '.pdf';
                await FileSaver.instance.saveFile(
                    name: fileName,
                    bytes: _res.bodyBytes,
                    mimeType: MimeType.pdf);
              } else if (contentType?.contains('application/xml') ?? false) {
                fileName += '.xml';
                await FileSaver.instance.saveFile(
                    name: fileName,
                    bytes: _res.bodyBytes,
                    mimeType: MimeType.other);
              } else if (contentType?.contains('application/zip') ?? false) {
                fileName += '.zip';
                await Share.shareXFiles(
                    [XFile.fromData(_res.bodyBytes, name: fileName)],
                    subject: fileName);
              }

              ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Archivo descargado: $fileName')));
            } catch (e) {
              _facturaController.reset();
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  content:
                      Text('Error al descargar el archivo: ${e.toString()}')));
            }
          },
        ),
      ];
      List<Widget> result = [
        SingleChildScrollView(
          child: Column(
            children: _formBody,
          ),
        )
      ];
      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      DARequestModel _req = ApiRequest.getEstadosDeCuenta(
        empresa: _data['Empresa'],
        cliente: _data['Cliente'],
        inmueble: _data['Inmueble'],
        local: _data['Local'],
      );
      dynamic _res = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
      _res = _res.where((item) => item['Mov'] == 'Cobro Inmobiliario').toList();
      List<Map<String, dynamic>> _docs = parseDynamicList(_res);

      _formBodyRes = await createWidgets(_docs);
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }
}
