import 'package:flutter/material.dart';
import 'package:pagosmovil/src/app_config/app_config.dart';
import 'package:pagosmovil/src/controllers/perfil_controller.dart';

import '/src/DAPackagesRef.dart';
import '/src/app_config/api_request.dart';

class AppDACambiarContrasena {
  static index(BuildContext context) {
    String _refID = 'cambiarContrasena';
    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    try {
      TextEditingController _controller = TextEditingController();
      DatosPerfil _perfil = DatosPerfil();

      DALayoutFormFilters _modelLayoutConfiguracion = new DALayoutFormFilters(
        heroTag: 'cambiarContrasenaScreen',
        refID: _refID,
        prefix: 'Cambiar Contraseña',
        title: '',
        hasBackButton: true,
        iconSubmit: Icons.check,
        formSubmit: (value) async {
          DARequestModel _req = ApiRequest.cambiarContrasena(_controller.text);
          dynamic _res = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
          if (_res.isEmpty) {
            _perfil.setChangingPassword(false);
            DAToast(context,
                'Contraseña cambiada correctamente, su sesión se cerrará en 5 segundos',
                useFlutterToast: true);
            await Future.delayed(Duration(seconds: 5));
            AppConfig.logOut();
          }
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(children: []),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        List<Widget> _formBody = [
          DAInput(
            refID: 'contrasena',
            controller: _controller,
            label: 'Nueva Contraseña',
            isRequired: true,
            disabled: false,
            tipo: DAInputType.string,
          ),
        ];
        return _formBody;
      }

      // Descargamos Catálogos y asignamos valores default
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        _formBodyRes = await createWidgets();
        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();
      return _modelLayoutConfiguracion;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }
}
