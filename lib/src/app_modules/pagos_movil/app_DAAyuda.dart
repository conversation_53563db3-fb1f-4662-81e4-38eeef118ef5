import 'package:flutter/material.dart';
import 'package:no_context_navigation/no_context_navigation.dart';

import '/src/DAPackagesRef.dart';

class AppDAAyuda {
  static index(BuildContext context) {
    String _refID = 'ayuda';
    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    try {
      DALayoutFormFilters _modelLayoutConfiguracion = new DALayoutFormFilters(
        heroTag: 'ayudaScreen',
        refID: _refID,
        prefix: 'Como Realizar Mi Pago',
        title: '',
        hasBackButton: false,
        iconSubmit: Icons.check,
        formSubmit: (value) async {
          navService.pushNamedAndRemoveUntil('home');
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(children: []),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        List<Widget> _formBody = [
          Card(
              child: ListTile(
                  onTap: () {},
                  title: Text('Paso 1 - Elige tu desarrollo'),
                  subtitle: Text(
                      'Da click en el desarrollo que quieres ingresar para comenzar a realizar tus pagos.'))),
          Card(
              child: ListTile(
                  onTap: () {},
                  title: Text('Paso 2 - Selecciona tu lote'),
                  subtitle: Text(
                      'Selecciona la manzana y lote que deseas pagar, si tienes pagos vencidos primero tendras que ponerte al corriente.'))),
          Card(
              child: ListTile(
                  onTap: () {},
                  title: Text('Paso 3 - Realiza tu pago'),
                  subtitle: Text(
                      'Ingresa los datos de tu tarjeta y realiza el pago. Recibiras la confirmación de pago en tu correo electrónico. Ademas, podrás obtener un recibo de pago si lo deseas.'))),
          Text(
            'En apego a la Ley Federal para la Prevención e Identificación de Operaciones con Recursos de Procedencia Ilícita le recordamos que el origen de los recursos deben coincidir con el titular del inmueble',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ];
        return _formBody;
      }

      // Descargamos Catálogos y asignamos valores default
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        _formBodyRes = await createWidgets();
        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();
      return _modelLayoutConfiguracion;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }
}
