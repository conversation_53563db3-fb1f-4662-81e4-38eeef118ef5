import 'package:flutter/material.dart';
import 'package:no_context_navigation/no_context_navigation.dart';

import '/src/DAPackagesRef.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class AppDAContacto {
  static index(BuildContext context) {
    String _refID = 'contacto';
    DALayoutFormWidgetsProvider _formProv =
        DALFormWgtQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    try {
      DALayoutFormFilters _modelLayoutConfiguracion = new DALayoutFormFilters(
        heroTag: 'contactoScreen',
        refID: _refID,
        prefix: 'Contacto',
        title: '',
        hasBackButton: false,
        iconSubmit: Icons.check,
        formSubmit: (value) async {
          navService.pushNamedAndRemoveUntil('home');
        },
        formBody: [],
        bottomNavigationBar: DABottomAppBar(children: []),
      );

      // Definimos lista de Widgets a mostrar
      createWidgets() async {
        List<Widget> _formBody = [
          Card(
            child: ListTile(
              leading: FaIcon(FontAwesomeIcons.whatsapp, color: Colors.green),
              title: Text('WhatsApp'),
              subtitle: Text('+52 442 157 9314'),
              onTap: () {
                launchUrl(Uri.parse('https://wa.me/524421579314'));
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: FaIcon(FontAwesomeIcons.envelope, color: Colors.blue),
              title: Text('Correo electrónico'),
              subtitle: Text('<EMAIL>'),
              onTap: () {
                launchUrl(Uri.parse('mailto:<EMAIL>'));
              },
            ),
          ),
          Card(
            child: ListTile(
              leading: Icon(Icons.access_time, color: Colors.orange),
              title: Text('Horario de Atención'),
              subtitle: Text(
                'Lunes a Jueves de 9:00 a 14:30 horas y de 16:30 a 19:00\n'
                'Viernes de 9:00 a 16:00 horas\n'
                'Call Center: 442 157 9314',
              ),
            ),
          ),
        ];
        return _formBody;
      }

      // Descargamos Catálogos y asignamos valores default
      downloadMedia() async {
        _formProv.isLoading = true;
        _formProv.bodyWidgets = [];

        _formBodyRes = await createWidgets();
        _formProv.isLoading = false;
        _formProv.bodyWidgets = _formBodyRes;
      }

      downloadMedia();
      return _modelLayoutConfiguracion;
    } catch (e) {
      DAToast(context, e.toString());
      _formProv.isLoading = false;
      _formProv.bodyWidgets = [];
    }
  }
}
