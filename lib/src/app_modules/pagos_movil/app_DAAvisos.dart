import 'package:easy_pdf_viewer/easy_pdf_viewer.dart';
import 'package:flutter/material.dart';
import 'package:no_context_navigation/no_context_navigation.dart';
import 'package:pagosmovil/src/controllers/aviso_privacidad_controller.dart';

import '/src/DAPackagesRef.dart';

class AppDAAvisos {
  static terminosCondiciones(BuildContext context, PDFDocument document) {
    String _refID = 'pagos';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Terminos y Condiciones',
      hasBackButton: true,
      formBody: [],
      isPDF: true,
      pdfPath: document,
      bottomNavigationBar: DABottomAppBar(
        children: [
          IconButton(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.transparent)),
            onPressed: () {
              navService.pushNamedAndRemoveUntil('home');
            },
            icon: Icon(Icons.home),
            tooltip: 'Inicio',
          ),
        ],
      ),
    );

    createWidgets() async {
      List<Widget> _formBody = [];
      List<Widget> result = _formBody;

      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      _formBodyRes = await createWidgets();
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }

  static dinamico(BuildContext context, PDFDocument document) {
    String _refID = 'pagos';
    DALayoutPaymentProvider _layoutListProv =
        DALPaymentQueueProv.getByRefID(_refID);
    List<Widget> _formBodyRes = [];

    DALayoutPaymentFilters modelLayout = new DALayoutPaymentFilters(
      refID: _refID,
      prefix: 'Lista de',
      title: 'Aviso de Privacidad',
      hasBackButton: false,
      formBody: [],
      isPDF: true,
      pdfPath: document,
      bottomNavigationBar: DABottomAppBar(
        children: [
          IconButton(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.transparent)),
            onPressed: () {
              DatosAvisoPrivacidad().documentSelected = '';
              navService.pushNamedAndRemoveUntil('home');
            },
            icon: Icon(Icons.home),
            tooltip: 'Inicio',
          ),
        ],
      ),
    );

    createWidgets() async {
      List<Widget> _formBody = [];
      List<Widget> result = _formBody;
      return result;
    }

    downloadData() async {
      _layoutListProv.isLoading = true;
      _layoutListProv.data = [];

      _formBodyRes = await createWidgets();
      _layoutListProv.isLoading = false; // Notificamos que cargo
      _layoutListProv.bodyWidgets = _formBodyRes;
    }

    // Configuramos data source para carga inicial y refresh
    modelLayout.dataSource = () async {
      await downloadData();
    };

    // Ejecutamos carga inicial de datos desde la configuración
    modelLayout.dataSource!();
    return modelLayout;
  }
}
