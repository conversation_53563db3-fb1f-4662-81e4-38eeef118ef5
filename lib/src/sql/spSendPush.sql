/**************** spSendPush ****************/ 
IF exists (SELECT * FROM sysobjects WHERE id = object_id('dbo.spSendPush') and type = 'P')
	DROP PROCEDURE dbo.spSendPush
GO
CREATE PROCEDURE spSendPush
		@NombreApp		nvarchar(max),
		@titulo			nvarchar(max),
		@mensaje		nvarchar(max),
		@destinatarios	nvarchar(max),
		@data			nvarchar(max),
		@Ok				int = 0 OUTPUT,
		@OkRef			varchar(255) = NULL OUTPUT
--//WITH ENCRYPTION
AS BEGIN
SET nocount ON
	DECLARE 
	@url		varchar(150),
	@usuario	varchar(20),
	@contrasena varchar(100)

	SELECT @url = url, @usuario = usuario, @contrasena = contrasena
	  FROM NotificacionesAPIFirst
	
	IF (NULLIF(@url, '') IS NOT NULL AND NULLIF(@usuario, '') IS NOT NULL AND NULLIF(@contrasena, '') IS NOT NULL)
	BEGIN
		SELECT * 
		  INTO #PushResult
		  FROM dbo.SendPushApp(@url, @NombreApp, @usuario, @contrasena, @titulo, @mensaje, @destinatarios,  @data)

		  SELECT @Ok = case NULLIF(Success,0) when 0 then 1 else null end, @OkRef = OkRef FROM #PushResult
		    DROP TABLE #PushResult
	END

	RETURN
SET NOCOUNT OFF
END
GO

SET ANSI_NULLS ON
SET QUOTED_IDENTIFIER ON
GO