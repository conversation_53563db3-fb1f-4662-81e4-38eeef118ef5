IF OBJECT_ID('spPGM_ObtenerDetallesCliente', 'P') IS NOT NULL
    DROP PROCEDURE spPGM_ObtenerDetallesCliente;
GO

CREATE PROCEDURE spPGM_ObtenerDetallesCliente
    @Cliente VARCHAR(10)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT 
        CT.Cliente AS Clave,
        CT.Nombre,
        RFC,
        CURP,
        Direccion + ' ' + ISNULL(DireccionNumeroInt, '') + ' ' + ISNULL(DireccionNumero, '') AS DireccionCompleta,
        Delegacion,
        CodigoPostal,
        Colonia,
        Pais,
        Estado,
        Telefonos AS Telefono,
        eMail AS Email,
        FiscalRegimen AS RegimenFiscal,
        ClaveUsoCFDI AS UsoCFDI
    FROM 
        Cte CT
		LEFT OUTER JOIN CteCFD CFD ON CFD.Cliente = CT.Cliente
    WHERE 
        CT.Cliente = @Cliente
END
GO
