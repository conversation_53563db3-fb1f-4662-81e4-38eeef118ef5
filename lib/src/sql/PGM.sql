/**************** dbo.fnNetUsuariosPagos ****************/
IF EXISTS (SELECT name
FROM sysobjects
WHERE name = 'fnNetUsuariosPagos' AND type = 'TF') DROP FUNCTION fnNetUsuariosPagos
GO
CREATE FUNCTION dbo.fnNetUsuariosPagos (@eMail varchar(50), @Contrasena varchar(100))
  RETURNS @Resultado TABLE(IDUsuario int,
    Cliente VARCHAR(20),
    Usuario varchar(10),
    Nombre varchar(100),
    RFC varchar(20),
    eMail varchar(150),
    IDUsuarioTipo INT,
    IDRole int,
    PersonalGastos varchar(10),
    Emp<PERSON>a varchar(5),
    Sucursal int)
AS BEGIN
    INSERT INTO @Resultado
        (IDUsuario, Cliente, Usuario, Nombre, RFC, eMail, IDUsuarioTipo, IDRole, PersonalGastos, Empresa, Sucursal)
    SELECT 1, C.<PERSON>, C.<PERSON>, C.<PERSON>, C.<PERSON>, @eMail eMail, 3, 3, NULL, NULL, NULL
    FROM Cte C
        INNER JOIN UsuarioPassCte UP ON UP.UsuarioCte = C.Cliente
    WHERE (CASE
        WHEN @eMail LIKE '%@%.%' THEN
            CASE WHEN C.eMail1 = @eMail THEN 1 ELSE 0 END
        ELSE
            CASE WHEN UP.UsuarioCte = @eMail THEN 1 ELSE 0 END
      END) = 1
        AND UP.Pass = dbo.fnPassword(@Contrasena)
    RETURN
END
GO

IF OBJECT_ID('spPGM_ObtenerDetallesCliente', 'P') IS NOT NULL
    DROP PROCEDURE spPGM_ObtenerDetallesCliente;
GO

CREATE PROCEDURE spPGM_ObtenerDetallesCliente
    @Cliente VARCHAR(10)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT 
        CT.Cliente AS Clave,
        CT.Nombre,
        RFC,
        CURP,
        Direccion + ' ' + ISNULL(DireccionNumeroInt, '') + ' ' + ISNULL(DireccionNumero, '') AS DireccionCompleta,
        Delegacion,
        CodigoPostal,
        Colonia,
        Pais,
        Estado,
        Telefonos AS Telefono,
        eMail AS Email,
        FiscalRegimen AS RegimenFiscal,
        ClaveUsoCFDI AS UsoCFDI
    FROM 
        Cte CT
		LEFT OUTER JOIN CteCFD CFD ON CFD.Cliente = CT.Cliente
    WHERE 
        CT.Cliente = @Cliente
END
GO

/************* sp_WEB_MovsPorPagar ************/
IF EXISTS (SELECT *
FROM sysobjects
WHERE id = object_id('sp_WEB_MovsPorPagar') and type = 'P') DROP PROCEDURE sp_WEB_MovsPorPagar
GO
CREATE PROCEDURE sp_WEB_MovsPorPagar


 @Empresa    char(5),
 @Cliente    varchar(15),
 @IDContraro int

AS BEGIN
DECLARE
@Cuenta int


SELECT @Cuenta=COUNT(0)
  FROM CXC
 WHERE Mov='Pena Convencional'
   AND Estatus='PENDIENTE'
   AND Cliente=@Cliente
   AND ContratoID=@IDContraro
   AND Empresa=@Empresa


 IF @Cuenta > 0
  BEGIN
   SELECT ID,Mov+' '+MovID AS Mov,Vencimiento,ISNULL(Referencia,' ') Referencia,ROUND(Saldo,2)  AS Total, MovID
     FROM CXC
    WHERE Mov='Pena Convencional'
      AND Estatus='PENDIENTE'
      AND Cliente=@Cliente
      AND ContratoID=@IDContraro
      AND Empresa=@Empresa
    ORDER BY FechaEmision
  END
 ELSE
   SELECT ID,Mov+' '+MovID AS Mov,Vencimiento,ISNULL(Referencia,' ') Referencia,ROUND(Saldo,2) AS Total, MovID
     FROM CXC
    WHERE Mov='Documento'
      AND Estatus='PENDIENTE'
      AND Cliente=@Cliente
      AND ContratoID=@IDContraro
     AND Empresa=@Empresa
  ORDER BY Vencimiento
 RETURN
END
GO

IF OBJECT_ID('spPGM_CambiarContrasenaCliente', 'P') IS NOT NULL
    DROP PROCEDURE spPGM_CambiarContrasenaCliente;
GO

CREATE PROCEDURE spPGM_CambiarContrasenaCliente
    @Cliente VARCHAR(10),
	@Contrasena VARCHAR(20)
AS
BEGIN
	UPDATE Cte Set Contrasena = @Contrasena WHERE Cliente = @Cliente
END
GO

/************* spPGM_FacturarA ************/
IF EXISTS (SELECT *
FROM sysobjects
WHERE id = object_id('spPGM_FacturarA') and type = 'P') DROP PROCEDURE spPGM_FacturarA
GO
CREATE PROCEDURE spPGM_FacturarA @Cliente VARCHAR(10)
AS BEGIN
SELECT e.Cliente,e.Nombre ,a.ID,a.Nombre AS NombreFacturarA, a.RFC --,*
FROM Cte e JOIN facturarA  a ON e.cliente= a.cliente
WHERE e.Cliente = @Cliente
END
GO

/************* spPGM_ObtenerDatosEdC ************/
IF EXISTS (SELECT *
FROM sysobjects
WHERE id = object_id('spPGM_ObtenerDatosEdC') and type = 'P') DROP PROCEDURE spPGM_ObtenerDatosEdC
GO

CREATE PROCEDURE spPGM_ObtenerDatosEdC
    @Param_Empresa   varchar(5),                                          
    @Param_Inmueble  varchar(15),                            
    @Param_Local     varchar(25)
AS
BEGIN
    -- Crear tabla temporal para resultados finales
    CREATE TABLE #ResultadosTemp (
        Concepto varchar(50),
        Valor money
    )

    -- Crear tabla temporal para cada resultado del SP
    CREATE TABLE #TempValor (Valor money)

    -- Total Cobrado del 01/05/2012 a la fecha (Turno 2)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 2, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Total Cobrado', Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Saldo Pendiente (Turno 3)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 3, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Saldo Pendiente', Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Saldo Vencido (Turno 5)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 5, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Saldo Vencido', Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Pena Vencida (Turno 6)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 6, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Pena Vencida', Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Pena Pagada (Turno 7)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 7, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Pena Pagada', Valor FROM #TempValor

    -- Mostrar resultados
    SELECT Concepto, Valor
    FROM #ResultadosTemp

    -- Limpiar tablas temporales
    DROP TABLE #TempValor
    DROP TABLE #ResultadosTemp
END
GO

-- Verifica si el SP existe y lo elimina si es así
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'spPGM_DesarrollosCte')
BEGIN
    DROP PROCEDURE spPGM_DesarrollosCte
END
GO

-- Crea el SP
CREATE PROCEDURE spPGM_DesarrollosCte
    @Cliente VARCHAR(10)
AS
BEGIN
    SET NOCOUNT ON;

    -- Verifica si @Cliente es NULL y lanza un error si lo es
    IF @Cliente IS NULL
    BEGIN
        RAISERROR('El parámetro @Cliente no puede ser NULL', 16, 1)
        RETURN
    END

    -- Consulta original
    SELECT ID, Mov + ' ' + MovID AS Mov, FechaEmision, ISNULL(Referencia, 'SR') Referencia, ContratoID, Empresa, Cliente
    FROM Cxc
    WHERE ContratoID IS NOT NULL
      AND Cliente = @Cliente
    ORDER BY Cliente, Empresa

    -- Consulta de Empresas únicas
    SELECT DISTINCT Empresa
    FROM Cxc
    WHERE ContratoID IS NOT NULL
      AND Cliente = @Cliente
    ORDER BY Empresa

    -- Consulta de ContratoID únicos con su Empresa correspondiente
    SELECT DISTINCT ContratoID, Empresa
    FROM Cxc
    WHERE ContratoID IS NOT NULL
      AND Cliente = @Cliente
    ORDER BY Empresa, ContratoID

END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'spPGM_TerrenosDesarrollo')
BEGIN
    DROP PROCEDURE spPGM_TerrenosDesarrollo
END
GO

CREATE PROCEDURE spPGM_TerrenosDesarrollo
    @Contrato VARCHAR(10)
AS
BEGIN
    IF @Contrato IS NULL
    BEGIN
        RAISERROR ('El parámetro @Contrato no puede ser NULL.', 16, 1)
        RETURN
    END

    SELECT 
        vc.Local,
        vc.Nombre,
        vc.NombreCorto,
        vc.Proyecto,
        vc.Uso,
        vc.Negociacion,
        vc.PermitirBorrar,
        vc.Inmueble,
        vc.Area,
        vc.SubArea,
        vc.Indiviso,
        vc.Factor1,
        vc.Factor2,
        vc.Factor3,
        vc.MesesRelComer,
        vc.FechaEstimOper,
        vc.PromPlanos,
        vc.Propio,
        vc.Complemento,
        vc.Descripcion,
        vc.Contrato AS ContratoVic,
        vc.Predial,
        vc.Nivel,
        vc.SubNivel,
        vc.ContratoCargoPorExcep,
        vc.Rama,
        vc.Estatus,
        vc.Tipo,
        vc.Cuenta,
        vc.Familia,
        vc.Categoria,
        vc.Grupo,
        vc.Usuario,
        vc.Unidad,
        vc.Medida,
        vc.MedidaEstimados,
        vc.PrecioVenta,
        vc.MonedaPrecioVenta,
        vc.CONTRATOID2,
        vc.Importe1,
        vc.Importe2,
        vc.Importe3,
        vc.APETipoPeriodoGracia,
        vc.APEPeriodoGracia,
        vc.APEInicioPeriodoGracia,
        vc.APECulminaPeriodoGracia,
        vc.APEZona,
        vc.Origen,
        vc.CostoEstandar,
        vc.CostoObra,
        vc.CostoTerreno,
        c.ID,
        c.Empresa,
        c.Mov,
        c.MovID,
        c.FechaEmision,
        c.UltimoCambio,
        c.ContactoTipo,
        c.Prospecto,
        c.Cliente,
        c.Proveedor,
        c.Personal,
        c.Agente,
        c.ContratoRama,
        c.UEN,
        c.Concepto,
        c.Proyecto AS ProyectoContrato,
        c.Usuario AS UsuarioContrato,
        c.Autorizacion,
        c.DocFuente,
        c.Observaciones,
        c.Referencia,
        c.Estatus AS EstatusContrato,
        c.Situacion,
        c.SituacionFecha,
        c.SituacionUsuario,
        c.SituacionNota,
        c.Moneda AS MonedaContrato,
        c.TipoCambio,
        c.RamaID,
        c.IDOrigen,
        c.OrigenTipo,
        c.Origen AS OrigenContrato,
        c.OrigenID,
        c.Ejercicio,
        c.Periodo,
        c.FechaRegistro,
        c.FechaConclusion,
        c.FechaCancelacion,
        c.Desde,
        c.Hasta,
        c.Sucursal,
        c.Prioridad,
        c.Comentarios,
        c.Documento,
        c.Titulo,
        c.Contrato AS ContratoContrato,
        c.FechaEscrituracion,
        c.FechaInicio,
        c.Inversionista,
        c.Importe,
        c.IVAImporte,
        c.ImporteTotal,
        c.SincroID,
        c.SincroC,
        c.SucursalOrigen,
        c.SucursalDestino,
        c.ClaseContrato,
        c.vicClienteEnviarA,
        c.vicGenerarComision,
        c.vicCxpModuloNombre,
        c.vicCxpMov,
        c.vicProveedor,
        c.vicCxpEmpresa,
        c.vicCxpConcepto,
        c.vicCxpReferencia,
        c.vicCxcModuloNombre,
        c.vicArticulo,
        c.vicCxcMov,
        c.vicCliente,
        c.vicCxcEmpresa,
        c.vicCxcConcepto,
        c.vicCxcReferencia,
        c.vicCxcComisionID,
        c.vicCxpComisionID,
        c.InteresCO,
        c.vicSubClave,
        c.ClaveOpus,
        c.IDOpus,
        c.vicConvertirMon,
        c.vicConvertirMonOrigen,
        c.vicConvertirMonOrigenTC,
        c.vicConvertirMonDestino,
        c.vicConvertirMonDestinoTC,
        c.ExtID,
        c.ExtClave,
        c.vicGFAComisionDEstatus,
        c.vicGFAComisionDPrecio,
        c.GFASituacionComis,
        c.GFASituacionFechaComis,
        c.GFASituacionUsuarioComis,
        c.GFASituacionNotaComis,
        c.vicGFASeguimientoCancelacion,
        c.CRMObjectId,
        c.ProyectoID,
        c.CRMFolio,
        c.CRMLastUpdate,
        c.CRMLastUpdate2
    FROM 
        vic_local vc 
    JOIN 
        Contrato c ON vc.Contrato = c.ID
    WHERE 
        vc.Contrato = @Contrato;
END
GO

CREATE VIEW vwUsuariosPagos AS
SELECT 
    C.Cliente AS IDUsuario, 
    C.Cliente AS Cliente, 
    C.Cliente AS Usuario, 
    C.Nombre AS Nombre, 
    C.RFC AS RFC, 
    C.eMail1 AS eMail, 
    3 AS IDUsuarioTipo, 
    3 AS IDRole, 
    NULL AS PersonalGastos, 
    NULL AS Empresa, 
    NULL AS Sucursal
FROM Cte C
INNER JOIN UsuarioPassCte UP ON UP.UsuarioCte = C.Cliente
