/************* sp_WEB_MovsPorPagar ************/
IF EXISTS (SELECT *
FROM sysobjects
WHERE id = object_id('sp_WEB_MovsPorPagar') and type = 'P') DROP PROCEDURE sp_WEB_MovsPorPagar
GO
CREATE PROCEDURE sp_WEB_MovsPorPagar


 @Empresa    char(5),
 @Cliente    varchar(15),
 @IDContraro int

AS BEGIN
DECLARE
@Cuenta int


SELECT @Cuenta=COUNT(0)
  FROM CXC
 WHERE Mov='Pena Convencional'
   AND Estatus='PENDIENTE'
   AND Cliente=@Cliente
   AND ContratoID=@IDContraro
   AND Empresa=@Empresa


 IF @Cuenta > 0
  BEGIN
   SELECT ID,Mov+' '+MovID AS Mov,Vencimiento,ISNULL(Referencia,' ') Referencia,ROUND(Saldo,2)  AS Total, MovID
     FROM CXC
    WHERE Mov='Pena Convencional'
      AND Estatus='PENDIENTE'
      AND Cliente=@Cliente
      AND ContratoID=@IDContraro
      AND Empresa=@Empresa
    ORDER BY FechaEmision
  END
 ELSE
   SELECT ID,Mov+' '+MovID AS Mov,Vencimiento,ISNULL(Referencia,' ') Referencia,ROUND(Saldo,2) AS Total, MovID
     FROM CXC
    WHERE Mov='Documento'
      AND Estatus='PENDIENTE'
      AND Cliente=@Cliente
      AND ContratoID=@IDContraro
     AND Empresa=@Empresa
  ORDER BY Vencimiento
 RETURN
END
GO