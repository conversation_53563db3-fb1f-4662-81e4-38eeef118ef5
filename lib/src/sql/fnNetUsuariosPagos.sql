/**************** dbo.fnNetUsuariosPagos ****************/
IF EXISTS (SELECT name
FROM sysobjects
WHERE name = 'fnNetUsuariosPagos' AND type = 'TF') DROP FUNCTION fnNetUsuariosPagos
GO
CREATE FUNCTION dbo.fnNetUsuariosPagos (@eMail varchar(50), @Contrasena varchar(100))
  RETURNS @Resultado TABLE(IDUsuario int,
    Cliente VARCHAR(20),
    Usuario varchar(10),
    Nombre varchar(100),
    RFC varchar(20),
    eMail varchar(150),
    IDUsuarioTipo INT,
    IDRole int,
    PersonalGastos varchar(10),
    Emp<PERSON>a varchar(5),
    Sucursal int)
AS BEGIN
    INSERT INTO @Resultado
        (IDUsuario, Cliente, Usuario, Nombre, RFC, eMail, IDUsuarioTipo, IDRole, PersonalGastos, Empresa, Sucursal)
    SELECT 1, C.<PERSON>, C.<PERSON>, C.<PERSON>, C.<PERSON>, @eMail eMail, 3, 3, NULL, NULL, NULL
    FROM Cte C
        INNER JOIN UsuarioPassCte UP ON UP.UsuarioCte = C.Cliente
    WHERE (CASE
        WHEN @eMail LIKE '%@%.%' THEN
            CASE WHEN C.eMail1 = @eMail THEN 1 ELSE 0 END
        ELSE
            CASE WHEN UP.UsuarioCte = @eMail THEN 1 ELSE 0 END
      END) = 1
        AND UP.Pass = dbo.fnPassword(@Contrasena)
    RETURN
END
GO