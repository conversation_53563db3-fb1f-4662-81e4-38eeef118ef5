CREATE PROCEDURE spPANGEAEdoCtaWEB             
 @Empresa  char(5)     ,      
 @Cliente  varchar(15)     ,      
 @Inmueble varchar(90)=NULL     ,      
 @Local    varchar(90)=NULL              
      
AS  
IF 1=0 BEGIN  
    SET FMTONLY OFF  
END  
 BEGIN                     
 IF @Inmueble = 'NULL'       
  SET @Inmueble=NULL             
        
 IF @Local    = 'NULL'       
  SET @Local=NULL          
      
  CREATE TABLE #Conceptos (ID int Identity,monto money)    
        
  IF @Inmueble = 'NULL' SET @Inmueble=NULL       
  IF @Local    = 'NULL' SET @Local=NULL       
      
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,1,@Inmueble,@Local    
      
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,2,@Inmueble,@Local    
    
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,3,@Inmueble,@Local    
       
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,5,@Inmueble,@Local    
      
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,7,@Inmueble,@Local    
      
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,6,@Inmueble,@Local    
  

             
  SELECT ROW_NUMBER() OVER(ORDER BY Cxc.Cliente,Cxc.ContratoID,vc.Inmueble,vc.Local,Cxc.Vencimiento ASC) IDRep, DENSE_RANK () OVER(ORDER BY Cxc.Cliente,Cxc.ContratoID,vc.Inmueble,vc.Local) AS Grupo,                   
         vc.Cliente,vc.ID,vc.Inmueble,vc.Local,vc.Nombre,vc.Direccion,vc.DireccionNumero,                   
         vc.Poblacion,vc.Colonia,vc.Delegacion,vc.Telefonos,vc.alergias,vc.eMail1,                   
         vc.Titulo,vc.EstadoCivil,vc.Religion,vc.RFC,vc.FechaContrato,vc.MovID AS MovIDLocal,                    
         vc.NombreEmpresa,vc.RFCEmpresa,vc.Articulo,vc.NombreLocal,vc.medida,                   
         vc.descripcion,Cxc.Moneda,Cxc.Concepto,Cxc.Ejercicio,Cxc.Periodo,Cxc.Referencia,                   
         Cxc.Origen,Cxc.OrigenID,Cxc.Mov,Cxc.MovID,Cxc.Vencimiento,Cxc.Observaciones,                      
         Cxc.FechaEmision,Cxc.Estatus,Cxc.FormaCobro,vf.Escrituracion,vf.Saldo,va.MEN1,                   
         va.MEN2,va.MEN3,va.MEN4,va.MEN5,va.MEN6,va.MEN7,va.MEN8,vf.Finiquito,vf.Apartado,                  
         vf.Enganche,vf.Anualidad,    
         Cargo = (CASE WHEN MovTipo.Clave IN ('CXC.F','CXC.D','CXC.DC','CXC.CD') THEN ISNULL(CXC.Importe,0) + ISNULL(CXC.Impuestos,0) ELSE NULL END),                     
         Abono = (CASE WHEN MovTipo.Clave IN ('CXC.C','CXC.A','CXC.ANC', /*agregado 21/09/2015*/'CXC.AJM', 'DIN.RND') THEN Cxc.Importe ELSE NULL END),       
         EstatusCat=(SELECT Categoria FROM vic_Contrato WHERE ID =(SELECT Contrato FROM vic_Local WHERE Local =vc.NombreLocal)),    
         Asesor =(SELECT Familia FROM vic_contrato WHERE ID=vc.ID),    
         Monto1= (SELECT Monto FROM #Conceptos WHERE ID=1),    
         Monto2= (SELECT Monto FROM #Conceptos WHERE ID=2),    
         Monto3= (SELECT Monto FROM #Conceptos WHERE ID=3),    
         Monto4=vf.Escrituracion-(SELECT Monto FROM #Conceptos WHERE ID=3),    
         Monto5=(SELECT Monto FROM #Conceptos WHERE ID=4),    
         Monto6=(SELECT Monto FROM #Conceptos WHERE ID=5),    
         Monto7=(SELECT Monto FROM #Conceptos WHERE ID=6)      
    FROM Cxc JOIN MovTipo ON Cxc.Mov=MovTipo.Mov  AND MovTipo.Modulo IN ('CXC') AND MovTipo.Clave IN ('CXC.F','CXC.C', 'CXC.A','CXC.D','CXC.ANC','CXC.DC','CXC.CD', /*agregado 21/09/2015*/'CXC.AJM')                        
             JOIN vicVWCTOCliente  vc  ON Cxc.ContratoID=vc.ID                       
             JOIN vicVWCTOSFactura vf  ON vc.ID=vf.IDContrato                        
             JOIN vicVWCTOArticulo va  ON vc.ID=va.IDContrato             
   WHERE Cxc.Estatus IN ('PENDIENTE','CONCLUIDO')               
     AND Cxc.Mov NOT IN ('Apartado','Enganche','Mensualidades','Anualidad', '%Pena%', 'Finiquito')                
     AND Cxc.Cliente=@Cliente                
     AND Cxc.Empresa=@Empresa               
     AND vc.Inmueble=ISNULL(@Inmueble,vc.Inmueble) --'ARF3M003E1'                
     AND vc.Local   =ISNULL(@Local,vc.Local)       --'ARF3M003E1-L014'                
     AND (CASE WHEN(SELECT TOP 1 Mov FROM Cxc m  JOIN CxcD n ON n.ID=m.ID   WHERE n.Aplica=Cxc.Mov AND n.AplicaID=Cxc.MovID  AND m.Estatus='CONCLUIDO') = 'Cancelar Documento'  THEN 1  ELSE 0  END) <>1               
    ORDER BY Cxc.Cliente,Cxc.ContratoID,vc.Inmueble,vc.Local,Cxc.Vencimiento ASC         
  RETURN             
END 
