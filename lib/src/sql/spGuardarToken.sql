/*********** spGuardarToken ***************/
if exists ( select * from sysobjects where id = object_id('dbo.spGuardarToken') and type = 'P')
    drop procedure dbo.spGuardarToken
GO
CREATE PROCEDURE dbo.spGuardarToken
    @Token NVARCHAR(MAX),
    @Usuario NVARCHAR(10),
    @Aplicacion NVARCHAR(50),
    @Logout BIT
AS
BEGIN
    BEGIN TRY
        IF(@Logout = 1)
        BEGIN
			UPDATE PushDispositivos SET Fecha = GETDATE(), Estatus = 'Inactivo'
				WHERE Usuario = @Usuario AND Aplicacion = @Aplicacion
			SELECT N'Token desactivado correctamente'
			RETURN
		END

        DECLARE @TokenActual NVARCHAR(MAX)
        SELECT @TokenActual = [Token]
		FROM PushDispositivos
		WHERE Usuario = @Usuario AND Aplicacion = @Aplicacion

        -- Primer inicio de sesion
        IF(@TokenActual IS NULL)
        BEGIN
			INSERT INTO PushDispositivos
			VALUES(@Usuario, @Aplicacion, @Token, GETDATE(), 'Activo')
			SET @TokenActual = @Token
			SELECT N'Token creado correctamente'
			RETURN
		END

        -- Inicio de sesion en otro dispositivo
        IF(@TokenActual <> @Token)
        BEGIN
			UPDATE PushDispositivos SET Token = @Token, Fecha = GETDATE(), Estatus = 'Activo'
            WHERE Usuario = @Usuario AND Aplicacion = @Aplicacion
			SELECT N'Token actualizado correctamente'
			RETURN
		END

        -- Inicio de sesion en el mismo dispositivo
        IF(@TokenActual = @Token)
        BEGIN
			UPDATE PushDispositivos SET Fecha = GETDATE(), Estatus = 'Activo'
            WHERE Usuario = @Usuario AND Aplicacion = @Aplicacion
			SELECT N'Hora de registro actualizada'
			RETURN
		END

    END TRY
    BEGIN CATCH
        SELECT CONCAT('Error en línea: ', ERROR_LINE(), 'Error: ', ERROR_MESSAGE()) AS Error
    END CATCH
END
GO
