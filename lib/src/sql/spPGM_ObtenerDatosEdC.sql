/************* spPGM_ObtenerDatosEdC ************/
IF EXISTS (SELECT *
FROM sysobjects
WHERE id = object_id('spPGM_ObtenerDatosEdC') and type = 'P') DROP PROCEDURE spPGM_ObtenerDatosEdC
GO

CREATE PROCEDURE spPGM_ObtenerDatosEdC
    @Param_Empresa   varchar(5),                                          
    @Param_Inmueble  varchar(15),                            
    @Param_Local     varchar(25)
AS
BEGIN
    -- Crear tabla temporal para resultados finales
    CREATE TABLE #ResultadosTemp (
        Concepto varchar(50),
        Valor money
    )

    -- Crear tabla temporal para cada resultado del SP
    CREATE TABLE #TempValor (Valor money)

    -- Total Cobrado del 01/05/2012 a la fecha (Turno 2)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 2, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Total Cobrado', Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Saldo Pendiente (Turno 3)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 4, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Saldo Pendiente al ' + CONVERT(varchar(10), GETDATE(), 103), Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Saldo Vencido (Turno 5)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 5, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Saldo Vencido al ' + CONVERT(varchar(10), GETDATE(), 103), Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Pena Vencida (Turno 6)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 6, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Pena Moratoria Por Pagar', Valor FROM #TempValor
    TRUNCATE TABLE #TempValor

    -- Pena Pagada (Turno 7)
    INSERT INTO #TempValor
    EXEC spEdoCtaCtePangea @Param_Empresa, 1, 7, @Param_Inmueble, @Param_Local
    INSERT INTO #ResultadosTemp
    SELECT 'Pena Moratoria Pagada', Valor FROM #TempValor

    -- Mostrar resultados
    SELECT Concepto, Valor
    FROM #ResultadosTemp

    -- Limpiar tablas temporales
    DROP TABLE #TempValor
    DROP TABLE #ResultadosTemp
END
GO