CREATE PROCEDURE spEdoCtaCtePangea                                                                                    
  @Empresa   varchar(5),                           
  @Estacion  int,                           
  @Turno     int,                           
  @Inmueble  varchar(15),                            
  @Local     varchar (25)                             
AS BEGIN                                                                                     
       
 DECLARE                                                                                             
        
  @Cargos  money,                           
       
  @Abonos  money,                           
        
  @Saldo   money,                                               
        
  @Valor   int                              
        
CREATE TABLE #EkoEdoCta   (ID int identity (1,1),                            
      
         Cliente        varchar (10) NULL,                            
      
   Cargo          money       NULL,                            
     
         Abono          money       NULL    )                                                                                                
      
 --<PERSON>álculo de total cobrado hasta antes del 30/04/2012                         
      
 IF @Turno = 1                           
      
  BEGIN                            
     
  INSERT INTO #EkoEdoCta                            
      
  SELECT     DISTINCT c1.cliente,                            
--modificacionabril2016      
    
       Cargo = ISNULL((SELECT SUM(vc.importe)                                
     
       FROM vic_Condicion vc                                
        WHERE vc.IDContrato=c1.ID                                
      
            AND vc.Estatus = 'ACTIVA'                                
       
           AND vc.SerieFactura = 'Saldo'),0),                            
      
    Abono = ISNULL((SELECT SUM(Cxc.Importe)                                
      
           FROM Cxc                                
      
           WHERE Cxc.ContratoID = c1.ID                                
       
          AND cxc.ContratoMov = c1.mov                                
      
           AND Cxc.Empresa =c1.Empresa                                
            AND Cxc.Cliente =c1.Cliente                                
            AND Cxc.Mov = 'Documento SI'                                
           AND Cxc.Estatus <> 'CANCELADO'),0)     
         
     --Linea Agregada 12/02/2018'    
    
   -ISNULL((SELECT SUM(CxcAux.Abono)                
      
            FROM Cxc, CxcAux       
            WHERE Cxc.Mov = 'Cancelar Documento'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Documento SI'       
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)                                
     
  FROM Contrato c1                            
  left outer JOIN vic_Condicion vc ON c1.ID = vc.IDContrato                            
 left outer JOIN VIC_Contrato vic ON c1.ID = vic.ID                         
   and vic.Inmueble =ISNULL(@Inmueble, vic.Inmueble)                            
   left outer join Cxc on Cxc.ContratoID =c1.ID                         
       
  and Cxc.ContratoMovID = c1.MovID                          
  AND Cxc.Cliente = c1.Cliente                                                                                          
    left outer join MovTipo m1 ON cxc.Mov = m1.Mov                         
        
  And m1.Modulo = 'CXC' and m1.Mov = 'Documento SI'                            
        
  where vc.Local =@Local                         
       
  AND c1.Estatus in ('Vigente','Concluido')        
      
ORDER BY c1.Cliente                           
        
  END            
      
 --Cáclulo de total cobrado del 01/05/2012 a la fecha                           
        
 IF @Turno = 2                           
BEGIN                            
       
  SELECT Cargo= ISNULL(SUM(Cxc.Importe),0)                        
      
     --Código agregado el 28/08/2015                               
      
     + ISNULL((SELECT SUM(Cxc.Importe)                                 
     
        FROM Cxc                                 
       WHERE ContratoID = (                                  
        SELECT IDContrato        
  FROM vic_Condicion                                  
       WHERE Local = @Local                                  
     and Estatus = 'ACTIVA')                                
     AND Mov = 'Reasignacion de Lote'                    
      and Estatus<>'SINAFECTAR'                                
         AND ESTATUS <> 'CANCELADO'), 0)                               
      
     
     - ISNULL((SELECT SUM(Cxc.Importe +Cxc.Impuestos)                                 
     
        FROM Cxc                                 
       
  WHERE ContratoID = (                  
     
          SELECT IDContrato                                  
           FROM vic_Condicion                                  
      WHERE Local = @Local                           
           and Estatus = 'ACTIVA')                                
       
    AND Mov = 'Devolucion Venta'                                
      
     AND ESTATUS <> 'CANCELADO'), 0)                        
        
   - ISNULL((SELECT SUM(Cxc.Importe +Cxc.Impuestos)                                 
        
     FROM Cxc                                 
        
  WHERE ContratoID = (                            
      
        SELECT IDContrato                                  
      
        FROM vic_Condicion                                  
        
  WHERE Local = @Local                                  
        
  and Estatus = 'ACTIVA')                                
        
  AND Mov = 'Devolucion'                                
  AND ESTATUS <> 'CANCELADO'), 0)                              
      
        
     --Fin de código agregado      
      
                           
        
     --Código agregado el 21/09/2015                                
        
  -ISNULL((SELECT SUM(Cxc.Importe)                                
       
        FROM Cxc                                
        WHERE ContratoID = (                                 
         
          SELECT IDContrato                                 
      
           FROM vic_Condicion                                 
        
          WHERE Local = @Local                                 
        
    and Estatus = 'ACTIVA')                               
        
        AND Mov IN ('Mensualidad Recl', 'Mensualidad Recl CFD')                               
        
        AND ESTATUS <> 'CANCELADO'), 0)                         
       - ISNULL((SELECT SUM(Cxc.Importe)                                
       FROM Cxc                                
       WHERE ContratoID = (                                 
       
  SELECT IDContrato                                 
        
  FROM vic_Condicion                                 
       
        WHERE Local = @Local                                 
        
  and Estatus = 'ACTIVA')                               
      
  AND Mov IN ('Recla Cobro', 'Recla Cobro CFD')                               
       
  AND ESTATUS <> 'CANCELADO'), 0)                               
        
     --Fin de código agregado                        
       --Código agregado 24/09/2015                                   
        
     + ISNULL((SELECT SUM(Cxc.Importe)                                
      
        FROM Cxc                                
        WHERE ContratoID = (                                 
        SELECT IDContrato                                 
         FROM vic_Condicion                                 
         WHERE Local = @Local                                 
           and Estatus = 'ACTIVA')                               
         AND Mov = 'Ajuste'                               
     AND ESTATUS <> 'CANCELADO'), 0)             
     
--Fin de código agregado                        
      --Código agregado el 26/11/2015                               
       
     - ISNULL((SELECT SUM(CxcAux.Abono)                               
      
        FROM Cxc, CxcAux                               
WHERE Cxc.Mov = 'Aplicacion'                               
          AND Cxc.ID = CxcAux.ModuloID                               
          AND Aplica = 'Pena Convencional'                               
          AND Cxc.Estatus = 'CONCLUIDO'                               
          AND ContratoID =       (        
   SELECT Contrato                                 
            FROM vic_Local                                 
           WHERE Local = @Local )), 0)                               
      --Fin de código agregado                        
       --Código agregado el 27/11/2015                        
       
     -ISNULL((SELECT SUM(Cxcaux.Abono)                        
       FROM Cxc, CxcAux                        
       WHERE Cxc.Mov = 'Cobro Inmobiliario'                        
       AND CxcAux.Aplica IN ('Cheque Devuelto')                        
       AND Cxc.Estatus = 'CONCLUIDO'                     
       AND Cxc.ID = CxcAux.ModuloID                        
       AND ContratoID =                        
          (                        
      
          SELECT Contrato                        
          FROM vic_Local                        
          WHERE Local = @Local                        
          )                 
          GROUP BY Cxc.ID                        
      
        HAVING SUM(Importe) % SUM(Abono) <> 0) , 0)      
      
      
  +ISNULL((SELECT SUM(cxc.importe)                        
       FROM Cxc, CxcAux                        
       WHERE Cxc.Mov = 'Cobro Inmobiliario'                        
       AND CxcAux.Aplica IN ('Cheque Devuelto')                        
       AND Cxc.Estatus = 'CONCLUIDO'                     
       AND Cxc.ID = CxcAux.ModuloID                        
       AND ContratoID =                        
          (                        
      
          SELECT Contrato                        
          FROM vic_Local                        
          WHERE Local = @Local                        
          )                 
          GROUP BY Cxc.ID,cxc.importe                        
      
        HAVING SUM(Importe) % SUM(Abono) <> 0) , 0)                       
        
     --Fin de código agregado                        
     --Código agregado el 30/11/2015                        
      - ISNULL((SELECT SUM(aux1.Abono)                               
         FROM Cxc, CxcAux aux1                        
         INNER JOIN CxcAux aux2 ON aux1.ModuloID = aux2.ModuloID                              
         WHERE Cxc.Mov = 'Aplicacion'                                        
         AND Cxc.ID = aux1.ModuloID                               
         AND aux1.Aplica = 'Pena Convencional'                        
         AND aux2.Aplica = 'Anticipo'                               
         AND Cxc.Estatus = 'CONCLUIDO'                                   
         AND ContratoID =       (                                
           SELECT Contrato                                 
            FROM vic_Local                                 
           WHERE Local = @Local )),0)                        
      
     --Fin de código agregado                        
     --Código agregado el 03/12/2015                        
    /*-ISNULL((SELECT SUM(CxcD.Importe)                               
         FROM Cxc, CxcD                               
 WHERE Cxc.ID = CxcD.ID                                     
  AND Aplica = 'Redondeo'                               
    AND Cxc.Estatus = 'CONCLUIDO'         
    AND ContratoID =(                                
    SELECT Contrato                                 
    FROM vic_Local                                 
    WHERE Local = @Local)),0)*/                        
   --Fin de código agregado                        
      
     - ISNULL((SELECT SUM(CxcAux.Abono)                               
       FROM Cxc, CxcAux                               
       WHERE Cxc.Mov = 'Cobro Inmobiliario'                               
        AND Cxc.ID = CxcAux.ModuloID                               
          AND Aplica = 'Pena Convencional'                               
          AND Cxc.Estatus = 'CONCLUIDO'                               
    AND ContratoID =       (                                
          SELECT Contrato                                 
  FROM vic_Local                                 
  WHERE Local = @Local)), 0)                        
   --Código agregado el 18/01/2016                        
       + ISNULL((SELECT SUM(CxcAux.Cargo)                               
         FROM Cxc, CxcAux                               
     WHERE Cxc.Mov = 'Devolucion'          
    AND Cxc.ID = CxcAux.ModuloID                 
AND Aplica = 'Nota Credito FZB'                               
    AND Cxc.Estatus = 'CONCLUIDO'                               
    AND ContratoID =       (                                
     SELECT Contrato                                 
      FROM vic_Local                                 
      WHERE Local = @Local)), 0)                        
       --Fin de código agregado                        
  --Código agregado el 07/12/2015                        
   /*+ ISNULL((SELECT SUM(Importe)            
        FROM Cxc          
     WHERE Mov = 'Neteo'            
 AND Estatus = 'CONCLUIDO'                        
   AND ContratoID = (                     
   SELECT Contrato                        
       FROM vic_Local                        
       WHERE Local = @Local                        
       )),0)*/                        
       --Fin de código agregado                        
  FROM Cxc JOIN MovTipo m1 ON cxc.Mov = m1.Mov                             
  And m1.Modulo ='CXC'                             
  and m1.Clave  IN ('CXC.C','CXC.A','CXC.ANC')                             
  and ISNULL(Cxc.Origen,'') NOT IN ( 'Cheque Devuelto', 'Pena Convencional')                             
  AND cxc.Estatus <> 'CANCELADO'                             
  JOIN Cte c ON CXC.Cliente = c.Cliente                             
  JOIN Contrato c1 ON CXC.ContratoID = c1.ID                         
  and Cxc.Empresa =c1.Empresa                             
  and c1.Estatus in('VIGENTE','CONCLUIDO')                         
  left outer JOIN vic_Condicion vc ON c1.ID = vc.IDContrato                          
  JOIN VIC_Contrato vic ON c1.ID = vic.ID                              
  and vic.Inmueble =ISNULL(@Inmueble, vic.Inmueble)                             
  WHERE vc.Local = @Local                        
  RETURN                       
  END                           
   --Cálculo de total cobrado                        
  IF @Turno = 3                           
   BEGIN                        
       
  INSERT INTO #EkoEdoCta                            
      
  SELECT     distinct c1.cliente,                            
     Cargo = ISNULL((SELECT SUM(vc.importe)                                 
         FROM vic_Condicion vc                                 
         WHERE vc.IDContrato=c1.ID                                 
         AND vc.Estatus = 'ACTIVA'                                
         AND vc.SerieFactura = 'Saldo'),0),                                 
      Abono = ISNULL((SELECT SUM(Cxc.Importe)                                 
      FROM Cxc                                  
  WHERE Cxc.ContratoID = c1.ID                                  
     AND cxc.ContratoMov = c1.mov                                  
    AND Cxc.Empresa =c1.Empresa                                  
    And Cxc.Cliente = c1.Cliente                                  
    and Cxc.Mov = 'Documento SI'                                  
   and Cxc.Estatus <> 'CANCELADO'),0)                               
        
  - ISNULL((SELECT SUM(CXC.Importe)                                  
    FROM Cxc                                  
    WHERE Cxc.ContratoID = c1.ID                                  
    AND cxc.ContratoMov = c1.mov                              
    And Cxc.Cliente = c1.Cliente                                  
    AND Cxc.Empresa =c1.Empresa                                  
    and Cxc.Mov = 'Cobro'                            
    and ISNULL(Cxc.Origen,'') <> 'Cheque Devuelto'                                  
    and Cxc.Estatus <> 'CANCELADO'),0)     
     
 --Linea agregada 04/10/2016    
     
 /* +ISNULL((SELECT SUM(CXC.Saldo)                                  
  FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        AND Cxc.Empresa =c1.Empresa                                  
        And Cxc.Cliente = c1.Cliente          
        and Cxc.Mov = 'Cheque Devuelto'                                  
        and Cxc.Estatus='PENDIENTE'),0)   */  
      
  --Linea agregada 09/02/2018     
    
  +ISNULL((SELECT SUM(CXC.Importe)                                  
        FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        AND Cxc.Empresa =c1.Empresa                                  
        And Cxc.Cliente = c1.Cliente          
        and Cxc.Mov = 'Cheque Devuelto'                                  
        and Cxc.Estatus IN('Concluido','PENDIENTE')),0)    
      
  --fin      
    
                               
      
      -ISNULL((SELECT SUM(CXC.Importe)                                
      FROM Cxc                                  
      WHERE Cxc.ContratoID = c1.ID                                  
      AND cxc.ContratoMov = c1.mov                       
       And Cxc.Cliente = c1.Cliente                                  
      AND Cxc.Empresa =c1.Empresa                                  
     and Cxc.Mov IN ( 'Cobro Inmobiliario')                                  
     and ISNULL(Cxc.Origen,'') <> 'Cheque Devuelto'                 
     and Cxc.Estatus in ('PENDIENTE','VIGENTE','CONCLUIDO')),0)      
      
  -- Linea Agregada 18/10/2016    
                     
      
    -ISNULL((SELECT SUM(CXC.Importe)                                  
        FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        AND Cxc.Empresa =c1.Empresa                                  
        And Cxc.Cliente = c1.Cliente          
        and Cxc.Mov = 'Aplicacion'                                  
        and Cxc.Estatus = 'CONCLUIDO'),0)     
    
  --linea nueva 07/06/21017    
    
  +ISNULL((SELECT SUM(CXC.Importe)                                  
        FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        AND Cxc.Empresa =c1.Empresa                                  
        And Cxc.Cliente = c1.Cliente          
        and Cxc.Mov = 'Ajuste Redondeo'                                  
        and Cxc.Estatus = 'CONCLUIDO'),0)     
    
  --linea nueva 01/03/2017    
    
  -ISNULL((SELECT SUM(CXC.Importe)                                  
        FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        AND Cxc.Empresa =c1.Empresa                                  
        And Cxc.Cliente = c1.Cliente          
        and Cxc.Mov = 'Aplicacion Dep Ident'                                  
        and Cxc.Estatus = 'CONCLUIDO'),0)     
      
      
  -- Linea Nueva 24/10/2016        
  -ISNULL((SELECT SUM(CXC.Importe)                                  
        FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        AND Cxc.Empresa =c1.Empresa                                  
        And Cxc.Cliente = c1.Cliente          
        and Cxc.Mov = 'Cancelar Deuda'                                  
        and Cxc.Estatus = 'CONCLUIDO'),0)     
    
  --Linea Agregada 12/02/2018'    
    
      
  /* -ISNULL((SELECT SUM(cxc.importe)         
      
            FROM Cxc, CxcAux       
WHERE Cxc.Mov = 'Cancelar Documento'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica IN ('Documento SI', 'Mensualidad Recl', 'Mensualidad Recl CFD')      
            AND Cxc.Estatus = 'CONCLUIDO'      
            AND ContratoID =       (                                
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)*/    
    
                                
       
   +ISNULL((SELECT SUM(CXC.Importe)                                  
    FROM Cxc                                  
    WHERE Cxc.ContratoID = c1.ID                  
    AND cxc.ContratoMov = c1.mov                                  
    AND Cxc.Empresa =c1.Empresa                                  
    And Cxc.Cliente = c1.Cliente                                  
    and Cxc.Mov = 'Reasignacion de Lotes D'                                  
    and Cxc.Estatus <> 'CANCELADO'),0)                       
      
        
       -ISNULL((SELECT SUM(CXC.Importe)                                  
        FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
         AND cxc.ContratoMov = c1.mov                                  
         AND Cxc.Empresa =c1.Empresa                                  
         And Cxc.Cliente = c1.Cliente                                  
         and Cxc.Mov = 'Reasignacion de Lote'                    
    and Cxc.Estatus<>'SINAFECTAR'                                  
    and Cxc.Estatus <> 'CANCELADO'),0)                       
        
      
      +ISNULL((SELECT SUM(CXC.Saldo)                                  
         
          from Cxc                                  
          where Cxc.ContratoID = c1.ID                                  
          AND cxc.ContratoMov = c1.mov                         
     And Cxc.Cliente = c1.Cliente                                  
          AND Cxc.Empresa =c1.Empresa                                  
          and Cxc.Mov IN ('Devolucion')                                  
          and Cxc.Estatus <> 'CANCELADO'),0)     
        
--Codigo Agregado14/12/2017    
 +ISNULL((SELECT SUM(CXC.importe)                                  
         
          from Cxc                                  
          where Cxc.ContratoID = c1.ID                                  
          AND cxc.ContratoMov = c1.mov            
     And Cxc.Cliente = c1.Cliente                                  
          AND Cxc.Empresa =c1.Empresa                                  
          and Cxc.Mov IN ('Devolucion Venta')                                  
          and Cxc.Estatus <> 'CANCELADO'),0)     
  --Linea agregada 17/08/2016    
      
  +ISNULL((SELECT SUM(CXC.Importe)                                  
         
          from Cxc                        
          where Cxc.ContratoID = c1.ID                        
          AND cxc.ContratoMov = c1.mov                        
          And Cxc.Cliente = c1.Cliente                                  
          AND Cxc.Empresa =c1.Empresa                                  
          and Cxc.Mov IN ('Devolucion Saldo')                                  
          and Cxc.Estatus <> 'CANCELADO'),0)      
--Linea agregada 17/08/2016     
    
+ISNULL((SELECT SUM(CXC.Importe)                                  
          from Cxc                                  
          where Cxc.ContratoID = c1.ID                                  
          AND cxc.ContratoMov = c1.mov                         
          And Cxc.Cliente = c1.Cliente                                  
          AND Cxc.Empresa =c1.Empresa                                  
          and Cxc.Mov IN ('Ajuste Saldo')                                  
          and Cxc.Estatus = 'Concluido'),0)        
                             
     
      
       -ISNULL((SELECT SUM(CXC.Saldo)                                  
        
          from Cxc                                  
          where Cxc.ContratoID = c1.ID                                  
          AND cxc.ContratoMov = c1.mov                              
          And Cxc.Cliente = c1.Cliente                                  
          AND Cxc.Empresa =c1.Empresa                                  
          and Cxc.Mov = 'Anticipo'                                  
          and Cxc.Estatus <> 'CANCELADO'),0)                 
        
       --Código agregado el 21/09/2015                              
      
       +ISNULL((SELECT SUM(CXC.Importe)                 
        from Cxc                                  
        where Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        And Cxc.Cliente = c1.Cliente               
     AND Cxc.Empresa =c1.Empresa            
        and Cxc.Mov IN ('Mensualidad Recl', 'Mensualidad Recl CFD')                                  
        and Cxc.Estatus <> 'CANCELADO'),0)     
    
--Linea Agregada 02/08/2018    
    
    -ISNULL((SELECT SUM(CXC.Importe)                 
        from Cxc                                  
        where Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        And Cxc.Cliente = c1.Cliente               
     AND Cxc.Empresa =c1.Empresa                
        and Cxc.Mov IN ('Neteo')                                  
        and Cxc.Estatus <> 'CANCELADO'),0)     
      
  --Codigo Agregado 27062017    
      
 /* - ISNULL((SELECT SUM(CxcAux.Abono)                               
           FROM Cxc, CxcAux                    
            WHERE Cxc.Mov in ('Recla Cobro', 'Recla Cobro CFD')                              
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica <> 'Pena Convencional'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)   */                            
        
       -ISNULL((SELECT SUM(CXC.Importe)                                 
          from Cxc                                 
          where Cxc.ContratoID = c1.ID                              
          AND cxc.ContratoMov = c1.mov                 
          And Cxc.Cliente = c1.Cliente                                 
          AND Cxc.Empresa =c1.Empresa                                 
          and Cxc.Mov IN ('Recla Cobro', 'Recla Cobro CFD')                                  
          and Cxc.Estatus <> 'CANCELADO'),0)                                        
        
       --Fin de código agregado        
        
        +ISNULL((SELECT SUM(Cxc.Saldo)                               
         FROM Cxc, CxcAux                               
         WHERE Cxc.Mov = 'Aplicacion'           
  AND Cxc.ID = CxcAux.ModuloID                               
         AND Aplica = 'Factura FZB'       
         AND Cxc.Estatus = 'CONCLUIDO'                               
         AND ContratoID =       (                                
         SELECT Contrato                                 
         FROM vic_Local                              
         WHERE Local = @Local)), 0)        
      
   --15/06/2016      
      
     +ISNULL((SELECT SUM(CxcAux.Abono)                
      
            FROM Cxc, CxcAux       
            WHERE Cxc.Mov = 'Aplicacion'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Factura FZB'       
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)      
    
    -- Linea Agregada 18/10/2016    
    
    
       
      
         /*-ISNULL((SELECT SUM(CxcAux.Cargo)    
                                        
             FROM Cxc, CxcAux                               
            WHERE Cxc.Mov = 'Devolucion'                               
            AND Cxc.ID = CxcAux.ModuloID                              
            AND Aplica =  'Anticipo'                              
            AND Cxc.Estatus = 'CONCLUIDO'      
            AND ContratoID =       (                     
           SELECT Contrato             
 FROM vic_Local                                 
      
       WHERE Local = @Local)),0)   */                       
        
       --Código agregado 24/09/2015                              
      
       -ISNULL((SELECT SUM(CXC.Importe)                                 
           from Cxc                                 
           where Cxc.ContratoID = c1.ID         
           AND cxc.ContratoMov = c1.mov                                 
           And Cxc.Cliente = c1.Cliente                                 
           AND Cxc.Empresa =c1.Empresa      
           and Cxc.Mov = 'Ajuste'                                 
            and Cxc.Estatus <> 'CANCELADO'),0)                             
         --Fin de código agregado     
   --Linea Agregada 06/02/2018    
   + ISNULL((SELECT SUM(CxcAux.Abono)        
           FROM Cxc, CxcAux                
            WHERE Cxc.Mov = 'Ajuste'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Cancelar Deuda'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)      
       
                               
        --Código agregado 26/11/2015                              
         + ISNULL((SELECT SUM(CxcAux.Abono)                               
            FROM Cxc, CxcAux                               
            WHERE Cxc.Mov = 'Aplicacion'                         
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Pena Convencional'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
            SELECT Contrato                           
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)                        
         --Fin de código agregado         
      
       --Código agregado el 27/11/2015 --Jonatan                       
        
       -ISNULL((SELECT SUM(Cxcaux.Abono)                        
        FROM Cxc, CxcAux                        
         WHERE Cxc.Mov = 'Cobro Inmobiliario'                        
         AND CxcAux.Aplica IN ('Cheque Devuelto')                        
         AND Cxc.Estatus = 'CONCLUIDO'                        
         AND Cxc.ID = CxcAux.ModuloID      
   and ISNULL(Cxc.Origen,'') = 'Cheque Devuelto'                       
         AND ContratoID =                        
          (                     
           SELECT Contrato                        
           FROM vic_Local                        
            WHERE Local = @Local )),0)     --bloqueo arreglado 30jul2019    
       
   --codigo agregado 29/08/2018       
   -ISNULL((SELECT SUM(Cxcaux.Abono)                        
        FROM Cxc, CxcAux                        
         WHERE Cxc.Mov = 'Cobro'                        
         AND CxcAux.Aplica IN ('Cheque Devuelto')                        
         AND Cxc.Estatus = 'CONCLUIDO'                        
         AND Cxc.ID = CxcAux.ModuloID      
   and ISNULL(Cxc.Origen,'') = 'Cheque Devuelto'                       
         AND ContratoID =                        
          (                        
           SELECT Contrato                        
           FROM vic_Local                        
            WHERE Local = @Local )),0)       
            --)                        
          --GROUP BY Cxc.ID                        
         -- HAVING SUM(Importe) % SUM(Abono) <> 0) , 0)                        
         --Fin de código agregado             
        --Código agregado el 30/11/2015 Documento              
+ ISNULL((SELECT SUM(cxc.Saldo)                              
           FROM Cxc, CxcAux aux1                        
           INNER JOIN CxcAux aux2 ON aux1.ModuloID = aux2.ModuloID                 
            WHERE Cxc.Mov = 'Aplicacion'      
            AND Cxc.ID = aux1.ModuloID                               
            AND aux1.Aplica = 'Pena Convencional'                       
            AND aux2.Aplica = 'Anticipo'       
            AND Cxc.Estatus = 'CONCLUIDO'               
            AND ContratoID =       (            
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local )),0)                        
        
       --Fin de código agregado                        
         --Código agregado el 03/12/2015                        
         /*+ISNULL((SELECT SUM(CxcD.Importe)                               
            FROM Cxc, CxcD                               
           WHERE Cxc.ID = CxcD.ID                                     
            AND Aplica = 'Redondeo'                          
            AND Cxc.Estatus = 'CONCLUIDO'                               
     AND ContratoID =(                                
SELECT Contrato                                 
              FROM vic_Local                                 
              WHERE Local = @Local)),0)*/                        
         --Fin de código agregado      
    
   --24/07/2017    
       
     + ISNULL((SELECT SUM(CxcAux.Abono)                               
            FROM Cxc, CxcAux                               
         WHERE Cxc.Mov = 'Aplicacion Dep Ident'      
            AND Cxc.ID = CxcAux.ModuloID               
    AND Aplica = 'Factura FZB'     
   --AND cxc.Referencia='PENA CONVENCIONAL'                              
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                          
             SELECT Contrato                                 
              FROM vic_Local                                 
              WHERE Local = @Local)), 0)                      
        
       + ISNULL((SELECT SUM(CxcAux.Abono)                               
           FROM Cxc, CxcAux                    
            WHERE Cxc.Mov = 'Cobro Inmobiliario'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Pena Convencional'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)      
         --Código agregado el 18/01/2016    
                       
       /* + ISNULL((SELECT SUM(CxcAux.Cargo)                               
            FROM Cxc, CxcAux                               
            WHERE Cxc.Mov = 'Devolucion FZB'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Nota Credito FZB'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
           AND ContratoID =       (                                
            SELECT Contrato                                 
              FROM vic_Local                                 
              WHERE Local = @Local)), 0) */                    
      
       --Fin de código agregado                        
         --Código agregado el 07/12/2015                        
         /*- ISNULL((SELECT SUM(Importe)                        
            FROM Cxc                        
            WHERE Mov = 'Neteo'                        
           AND Estatus = 'CONCLUIDO'                        
            AND ContratoID = (                        
            SELECT Contrato          
              FROM vic_Local           
 WHERE Local = @Local             
            )),0)*/                        
         --Fin de código agregado      
        
  FROM Contrato c1                            
  left outer Join Cxc ON Cxc.ContratoID = c1.ID                         
  AND Cxc.ContratoMov =c1.Mov                            
  left outer JOIN Cte c ON CXC.Cliente = c.Cliente                   
  left outer JOIN vic_Condicion vc ON c1.ID = vc.IDContrato                 
  left outer JOIN VIC_Contrato vic ON c1.ID = vic.ID                         
  and vic.Inmueble = ISNULL(@Inmueble, vic.Inmueble)                            
  WHERE vc.Local = @Local  AND c1.Estatus in ('VIGENTE','Concluido')  and vc.Estatus='Activa'                         
  END                    
       
--Turno especial 8                   
      
IF @Turno = 8                           
 BEGIN                        
 INSERT INTO #EkoEdoCta                            
  SELECT     distinct c1.cliente,                  
  Cargo = ISNULL((SELECT SUM(vc.importe)                                 
  FROM vic_Condicion vc                                 
   WHERE vc.IDContrato=c1.ID                                 
   AND vc.Estatus = 'ACTIVA'                                 
   AND vc.SerieFactura = 'Saldo'),0),                                 
   Abono = ISNULL((SELECT SUM(Cxc.Importe)                            
  FROM Cxc                       
  WHERE Cxc.ContratoID = c1.ID                                  
  AND cxc.ContratoMov = c1.mov                                  
  AND Cxc.Empresa =c1.Empresa                                  
  And Cxc.Cliente = c1.Cliente                                  
and Cxc.Mov = 'Documento SI'                         
  and Cxc.Estatus <> 'CANCELADO'),0)                               
        
   - ISNULL((SELECT SUM(CXC.Importe)                                  
         FROM Cxc                                  
         WHERE Cxc.ContratoID = c1.ID                    
         AND cxc.ContratoMov = c1.mov                                  
         And Cxc.Cliente = c1.Cliente                                  
         AND Cxc.Empresa =c1.Empresa                                  
         and Cxc.Mov = 'Cobro'                                  
         and ISNULL(Cxc.Origen,'') <> 'Cheque Devuelto'                                  
         and Cxc.Estatus <> 'CANCELADO'),0)                               
      
         -ISNULL((SELECT SUM(CXC.Importe)                                  
          FROM Cxc                                  
          WHERE Cxc.ContratoID = c1.ID                                  
          AND cxc.ContratoMov = c1.mov                                  
          And Cxc.Cliente = c1.Cliente                                  
         AND Cxc.Empresa =c1.Empresa                                  
and Cxc.Mov IN ( 'Cobro Inmobiliario')                                  
          and ISNULL(Cxc.Origen,'') <> 'Cheque Devuelto'                                  
         and Cxc.Estatus in('PENDIENTE','VIGENTE','CONCLUIDO')),0)                               
       
       -ISNULL((SELECT SUM(CXC.Importe)                                  
         FROM Cxc                                  
         WHERE Cxc.ContratoID = c1.ID                                  
         AND cxc.ContratoMov = c1.mov                                  
         AND Cxc.Empresa =c1.Empresa                                  
         And Cxc.Cliente = c1.Cliente                                  
         and Cxc.Mov = 'Aplicacion'                                  
         and Cxc.Estatus <> 'CANCELADO'),0)                               
        
       +ISNULL((SELECT SUM(CXC.Importe)                                  
        FROM Cxc                                  
        WHERE Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov                                  
        AND Cxc.Empresa =c1.Empresa                                  
        And Cxc.Cliente = c1.Cliente                  
        and Cxc.Mov = 'Reasignacion de Lotes D'                                
   and Cxc.Estatus <> 'CANCELADO'),0)                
      
         -ISNULL((SELECT SUM(CXC.Importe)                                  
          FROM Cxc                   
          WHERE Cxc.ContratoID = c1.ID                                  
          AND cxc.ContratoMov = c1.mov                             
 AND Cxc.Empresa =c1.Empresa                
          And Cxc.Cliente = c1.Cliente        
  and Cxc.Mov = 'Reasignacion de Lote'                    
          and Cxc.Estatus<>'SINAFECTAR'                                  
          and Cxc.Estatus <> 'CANCELADO'),0)                                
       
       +ISNULL((SELECT SUM(CXC.Importe)                                  
        from Cxc                                  
        where Cxc.ContratoID = c1.ID                                  
        AND cxc.ContratoMov = c1.mov          
        And Cxc.Cliente = c1.Cliente                                  
        AND Cxc.Empresa =c1.Empresa                                  
        and Cxc.Mov IN ('Devolucion')                                  
        and Cxc.Estatus <> 'CANCELADO'),0)             
      
       +ISNULL((SELECT SUM(CXC.Saldo)                                  
        from Cxc                                  
        where Cxc.ContratoID = c1.ID                                  
AND cxc.ContratoMov = c1.mov                                  
        And Cxc.Cliente = c1.Cliente                                  
        AND Cxc.Empresa =c1.Empresa                   
        and Cxc.Mov = 'Anticipo'                                  
        and Cxc.Estatus <> 'CANCELADO'),0)                         
 --Código agregado el 21/09/2015                     
         +ISNULL((SELECT SUM(CXC.Importe)           
          from Cxc              
          where Cxc.ContratoID = c1.ID       
          AND cxc.ContratoMov = c1.mov                                  
          And Cxc.Cliente = c1.Cliente                                  
          AND Cxc.Empresa =c1.Empresa                                  
          and Cxc.Mov IN ('Mensualidad Recl', 'Mensualidad Recl CFD')                                  
          and Cxc.Estatus <> 'Concluido'),0)                               
      
        -ISNULL((SELECT SUM(CXC.Importe)                                 
         from Cxc                                 
         where Cxc.ContratoID = c1.ID                                 
         AND cxc.ContratoMov = c1.mov                                 
         And Cxc.Cliente = c1.Cliente                                 
         AND Cxc.Empresa =c1.Empresa                                 
    and Cxc.Mov IN ('Recla Cobro', 'Recla Cobro CFD')                                  
  and Cxc.Estatus <> 'CANCELADO'),0)                                        
         --Fin de código agregado                         
        --Código agregado 24/09/2015                              
      
         -ISNULL((SELECT SUM(CXC.Importe)                                 
          from Cxc                                 
          where Cxc.ContratoID = c1.ID                                 
          AND cxc.ContratoMov = c1.mov                                 
          And Cxc.Cliente = c1.Cliente                                 
          AND Cxc.Empresa =c1.Empresa                                 
         and Cxc.Mov = 'Ajuste'                                 
         and Cxc.Estatus <> 'CANCELADO'),0)                              
         --Fin de código agregado                              
       
      +ISNULL((SELECT SUM(CxcAux.Cargo)                               
          FROM Cxc, CxcAux                               
          WHERE Cxc.Mov = 'Aplicacion'                               
          AND Cxc.ID = CxcAux.ModuloID         
          AND Aplica = 'Factura FZB'                               
          AND Cxc.Estatus = 'CONCLUIDO'                               
         AND ContratoID =       (                                
      SELECT Contrato                               
   FROM vic_Local                        
           WHERE Local = @Local)), 0)        
   --     -ISNULL((SELECT SUM(CxcAux.Abono)               
     --      FROM Cxc, CxcAux                            
      --      WHERE Cxc.Mov = 'Aplicacion'                               
      --      AND Cxc.ID = CxcAux.ModuloID                               
      --      AND Aplica <> 'Factura FZB'                      
    --      AND Cxc.Estatus = 'CONCLUIDO'                  
      --   AND ContratoID =  (                                
     --       SELECT Contrato                                 
   --       FROM vic_Local                                 
     --       WHERE Local = @Local)), 0)                     
        --Código agregado 26/11/2015                              
      
         + ISNULL((SELECT SUM(CxcAux.Abono)                               
            FROM Cxc, CxcAux                               
            WHERE Cxc.Mov = 'Aplicacion'                               
            AND Cxc.ID = CxcAux.ModuloID                        
            AND Aplica = 'Pena Convencional'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
           SELECT Contrato                                 
             FROM vic_Local                                 
              WHERE Local = @Local)), 0)                        
         --Fin de código agregado                        
         --Código agregado el 27/11/2015                        
       
       +ISNULL((SELECT SUM(Cxcaux.Abono)            
            FROM Cxc, CxcAux         
            WHERE Cxc.Mov = 'Cobro Inmobiliario'                       
       AND CxcAux.Aplica IN ('Cheque Devuelto')                  
            AND Cxc.Estatus = 'CONCLUIDO'        
AND Cxc.ID = CxcAux.ModuloID                        
            AND ContratoID =                        
            (                        
             SELECT Contrato                        
              FROM vic_Local                        
              WHERE Local = @Local                        
            )                        
            GROUP BY Cxc.ID                        
            HAVING SUM(Importe) % SUM(Abono) <> 0) , 0)                        
         --Fin de código agregado                        
         --Código agregado el 30/11/2015                        
        
       + ISNULL((SELECT SUM(aux1.Abono)                               
            FROM Cxc, CxcAux aux1                        
            INNER JOIN CxcAux aux2 ON aux1.ModuloID = aux2.ModuloID                              
      WHERE Cxc.Mov = 'Aplicacion'                                        
            AND Cxc.ID = aux1.ModuloID                               
            AND aux1.Aplica = 'Pena Convencional'                        
            AND aux2.Aplica = 'Anticipo'                               
            AND Cxc.Estatus = 'CONCLUIDO'                                   
            AND ContratoID =       (                                
              SELECT Contrato             
               FROM vic_Local                                 
               WHERE Local = @Local )),0)                        
         --Fin de código agregado                        
         --Código agregado el 03/12/2015                        
         /*+ISNULL((SELECT SUM(CxcD.Importe)                               
            FROM Cxc, CxcD                               
            WHERE Cxc.ID = CxcD.ID                                     
            AND Aplica = 'Redondeo'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =(                                
             SELECT Contrato            
              FROM vic_Local                    
              WHERE Local = @Local)),0)*/                        
         --Fin de código agregado           
      
         + ISNULL((SELECT SUM(CxcAux.Abono)           
            FROM Cxc, CxcAux                               
            WHERE Cxc.Mov = 'Cobro Inmobiliario'       
    AND Cxc.ID = CxcAux.ModuloID       
       AND Aplica = 'Pena Convencional'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
         AND ContratoID =       (                  
             SELECT Contrato                                 
              FROM vic_Local                       
             WHERE Local = @Local)), 0)                        
      --Código agregado el 18/01/2016      
        
       - ISNULL((SELECT SUM(CxcAux.Cargo)                               
            FROM Cxc, CxcAux                               
     WHERE Cxc.Mov = 'Devolucion'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Nota Credito FZB'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
          AND ContratoID =       (                                
             SELECT Contrato                                 
              FROM vic_Local                                 
              WHERE Local = @Local)), 0)                        
         --Fin de código agregado                        
        --Código agregado el 07/12/2015                        
         /*- ISNULL((SELECT SUM(Importe)                        
            FROM Cxc                        
            WHERE Mov = 'Neteo'                        
            AND Estatus = 'CONCLUIDO'                        
    AND ContratoID = (                        
SELECT Contrato                       
       FROM vic_Local                   
   WHERE Local = @Local                        
            )),0)*/            
         --Fin de código agregado                   
       
  FROM Contrato c1                         
    left outer Join Cxc ON Cxc.ContratoID = c1.ID                         
    AND Cxc.ContratoMov =c1.Mov                            
    left outer JOIN Cte c ON CXC.Cliente = c.Cliente                            
    left outer JOIN vic_Condicion vc ON c1.ID = vc.IDContrato                            
    left outer JOIN VIC_Contrato vic ON c1.ID = vic.ID                         
    and vic.Inmueble = ISNULL(@Inmueble, vic.Inmueble)                            
    WHERE vc.Local = @Local  AND c1.Estatus in ('Vigente','Concluido')           
   END                           
       
      
 IF @Turno = 4                           
      
  BEGIN                            
      
  SET @Valor=(SELECT ISNULL(SUM(CXCD.Importe),0)                    
       FROM Contrato c1                         
       JOIN  Cxc   ON Cxc.ContratoID = c1.ID                         
       AND Cxc.ContratoMov =c1.Mov                       
       JOIN  CxcD  ON Cxc.ID = CxcD.ID                         
       AND CxcD.Aplica = 'Redondeo'                             
       JOIN  Cte c ON CXC.Cliente = c.Cliente                             
       LEFT OUTER JOIN  vic_Condicion vc ON c1.ID = vc.IDContrato                             
       JOIN  VIC_Contrato vic ON c1.ID = vic.ID                         
       and vic.Inmueble = ISNULL(@Inmueble, vic.Inmueble)                             
        WHERE vc.Local = @Local                           
        AND c1.Estatus in ('Vigente','Concluido')                             
        AND Cxc.ContratoID = c1.ID                             
        AND cxc.ContratoMov = c1.mov                             
        And Cxc.Cliente = c1.Cliente                          
        AND Cxc.Empresa =c1.Empresa                             
        AND Cxc.Mov in('Cobro','Aplicacion')                             
        AND Cxc.Origen NOT IN ('Cheque Devuelto')              
        AND Cxc.Estatus <> 'CANCELADO')               
        
      
  INSERT INTO #EkoEdoCta                 
     SELECT                    
      DISTINCT c1.cliente,              
      Cargo = ISNULL((SELECT vc.importe                                 
           FROM vic_Condicion vc                                 
   WHERE vc.IDContrato=c1.ID                           
           AND vc.Estatus = 'ACTIVA'          
  AND vc.SerieFactura = 'Escrituracion'),0),      
      Abono =ISNULL((SELECT vc.importe                    
          FROM vic_Condicion vc                                
          WHERE vc.IDContrato=c1.ID                                
          AND vc.Estatus = 'ACTIVA'                                
         AND vc.SerieFactura = 'Saldo'),0)                       
       -ISNULL((SELECT SUM(Cxc.Importe)                             
   FROM Cxc               
        WHERE Cxc.ContratoID = c1.ID                                 
        AND cxc.ContratoMov = c1.mov                                 
AND Cxc.Empresa =c1.Empresa                                 
        And Cxc.Cliente = c1.Cliente                                 
        and Cxc.Mov = 'Documento SI'                         
        and Cxc.Estatus <> 'CANCELADO'),0)                        
       - CASE WHEN @Valor < 0                         
        THEN @Valor                         
         ELSE 0                         
         END                              
        
      + ISNULL((SELECT SUM(CXC.Importe)                                 
           FROM Cxc                                 
           WHERE Cxc.ContratoID = c1.ID                                 
           AND cxc.ContratoMov = c1.mov                    
           And Cxc.Cliente = c1.Cliente                                 
           AND Cxc.Empresa =c1.Empresa                             
           and Cxc.Mov in('Cobro')                 
           and Cxc.Origen NOT IN ('Cheque Devuelto')                               
           and Cxc.Estatus <> 'CANCELADO'),0)         
      
     + ISNULL((SELECT SUM(CXC.Importe)       
           FROM Cxc                                 
     WHERE Cxc.ContratoID = c1.ID                                 
           AND cxc.ContratoMov = c1.mov                         
           AND Cxc.Empresa =c1.Empresa                                 
           And Cxc.Cliente = c1.Cliente                                 
           and Cxc.Mov = 'Aplicacion'                                 
           and Cxc.Estatus <> 'CANCELADO'),0)                              
        
      -ISNULL((SELECT SUM(CXC.Importe)                                 
         from Cxc                                 
           where Cxc.ContratoID = c1.ID                                 
           AND cxc.ContratoMov = c1.mov             
           And Cxc.Cliente = c1.Cliente                                 
          AND Cxc.Empresa =c1.Empresa                                 
           and Cxc.Mov = 'Devolucion'                                 
           and Cxc.Estatus <> 'CANCELADO'),0)                            
     FROM Contrato c1                            
     JOIN  Cxc ON Cxc.ContratoID = c1.ID                         
     AND Cxc.ContratoMov =c1.Mov                            
     JOIN  Cte c ON CXC.Cliente = c.Cliente                            
     left outer JOIN  vic_Condicion vc ON c1.ID = vc.IDContrato                            
     JOIN  VIC_Contrato vic ON c1.ID = vic.ID                         
     and vic.Inmueble = @Inmueble                            
     WHERE vc.Local = @Local                           
     AND c1.Estatus in ('Vigente','Concluido')         
   END                        
   --Calculo de saldo vencido                           
      
  IF @Turno = 5                           
       
  BEGIN                         
    INSERT INTO #EkoEdoCta                           
    SELECT     DISTINCT c1.cliente,                            
    Cargo = ISNULL( (SELECT SUM(Saldo)      
           FROM Cxc                                 
          WHERE CAST(Vencimiento AS DATE) < CAST(GETDATE() AS DATE)       
          AND Estatus = 'PENDIENTE'                         
      AND Mov NOT IN ('Pena Convencional','Anticipo','Endoso a Favor','Factura FZB')                        
          AND OrigenTipo in ('CXC','VTAS')         
          AND ContratoID =          (          
          SELECT Contrato            
          FROM vic_Local                  
          WHERE Local = @Local)),0)                              
        --Código agregado el 12/10/2015      
      
      
  /*-ISNULL((SELECT SUM(CXC.Importe)                                  
         
          from Cxc                                  
          where Cxc.ContratoID = c1.ID                                  
          AND cxc.ContratoMov = c1.mov            
     And Cxc.Cliente = c1.Cliente                                  
          AND Cxc.Empresa =c1.Empresa            
          and Cxc.Mov IN ('Devolucion Venta')      
          and Cxc.Estatus <> 'CANCELADO'),0) */    
    
    
    
       --Codigo Agregado 13/02/2017    
    
   -ISNULL((SELECT SUM(CXC.Saldo)                                  
        
          from Cxc                                  
          where Cxc.ContratoID = c1.ID                  
          AND cxc.ContratoMov = c1.mov            
          And Cxc.Cliente = c1.Cliente                                  
   AND Cxc.Empresa =c1.Empresa                                  
          and Cxc.Mov = 'Factura Valor Total'                                  
          and Cxc.Estatus <> 'CANCELADO'),0)        
    ---                               
       +ISNULL( (SELECT SUM(Saldo)                                
           FROM Cxc                                 
          WHERE CAST(Vencimiento AS DATE) <= CAST(GETDATE() AS DATE)                                
          AND Estatus = 'PENDIENTE'                                
   AND Mov  IN ('Mensualidad Recl','Mensualidad Recl CFD')                                
           AND ContratoID =          (                                  
           SELECT Contrato                                   
              FROM vic_Local                                   
               WHERE Local = @Local)),0),                         
     --Fin de código agregado                            
       Abono = 0               
    FROM Contrato c1                       
    left outer Join Cxc                      
     ON Cxc.ContratoID = c1.ID          
     AND Cxc.ContratoMov =c1.Mov                            
     left outer JOIN Cte c ON CXC.Cliente = c.Cliente                            
     left outer JOIN vic_Condicion vc ON c1.ID = vc.IDContrato       
     left outer JOIN VIC_Contrato vic ON c1.ID = vic.ID        
     and vic.Inmueble = ISNULL(@Inmueble, vic.Inmueble)                            
      WHERE vc.Local = @Local  AND c1.Estatus in ('Vigente','Concluido')                         
    END                              
   --Calculo de pena vencida                              
   IF @Turno = 6                           
   BEGIN                           
     INSERT INTO #EkoEdoCta                            
     SELECT     distinct  c1.Cliente,                            
        Cargo = ISNULL( (SELECT SUM(cxc.Saldo)                                
            FROM Cxc                                 
              WHERE-- CAST(Vencimiento AS DATE) <= CAST(GETDATE() AS DATE)                                
              Estatus = 'PENDIENTE'                                
             AND Mov IN ('Pena Convencional')                                
             AND ContratoID =  c1.id),0),                            
        Abono = 0                            
     FROM Contrato c1                            
     left outer Join Cxc ON Cxc.ContratoID = c1.ID                         
           AND Cxc.ContratoMov =c1.Mov                            
     left outer JOIN Cte c ON CXC.Cliente = c.Cliente                            
       left outer JOIN vic_Condicion vc ON c1.ID = vc.IDContrato                            
    left outer JOIN VIC_Contrato vic ON c1.ID = vic.ID                         
   and vic.Inmueble = ISNULL(@Inmueble, vic.Inmueble)                            
      WHERE vc.Local = @Local  AND c1.Estatus in ('Vigente','Concluido')                           
     END                           
   --Calculo de pena pagada                            
   IF @Turno = 7               
   BEGIN                         
    INSERT INTO #EkoEdoCta                            
    SELECT     DISTINCT c1.cliente,                            
Cargo = ISNULL( (SELECT SUM(Importe)                  
            FROM Cxc                       
            WHERE Estatus = 'CONCLUIDO'                            
            AND Mov IN ('Pena Conv. Cobrada')      
            AND ContratoID =          (                                  
   SELECT Contrato                  
              FROM vic_Local                                   
   WHERE Local = @Local)),0)     
      
       + ISNULL((SELECT SUM(CxcAux.Abono)      
           FROM Cxc, CxcAux                            
         WHERE Cxc.Mov = 'Aplicacion'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Pena Convencional'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (             
             SELECT Contrato                                 
              FROM vic_Local                                 
    WHERE Local = @Local)), 0)           
              --Codigo Agregado 13/04/2016--          
    
     
        
       + ISNULL((SELECT SUM(CxcAux.Abono)                               
            FROM Cxc, CxcAux                               
            WHERE Cxc.Mov = 'Aplicacion'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Factura FZB'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                          
             SELECT Contrato                                 
              FROM vic_Local               
              WHERE Local = @Local)), 0)      
    
 --codigo agregado 12/072017    
    
    + ISNULL((SELECT SUM(CxcAux.Abono)          
            FROM Cxc, CxcAux                
            WHERE Cxc.Mov = 'Aplicacion Dep Ident'                               
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Factura FZB'     
   --AND cxc.Referencia='PENA CONVENCIONAL'             
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                          
             SELECT Contrato                                 
              FROM vic_Local                                 
              WHERE Local = @Local)), 0)    
         
     --Codigo Agregado 19/11/2016    
  + ISNULL( (SELECT SUM(Importe)                                
            FROM Cxc                                 
            WHERE Estatus = 'CONCLUIDO'                            
            AND Mov IN ('Pena Conv. Cob. Recl')                                
            AND ContratoID =          (                                  
             SELECT Contrato                                   
             FROM vic_Local                                   
             WHERE Local = @Local)),0)       
--Linea Agregada2706/207    
    
+ ISNULL((SELECT SUM(CxcAux.Abono)                               
           FROM Cxc, CxcAux                    
            WHERE Cxc.Mov in ('Recla Cobro', 'Recla Cobro CFD')                              
            AND Cxc.ID = CxcAux.ModuloID                               
            AND Aplica = 'Pena Convencional'                               
            AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =       (                                
            SELECT Contrato                                 
            FROM vic_Local                                 
            WHERE Local = @Local)), 0)                                 
      
       + ISNULL((SELECT SUM(CxcAux.Abono)                               
            FROM Cxc, CxcAux                               
            WHERE Cxc.Mov = 'Cobro Inmobiliario'                               
            AND Cxc.ID = CxcAux.ModuloID                        
 AND Aplica = 'Pena Convencional'        
       AND Cxc.Estatus = 'CONCLUIDO'                               
            AND ContratoID =    (                                
             SELECT Contrato                    
              FROM vic_Local                                 
              WHERE Local = @Local)), 0)            
       --Linea Agregada 01/04/2016     
         
      
                
      
       -ISNULL((SELECT SUM(CxcAux.Cargo)                               
          FROM Cxc, CxcAux                               
          WHERE Cxc.Mov = 'Devolucion FZB'                    
          AND Cxc.ID = CxcAux.ModuloID       
          AND Aplica = 'Nota Credito FZB'                               
          AND Cxc.Estatus = 'CONCLUIDO'                               
          AND ContratoID =       (                                
           SELECT Contrato                                 
            FROM vic_Local                   
            WHERE Local = @Local)), 0)      
    
   --LINEA AGREGADA 28/09/2018    
    -ISNULL((SELECT SUM(CxcAux.Cargo)                               
          FROM Cxc, CxcAux                               
          WHERE Cxc.Mov = 'Devolucion Pena'                               
          AND Cxc.ID = CxcAux.ModuloID                         
          AND Aplica = 'Nota Credito PDI'                               
          AND Cxc.Estatus = 'CONCLUIDO'                               
          AND ContratoID =       (                                
           SELECT Contrato                                 
            FROM vic_Local                     
            WHERE Local = @Local)), 0)     
    
   --Linea Agregada 28/11/2017    
    
     -ISNULL((SELECT SUM(CxcAux.Cargo)                               
          FROM Cxc, CxcAux           
          WHERE Cxc.Mov = 'Devolucion FZB'                               
          AND Cxc.ID = CxcAux.ModuloID                               
          AND Aplica = 'Nota Credito FZR'                               
          AND Cxc.Estatus = 'CONCLUIDO'                               
          AND ContratoID =       (                                
           SELECT Contrato                                 
            FROM vic_Local                     
            WHERE Local = @Local)), 0)    
       
   --Linea Agregada 31/08/2017    
       
    -ISNULL((SELECT SUM(CxcAux.Cargo)      
          FROM Cxc, CxcAux                               
          WHERE Cxc.Mov = 'Devolucion FZB'                 
          AND Cxc.ID = CxcAux.ModuloID      
          AND Aplica = 'Nota Credito FZN'                               
       AND Cxc.Estatus = 'CONCLUIDO'         
          AND ContratoID =       (                                
     SELECT Contrato                                 
            FROM vic_Local                  
            WHERE Local = @Local)), 0)                 
      
       -ISNULL((SELECT SUM(CxcAux.Cargo)                           
          FROM Cxc, CxcAux                               
          WHERE Cxc.Mov = 'Devolucion'                               
          AND Cxc.ID = CxcAux.ModuloID                  
          AND Aplica = 'Nota Credito FZB'                               
          AND Cxc.Estatus = 'CONCLUIDO'                               
     AND ContratoID =       (                             
           SELECT Contrato              
            FROM vic_Local                     
            WHERE Local = @Local)), 0),                             
        Abono = 0                           
    FROM Contrato c1                            
    left outer Join Cxc ON Cxc.ContratoID = c1.ID                         
    AND Cxc.ContratoMov =c1.Mov              
    left outer JOIN Cte c ON CXC.Cliente = c.Cliente                            
    left outer JOIN vic_Condicion vc ON c1.ID = vc.IDContrato                            
    left outer JOIN VIC_Contrato vic ON c1.ID = vic.ID                         
    and vic.Inmueble = ISNULL(@Inmueble, vic.Inmueble)       
    WHERE vc.Local = @Local and vc.Estatus='Activa'                         
    AND c1.Estatus in ('Vigente','Concluido')                           
    END                              
        
 SELECT  @Cargos = SUM(Cargo), @Abonos = SUM(Abono)                         
   FROM #EkoEdoCta                         
   SELECT  @Saldo = ISNULL(@Cargos,0) - ISNULL(@Abonos,0)                           
     
  --  SELECT  @Saldo     
    
  --GMO REGRESAR     
  IF @Turno IN (7)    
  BEGIN     
     SELECT @Saldo=ROUND(ABS(@Saldo-Importe),2)                         
       FROM Cxc                             
      WHERE Cxc.Mov = 'Nota Credito FZB'                              
        AND Cxc.Estatus IN ('PENDIENTE','CONCLUIDO')      
  AND Cxc.ID= 311952     
  AND ContratoID =       (                           
           SELECT Contrato              
            FROM vic_Local                     
            WHERE Local = @Local)      
      
  END                             
     
    
       SELECT  @Saldo     
      
                            
 RETURN                          
       
END 