import 'package:flutter/material.dart';

// ignore: must_be_immutable
class PaymentTable extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final List<String>? columns;
  final List<String>? columnLabels; // Nuevo parámetro
  final List<String>? confirmationColumns;
  final bool selectable;
  final List<Map<String, dynamic>>? billingOptions;
  final bool showDropdown;
  final Function(List<Map<String, dynamic>>, double)? onSelectionChanged;
  final Future<void> Function(dynamic, double, String?)? onConfirm;
  final Function(List<Map<String, dynamic>>, double)? onConfirmSelection;
  Set<int> selectedRowIndices = {};
  Map<int, TextEditingController> totalControllers = {};
  String? selectedBillingOption;
  double? firstElementOriginalTotal;

  PaymentTable({
    required this.data,
    this.columns,
    this.columnLabels, // Agregamos el parámetro al constructor
    this.confirmationColumns,
    required this.selectable,
    this.billingOptions,
    this.showDropdown = false,
    this.onSelectionChanged,
    this.onConfirm,
    this.onConfirmSelection,
  }) {
    // Validación de longitud igual entre columns y columnLabels
    assert(
        columns == null ||
            columnLabels == null ||
            columns!.length == columnLabels!.length,
        'columns y columnLabels deben tener la misma cantidad de elementos');
  }

  @override
  _PaymentTableState createState() => _PaymentTableState();
}

class _PaymentTableState extends State<PaymentTable> {
  Set<int> selectedRowIndices = {};
  Map<int, TextEditingController> totalControllers = {};
  String? selectedBillingOption;
  List<int> selectionOrder = [];

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < widget.data.length; i++) {
      totalControllers[i] = TextEditingController(
          text: widget.data[i]['Total']?.toString() ?? '');
    }

    // Si es seleccionable y tiene columna Total, seleccionar primer elemento
    if (widget.selectable && widget.data.first.containsKey('Total')) {
      selectedRowIndices.add(0);
      widget.firstElementOriginalTotal =
          double.tryParse(widget.data[0]['Total'].toString());
    }
  }

  void onRowSelect(int index, bool? selected) {
    if (widget.selectable) {
      // Si no tiene columna Total, comportamiento normal
      if (!widget.data.first.containsKey('Total')) {
        _handleNormalSelection(index, selected);
        return;
      }

      // Validar selección de elementos subsiguientes
      if (index > 0) {
        // Verificar si el primer elemento está seleccionado
        if (!selectedRowIndices.contains(0)) {
          _showFirstElementRequiredAlert();
          return;
        }

        double? currentTotal = double.tryParse(totalControllers[0]!.text);
        if (currentTotal != widget.firstElementOriginalTotal) {
          _showTotalValidationAlert();
          return;
        }
      }

      _handleNormalSelection(index, selected);
    } else {
      showConfirmationDialog(widget.data[index]);
    }
  }

  void _handleNormalSelection(int index, bool? selected) {
    // Si intenta deseleccionar
    if (selected == false) {
      // Si es el primer elemento (índice 0), verificar que no haya otros elementos seleccionados
      if (index == 0) {
        if (selectedRowIndices.length > 1) {
          _showFirstElementRequiredAlert();
          return;
        }
      }
      // Si no es el primer elemento, verificar que sea el último seleccionado
      else if (selectionOrder.isNotEmpty && selectionOrder.last != index) {
        _showDeselectionOrderAlert();
        return;
      }
    }

    setState(() {
      if (selected == true) {
        selectedRowIndices.add(index);
        selectionOrder.add(index);
      } else {
        selectedRowIndices.remove(index);
        selectionOrder.remove(index);
      }
    });

    if (widget.onSelectionChanged != null) {
      List<Map<String, dynamic>> selectedData = getSelectedData();
      double totalSum = calculateTotalSum(selectedData);
      widget.onSelectionChanged!(selectedData, totalSum);
    }
  }

  void _showTotalValidationAlert() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Validación de Pago'),
        content: Text(
            'Debe igualar el total del primer elemento antes de continuar.'),
        actions: [
          TextButton(
            child: Text('Entendido'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showFirstElementRequiredAlert() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Validación de Selección'),
        content: Text('Debe deseleccionar primero los elementos posteriores.'),
        actions: [
          TextButton(
            child: Text('Entendido'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showDeselectionOrderAlert() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Orden de Deselección'),
        content:
            Text('Solo puede deseleccionar el último elemento seleccionado.'),
        actions: [
          TextButton(
            child: Text('Entendido'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> getSelectedData() {
    return selectedRowIndices
        .map((index) => Map<String, dynamic>.from(widget.data[index]))
        .toList();
  }

  double calculateTotalSum(dynamic data) {
    if (data is List<Map<String, dynamic>>) {
      return data.fold(0.0, (sum, item) {
        return sum + (double.tryParse(item['Total']?.toString() ?? '0') ?? 0.0);
      });
    } else if (data is Map<String, dynamic>) {
      return double.tryParse(data['Total']?.toString() ?? '0') ?? 0.0;
    }
    return 0.0;
  }

  void showConfirmationDialog(dynamic dataToConfirm) {
    double totalSum = calculateTotalSum(dataToConfirm);
    List columnKeys = widget.confirmationColumns ??
        widget.columns ??
        (dataToConfirm is Map ? dataToConfirm.keys.toList() : []);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            bool isDropdownValid = !widget.showDropdown ||
                !widget.billingOptions!.isNotEmpty ||
                selectedBillingOption != null;

            return AlertDialog(
              title: Text('Confirmación de selección'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.showDropdown &&
                        widget.billingOptions != null &&
                        widget.billingOptions!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Facturar a nombre de:'),
                            DropdownButton<String>(
                              isExpanded: true,
                              value: selectedBillingOption,
                              hint: Text('Seleccione una opción'),
                              items: widget.billingOptions!.map((option) {
                                return DropdownMenuItem<String>(
                                  value: option['value'] as String,
                                  child: Text(option['label'] as String),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                setState(() {
                                  selectedBillingOption = newValue;
                                });
                              },
                            ),
                            if (!isDropdownValid)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  'Debe seleccionar una opción de facturación',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    if (dataToConfirm is List<Map<String, dynamic>>)
                      for (var data in dataToConfirm)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ...columnKeys.map((key) {
                              return Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 4.0),
                                child: Text(
                                    '$key: ${data[key]?.toString() ?? ''}'),
                              );
                            }),
                            Divider(),
                          ],
                        )
                    else if (dataToConfirm is Map<String, dynamic>)
                      ...columnKeys.map((key) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4.0),
                          child: Text(
                              '$key: ${dataToConfirm[key]?.toString() ?? ''}'),
                        );
                      }),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: widget.selectable == true
                          ? Text('Suma Total: $totalSum',
                              style: TextStyle(fontWeight: FontWeight.bold))
                          : Container(),
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: Text('Cancelar'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: Text('Aceptar'),
                  onPressed: !isDropdownValid
                      ? null // Deshabilita el botón si no es válido
                      : () async {
                          Navigator.of(context).pop();
                          if (widget.onConfirm != null) {
                            await widget.onConfirm!(
                                dataToConfirm, totalSum, selectedBillingOption);
                          }
                        },
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty ||
        // ignore: unnecessary_type_check
        widget.data.any((element) => element is! Map<String, dynamic>)) {
      return Center(child: Text('La data proporcionada no es válida.'));
    }

    List<String> columnKeys = widget.columns ?? widget.data.first.keys.toList();
    List<String> displayLabels = widget.columnLabels ?? columnKeys;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.selectable)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: () {
                List<Map<String, dynamic>> selectedData = getSelectedData();
                double totalSum = calculateTotalSum(selectedData);
                if (widget.onConfirmSelection != null) {
                  widget.onConfirmSelection!(selectedData, totalSum);
                }
                showConfirmationDialog(selectedData);
              },
              child: Text('Confirmar Selección'),
            ),
          ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            showCheckboxColumn: widget.selectable,
            columns: [
              for (var i = 0; i < columnKeys.length; i++)
                DataColumn(label: Text(displayLabels[i])),
            ],
            rows: [
              for (int index = 0; index < widget.data.length; index++)
                DataRow(
                  selected: selectedRowIndices.contains(index),
                  onSelectChanged: (selected) => onRowSelect(index, selected),
                  cells: [
                    ...columnKeys.map(
                      (column) => DataCell(
                        column == 'Total' && widget.selectable
                            ? TextField(
                                controller: totalControllers[index],
                                enabled: !selectedRowIndices.contains(index),
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                  decimal: true,
                                  signed: false,
                                ),
                                decoration: const InputDecoration(
                                  contentPadding:
                                      EdgeInsets.symmetric(horizontal: 8),
                                ),
                                onTapOutside: (_) =>
                                    FocusScope.of(context).unfocus(),
                                onChanged: (value) {
                                  widget.data[index]['Total'] =
                                      double.tryParse(value) ??
                                          widget.data[index]['Total'];
                                  if (widget.onSelectionChanged != null) {
                                    List<Map<String, dynamic>> selectedData =
                                        getSelectedData();
                                    double totalSum =
                                        calculateTotalSum(selectedData);
                                    widget.onSelectionChanged!(
                                        selectedData, totalSum);
                                  }
                                },
                              )
                            : Text(
                                widget.data[index][column]?.toString() ?? ''),
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    totalControllers.values.forEach((controller) => controller.dispose());
    super.dispose();
  }
}
