import 'package:easy_pdf_viewer/easy_pdf_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:no_context_navigation/no_context_navigation.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAAvisos.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DACambiarContrasena.dart';
import 'package:pagosmovil/src/app_modules/pagos_movil/app_DAContacto.dart';
import 'package:pagosmovil/src/controllers/aviso_privacidad_controller.dart';
import 'package:pagosmovil/src/controllers/perfil_controller.dart';

import '/src/DAPackagesRef.dart';
import '/src/widgets/app_version.dart';
export '/src/home_page.dart';
export '/src/app_config/app_routes.dart';
import '/src/app_modules/pagos_movil/app_DAPagos.dart';
import 'api_request.dart';

DASessionProvider prov =
    DASessionProvider(); // Share session across other pages

class AppConfig {
  static final AppConfig _instance = AppConfig._internal();
  AppConfig._internal();

  static DAAppConfigModel get options {
    return DAAppConfigModel(
      licence: 'APP_CobrosTDC',
      appName: 'Pagos Móvil',
      homeLayout: '/PagosPage', // Don't forget to configure homeLayoutArgs
      idUsuarioTipo: 3,
      primaryColor: Color(0xFF78af22),
      accentColor: Color(0xFFcdce00),
      logo: Image.asset('assets/app/app-icon.png'),
      loginLogo: Padding(
        padding: const EdgeInsets.only(left: 224.0),
        child: Image.asset('assets/app/login.png'),
      ),
      logoAvatar: Image.asset('assets/app/app-icon.png'),
      // minLogo: SvgPicture.asset('assets/icon/box.svg'),
      // appBarLogo: SvgPicture.asset('assets/icon/box.svg', height: 20.0),
      fontName: 'BalooTamma',
      isOffline: false,
    );
  }

  static Object homeLayoutArgs(BuildContext context) {
    return AppDAPagos.index(context);
  }

  factory AppConfig() {
    return _instance;
  }

  static String get licence => options.licence;

  static String get appName => options.appName;

  static int get idUsuarioTipo => options.idUsuarioTipo;

  static String get homeLayout => options.homeLayout;

  static Widget get logo {
    return SizedBox(
      child: options.logo,
      height: 150.0,
      width: 200.0,
    );
  }

  static Widget get loginLogo {
    return SizedBox(
      child: options.loginLogo,
      height: 150.0,
      width: 200.0,
    );
  }

  static Widget? get logoAvatar => options.logoAvatar;

  static Widget get minLogo {
    return SizedBox(
      child: options.minLogo,
      height: 25.0,
      width: 100.0,
    );
  }

  static bool get isOffline => options.isOffline;

  static Widget appBarLogo() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.0),
      child: options.appBarLogo,
    );
  }

  static ThemeData get theme {
    return ThemeData(
      appBarTheme: AppBarTheme(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      fontFamily: options.fontName,
      primaryColor: options.primaryColor,
      colorScheme: ColorScheme.light(
        primary: options.primaryColor,
        secondary: options.accentColor ?? options.primaryColor,
      ),
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: options.primaryColor,
        selectionColor: options.primaryColor,
        selectionHandleColor: options.primaryColor,
      ),
    );
  }

  static SystemUiOverlayStyle get configBrightness {
    return SystemUiOverlayStyle.dark.copyWith(
      statusBarColor: Color.fromRGBO(0, 0, 0, 0.25),
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.grey[900],
      systemNavigationBarIconBrightness: Brightness.light,
    );
  }

  static List<Widget> appMenu(BuildContext context) {
    try {
      List<Widget> _navBarOpt = [];
      DatosPerfil _perfil = DatosPerfil();

      /* _navBarOpt.add(DAInput(
        refID: 'clave',
        controller: TextEditingController(text: _perfil.clave),
        label: 'Cliente',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'nombre',
        controller: TextEditingController(text: _perfil.nombre),
        label: 'Nombre',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      )); */

      _navBarOpt.add(DAInput(
        refID: 'rfc',
        controller: TextEditingController(text: _perfil.rfc),
        label: 'RFC',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'curp',
        controller: TextEditingController(text: _perfil.curp),
        label: 'CURP',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'direccionCompleta',
        controller: TextEditingController(text: _perfil.direccionCompleta),
        label: 'Dirección',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'delegacion',
        controller: TextEditingController(text: _perfil.delegacion),
        label: 'Delegación',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'codigoPostal',
        controller: TextEditingController(text: _perfil.codigoPostal),
        label: 'Código Postal',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'colonia',
        controller: TextEditingController(text: _perfil.colonia),
        label: 'Colonia',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'pais',
        controller: TextEditingController(text: _perfil.pais),
        label: 'Pais',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'estado',
        controller: TextEditingController(text: _perfil.estado),
        label: 'Estado',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'telefono',
        controller: TextEditingController(text: _perfil.telefono),
        label: 'Telefono',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'email',
        controller: TextEditingController(text: _perfil.email),
        label: 'Correo Electrónico',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'regimenFiscal',
        controller: TextEditingController(text: _perfil.regimenFiscal),
        label: 'Regimen Fiscal',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(DAInput(
        refID: 'usoCFDI',
        controller: TextEditingController(text: _perfil.usoCFDI),
        label: 'Uso CFDI',
        isRequired: false,
        disabled: true,
        tipo: DAInputType.string,
      ));

      _navBarOpt.add(ListTile(
        title: Text('Términos y Condiciones'),
        leading: Container(
          padding: EdgeInsets.only(right: 5),
          child: Icon(Icons.description, color: Colors.black),
        ),
        onTap: () async {
          PDFDocument doc =
              await PDFDocument.fromAsset('assets/app/PoliticaCobro.pdf');
          navService.pushNamed('/PagosPage',
              args: AppDAAvisos.terminosCondiciones(context, doc));
        },
      ));

      if (!DatosAvisoPrivacidad.noDocumentSelected()) {
        _navBarOpt.add(ListTile(
          title: Text('Aviso de Privacidad'),
          leading: Container(
            padding: EdgeInsets.only(right: 5),
            child: Icon(Icons.privacy_tip, color: Colors.black),
          ),
          onTap: () async {
            DatosAvisoPrivacidad _datosAvisoPrivacidad = DatosAvisoPrivacidad();
            PDFDocument doc = await _datosAvisoPrivacidad
                .getDocument(DatosAvisoPrivacidad().documentSelected);
            navService.pushNamed('/PagosPage',
                args: AppDAAvisos.dinamico(context, doc));
          },
        ));
      }

      _navBarOpt.add(ListTile(
        title: Text('Inicio'),
        leading: Container(
          padding: EdgeInsets.only(right: 5),
          child: Icon(Icons.home_outlined, color: Colors.black),
        ),
        onTap: () async {
          _perfil.setChangingPassword(false);
          navService.pushNamedAndRemoveUntil('home');
        },
      ));

      if (!_perfil.isChangingPassword) {
        _navBarOpt.add(ListTile(
          title: Text('Cambiar Contraseña'),
          leading: Container(
            padding: EdgeInsets.only(right: 5),
            child: Icon(Icons.password, color: Colors.black),
          ),
          onTap: () async {
            _perfil.setChangingPassword(true);
            navService
                .pushNamed('/WidgetsForm',
                    args: AppDACambiarContrasena.index(context))
                .then((value) {
              _perfil.setChangingPassword(false);
            });
          },
        ));
      }

      _navBarOpt.add(ListTile(
        title: Text('Contacto'),
        leading: Container(
          padding: EdgeInsets.only(right: 5),
          child: Icon(Icons.donut_small, color: Colors.black),
        ),
        onTap: () async {
          navService.pushNamed('/WidgetsForm',
              args: AppDAContacto.index(context));
        },
      ));

      _navBarOpt.add(ListTile(
        title: Text('Cerrar Sesión'),
        leading: Container(
          padding: EdgeInsets.only(right: 5),
          child: Icon(Icons.logout, color: Colors.black),
        ),
        onTap: () async {
          _perfil.setChangingPassword(false);
          await logOut();
        },
      ));

      _navBarOpt.add(VersionDisplay());

      return _navBarOpt;
    } catch (e) {
      DAToast(context, e.toString());
      return [];
    }
  }

  static Future<void> logOut() async {
    try {
      // Desactivar token FCM
      DARequestModel _req = await ApiRequest.desactivarToken;
      await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

      // Continuar con el proceso normal de logout
      final httpProv = HttpProvider();
      bool resetEstacion = await httpProv.logOut();
      if (resetEstacion) {
        prov.resetEstacion();
      }
      prov.reset();
      navService.pushNamedAndRemoveUntil('login');
    } catch (e) {
      print('Error durante el logout: $e');
      // Asegurar que el usuario sea desconectado incluso si falla la desactivación del token
      prov.reset();
      navService.pushNamedAndRemoveUntil('login');
    }
  }
}
