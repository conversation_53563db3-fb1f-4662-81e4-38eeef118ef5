class DatosPerfil {
  static final DatosPerfil _instance = DatosPerfil._internal();

  DatosPerfil._internal();

  factory DatosPerfil() {
    return _instance;
  }

  String? clave;
  String? nombre;
  String? rfc;
  String? curp;
  String? direccionCompleta;
  String? delegacion;
  String? codigoPostal;
  String? colonia;
  String? pais;
  String? estado;
  String? telefono;
  String? email;
  String? regimenFiscal;
  String? usoCFDI;
  bool isChangingPassword = false;

  void setChangingPassword(bool _isChangingPassword) {
    isChangingPassword = _isChangingPassword;
  }

  void updateProfile({
    String? clave,
    String? nombre,
    String? rfc,
    String? curp,
    String? direccionCompleta,
    String? delegacion,
    String? codigoPostal,
    String? colonia,
    String? pais,
    String? estado,
    String? telefono,
    String? email,
    String? regimenFiscal,
    String? usoCFDI,
  }) {
    this.clave = clave;
    this.nombre = nombre;
    this.rfc = rfc;
    this.curp = curp;
    this.direccionCompleta = direccionCompleta;
    this.delegacion = delegacion;
    this.codigoPostal = codigoPostal;
    this.colonia = colonia;
    this.pais = pais;
    this.estado = estado;
    this.telefono = telefono;
    this.email = email;
    this.regimenFiscal = regimenFiscal;
    this.usoCFDI = usoCFDI;
  }

  Map<String, dynamic> toJson() {
    return {
      'Clave': clave,
      'Nombre': nombre,
      'RFC': rfc,
      'CURP': curp,
      'DireccionCompleta': direccionCompleta,
      'Delegacion': delegacion,
      'CodigoPostal': codigoPostal,
      'Colonia': colonia,
      'Pais': pais,
      'Estado': estado,
      'Telefono': telefono,
      'Email': email,
      'RegimenFiscal': regimenFiscal,
      'UsoCFDI': usoCFDI,
    };
  }

  factory DatosPerfil.fromJson(dynamic json) {
    if (json != null && json is List && json.isNotEmpty) {
      json = json[0];
    }
    if (json != null && json is Map<String, dynamic>) {
      final perfil = DatosPerfil();
      perfil.clave = json['Clave'];
      perfil.nombre = json['Nombre'];
      perfil.rfc = json['RFC'];
      perfil.curp = json['CURP'];
      perfil.direccionCompleta = json['DireccionCompleta'];
      perfil.delegacion = json['Delegacion'];
      perfil.codigoPostal = json['CodigoPostal'];
      perfil.colonia = json['Colonia'];
      perfil.pais = json['Pais'];
      perfil.estado = json['Estado'];
      perfil.telefono = json['Telefono'];
      perfil.email = json['Email'];
      perfil.regimenFiscal = json['RegimenFiscal'];
      perfil.usoCFDI = json['UsoCFDI'];
      return perfil;
    }
    return DatosPerfil();
  }
}
