class Contrato {
  String contratoId;
  String empresa;

  Contrato(this.contratoId, this.empresa);
}

class DatosDesarrollos {
  static final DatosDesarrollos _instance = DatosDesarrollos._internal();

  DatosDesarrollos._internal();

  factory DatosDesarrollos() {
    return _instance;
  }

  List<String>? desarrollos;
  List<Contrato>? contratos;

  void setDesarrollos(List<String>? desarrollos) {
    this.desarrollos = desarrollos;
  }

  void setContratos(List<Contrato>? contratos) {
    this.contratos = contratos;
  }

  Map<String, dynamic> toJson() {
    return {
      'Desarrollos': desarrollos,
      'Contratos': contratos,
    };
  }

  factory DatosDesarrollos.fromJson(
      dynamic jsonDesarrollos, dynamic jsonContratos) {
    final desarrollosController = DatosDesarrollos();
    desarrollosController.desarrollos = [];
    desarrollosController.contratos = [];

    if (jsonDesarrollos != null) {
      List jsonList =
          jsonDesarrollos is List ? jsonDesarrollos : [jsonDesarrollos];

      for (var item in jsonList) {
        if (item is Map<String, dynamic> && item.containsKey('Empresa')) {
          var empresa = item['Empresa'];
          if (empresa is String) {
            desarrollosController.desarrollos!.add(empresa);
          } else if (empresa is List) {
            desarrollosController.desarrollos!
                .addAll(empresa.map((e) => e.toString()).toList());
          }
        }
      }
    }

    if (jsonContratos != null) {
      List jsonList = jsonContratos is List ? jsonContratos : [jsonContratos];

      for (var item in jsonList) {
        if (item is Map<String, dynamic> &&
            item.containsKey('ContratoID') &&
            item.containsKey('Empresa')) {
          desarrollosController.contratos!.add(Contrato(
              item['ContratoID'].toString(), item['Empresa'].toString()));
        }
      }
    }

    return desarrollosController;
  }

  bool containsDesarrollo(String desarrollo) {
    return desarrollos?.contains(desarrollo) ?? false;
  }

  List<Contrato> getContratos(String desarrollo) {
    List<Contrato> _contratos = [];
    for (var item in contratos!) {
      if (item.empresa == desarrollo) {
        _contratos.add(item);
      }
    }
    return _contratos;
  }
}
