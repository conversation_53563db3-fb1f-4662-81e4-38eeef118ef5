import 'package:get/get.dart';
import 'package:pdf/widgets.dart' as pw;

class Mensualidad {
  const Mensualidad(
    this.movimiento,
    this.fechaEmision,
    this.referencia,
  );

  final String movimiento;
  final String fechaEmision;
  final String referencia;

  String getIndex(int index) {
    switch (index) {
      case 0:
        return movimiento;
      case 1:
        return fechaEmision;
      case 2:
        return referencia;
    }
    return '';
  }
}

final lorem = pw.LoremText();

class TicketEdcController extends GetxController {
  RxString _nombreEmpresa = ''.obs;
  RxString _rfcEmpresa = ''.obs;
  RxString _sucursalUsuario = ''.obs;

  set nombreEmpresa(String _) => _nombreEmpresa.value = _;
  set rfcEmpresa(String _) => _rfcEmpresa.value = _;
  set sucursalUsuario(String _) => _sucursalUsuario.value = _;
  String get nombreEmpresa => _nombreEmpresa.value;
  String get rfcEmpresa => _rfcEmpresa.value;
  String get sucursalUsuario => _sucursalUsuario.value;

  RxString _claveCliente = ''.obs;
  RxString _nombreCliente = ''.obs;

  set claveCliente(String _) => _claveCliente.value = _;
  set nombreCliente(String _) => _nombreCliente.value = _;
  String get claveCliente => _claveCliente.value;
  String get nombreCliente => _nombreCliente.value;

  RxString _fechaVenta = ''.obs;

  set fechaVenta(String _) => _fechaVenta.value = _;
  String get fechaVenta => _fechaVenta.value;

  RxString _serieFolio = ''.obs;

  set serieFolio(String _) => _serieFolio.value = _;
  String get serieFolio => _serieFolio.value;

  List<Mensualidad> products = [];

  addProductI(Mensualidad _) {
    products.add(_);
  }

  clearProducts() {
    products.clear();
  }

  reset() {
    _nombreEmpresa.value = '';
    _rfcEmpresa.value = '';
    _sucursalUsuario.value = '';
    _claveCliente.value = '';
    _nombreCliente.value = '';
    _fechaVenta.value = '';
    _serieFolio.value = '';
    clearProducts();
  }
}
