class FacturaController {
  static final FacturaController _instance = FacturaController._internal();

  FacturaController._internal();

  factory FacturaController() {
    return _instance;
  }

  String _route = '';

  addToRoute(String _, {bool isFinal = false}) {
    _route += _ + (isFinal ? '' : '|');
  }

  set route(String _) {
    _route = _;
  }

  String get route => _route;

  reset() {
    _route = '';
  }
}
