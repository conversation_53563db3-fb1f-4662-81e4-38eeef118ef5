class DatosNotificaciones {
  static final DatosNotificaciones _instance = DatosNotificaciones._internal();

  DatosNotificaciones._internal();

  factory DatosNotificaciones() {
    return _instance;
  }

  List<dynamic> _notificaciones = [];
  bool _hasUnreadNotifications = false;

  List<dynamic> get notificaciones => _notificaciones;

  set notificaciones(List<dynamic> value) {
    _notificaciones = value;
  }

  bool get hasUnreadNotifications => _hasUnreadNotifications;

  set hasUnreadNotifications(bool value) {
    _hasUnreadNotifications = value;
  }

  void markAllAsRead() {
    _hasUnreadNotifications = false;
  }

  void addNotification(dynamic notification) {
    _notificaciones.insert(0, notification);
    _hasUnreadNotifications = true;
  }

  void removeNotification(int index) {
    if (index >= 0 && index < _notificaciones.length) {
      _notificaciones.removeAt(index);
      // Update unread status if needed
      _updateUnreadStatus();
    }
  }

  void _updateUnreadStatus() {
    // Check if there are any unread notifications
    // For now, we'll just set it to false if the list is empty
    if (_notificaciones.isEmpty) {
      _hasUnreadNotifications = false;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'notificaciones': _notificaciones,
      'hasUnreadNotifications': _hasUnreadNotifications,
    };
  }

  factory DatosNotificaciones.fromJson(dynamic json) {
    if (json != null && json is Map<String, dynamic>) {
      final notificaciones = DatosNotificaciones();
      notificaciones._notificaciones = json['notificaciones'] ?? [];
      notificaciones._hasUnreadNotifications =
          json['hasUnreadNotifications'] ?? false;
      return notificaciones;
    }
    return DatosNotificaciones();
  }
}
