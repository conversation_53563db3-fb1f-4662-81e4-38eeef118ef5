import 'package:easy_pdf_viewer/easy_pdf_viewer.dart';

class DatosAvisoPrivacidad {
  static final DatosAvisoPrivacidad _instance =
      DatosAvisoPrivacidad._internal();

  DatosAvisoPrivacidad._internal() {}

  factory DatosAvisoPrivacidad() {
    return _instance;
  }

  String _documentSelected = '';

  set documentSelected(String _) {
    _documentSelected = _;
  }

  String get documentSelected => _documentSelected;

  getDocument(String _) async {
    switch (_) {
      case 'FZB':
        return await PDFDocument.fromAsset('assets/app/PrivacidadFZB.pdf');
      case 'FZR':
        return await PDFDocument.fromAsset('assets/app/PrivacidadFZR.pdf');
      case 'PDI':
        return await PDFDocument.fromAsset('assets/app/PrivacidadPDI.pdf');
    }
  }

  static bool noDocumentSelected() {
    return _instance._documentSelected == '';
  }
}
