class FacturarA {
  String cliente;
  String nombre;
  String id;
  String nombreFacturarA;
  String rfc;

  FacturarA(this.cliente, this.nombre, this.id, this.nombreFacturarA, this.rfc);
}

class DatosFacturarA {
  static final DatosFacturarA _instance = DatosFacturarA._internal();

  DatosFacturarA._internal();

  factory DatosFacturarA() {
    return _instance;
  }

  List<FacturarA>? facturarAs;

  void setFacturarAs(List<FacturarA> facturarAs) {
    this.facturarAs = facturarAs;
  }

  Map<String, dynamic> toJson() {
    return {
      'FacturarAs': facturarAs,
    };
  }

  factory DatosFacturarA.fromJson(dynamic jsonFacturarAs) {
    final facturarAsController = DatosFacturarA();
    facturarAsController.facturarAs = [];

    if (jsonFacturarAs != null) {
      List jsonList =
          jsonFacturarAs is List ? jsonFacturarAs : [jsonFacturarAs];

      for (var item in jsonList) {
        if (item is Map<String, dynamic> && item.containsKey('Cliente')) {
          facturarAsController.facturarAs!.add(FacturarA(
            item['Cliente'].toString(),
            item['Nombre'].toString(),
            item['ID'].toString(),
            item['NombreFacturarA'].toString(),
            item['RFC'].toString(),
          ));
        }
      }
    }

    return facturarAsController;
  }

  List<Map<String, dynamic>> toBillingOptions() {
    List<Map<String, dynamic>> billingOptions = [];
    if (facturarAs != null) {
      for (var facturarA in facturarAs!) {
        billingOptions.add({
          'value': facturarA.id,
          'label': '${facturarA.rfc} - ${facturarA.nombreFacturarA}'
        });
      }
    }
    return billingOptions;
  }

  bool containsFacturarA(String facturarA) {
    return facturarAs != null && facturarAs!.contains(facturarA);
  }

  bool shouldShowDropdown() {
    bool isEmpty = false;
    if (facturarAs != null) {
      if (facturarAs!.length > 0) {
        isEmpty = true;
      }
    }
    return isEmpty;
  }
}
