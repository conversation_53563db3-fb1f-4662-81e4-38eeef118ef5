// Flutter Core
import 'dart:io';

import 'package:flutter/material.dart';

// Third-Party Packages
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project Imports (same package)
import '/src/DAPackagesRef.dart';
import '/src/app_config/app_config.dart';
import 'app_config/api_request.dart';
import 'controllers/desarrollos_controller.dart';
import 'controllers/facturar_a_controller.dart';
import 'controllers/perfil_controller.dart';
import 'controllers/notificaciones_controller.dart';

class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isLoading = true;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  @override
  void initState() {
    super.initState();
    loadSesion();

    _setupPushNotifications();

    DAController.refAvatar(context, () {
      setState(() {});
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _setupPushNotifications() async {
    try {
      // Solicitar permisos
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      // Para iOS, obtener el token APNS primero
      if (Platform.isIOS) {
        String? apnsToken = await _firebaseMessaging.getAPNSToken();
        print("APNS Token: $apnsToken");
        // Esperar un momento si el token APNS no está disponible inmediatamente
        if (apnsToken == null) {
          await Future.delayed(Duration(seconds: 2));
          apnsToken = await _firebaseMessaging.getAPNSToken();
        }
      }

      // Obtener el token FCM
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString('fcmToken', token);
        print("FCM Token: $token");

        DARequestModel _req = await ApiRequest.guardarToken;
        await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);
      }

      // Inicializar controlador de notificaciones
      DatosNotificaciones notificacionesController = DatosNotificaciones();

      // Manejar notificaciones cuando la app está en primer plano
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print(
            "Notificación recibida en primer plano: ${message.notification?.title}");

        // Crear objeto de notificación
        Map<String, dynamic> notificacion = {
          'Titulo': message.notification?.title ?? 'Nueva notificación',
          'Mensaje': message.notification?.body ?? '',
          'Fecha': DateTime.now().toString(),
          'Tipo': message.data['tipo'] ?? '',
          'Id': message.data['id'] ?? '',
        };

        // Guardar notificación en el controlador
        notificacionesController.addNotification(notificacion);

        // Mostrar toast
        DAToast(context, "Nueva notificación: ${message.notification?.title}");
      });

      // Manejar cuando se toca la notificación con la app en segundo plano
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print(
            "Notificación abierta desde segundo plano: ${message.notification?.title}");

        // Crear objeto de notificación
        Map<String, dynamic> notificacion = {
          'Titulo': message.notification?.title ?? 'Nueva notificación',
          'Mensaje': message.notification?.body ?? '',
          'Fecha': DateTime.now().toString(),
          'Tipo': message.data['tipo'] ?? '',
          'Id': message.data['id'] ?? '',
        };

        // Guardar notificación en el controlador
        notificacionesController.addNotification(notificacion);

        // Manejar la navegación
        _handleNotificationClick(message.data);
      });

      // Manejar notificación con app terminada
      FirebaseMessaging.instance.getInitialMessage().then((message) {
        if (message != null) {
          print(
              "Notificación abierta desde app terminada: ${message.notification?.title}");

          // Crear objeto de notificación
          Map<String, dynamic> notificacion = {
            'Titulo': message.notification?.title ?? 'Nueva notificación',
            'Mensaje': message.notification?.body ?? '',
            'Fecha': DateTime.now().toString(),
            'Tipo': message.data['tipo'] ?? '',
            'Id': message.data['id'] ?? '',
          };

          // Guardar notificación en el controlador
          notificacionesController.addNotification(notificacion);

          // Manejar la navegación
          _handleNotificationClick(message.data);
        }
      });
    } catch (e) {
      print("Error en notificaciones: $e");
    }
  }

  void _handleNotificationClick(Map<String, dynamic> data) {
    // Ejemplo: Navegar a una sección específica
    if (data['tipo'] == 'desarrollo') {
      Navigator.pushNamed(context, '/detalle_desarrollo',
          arguments: data['id']);
    }
  }

  @override
  Widget build(BuildContext context) {
    // No borrar, es para logout
    Provider.of<DASessionProvider>(context);
    if (!DASessionProvider.toLogin) {
      AppConfig.logOut();
    }

    return LoadingOverlay(
      isLoading: _isLoading,
      child: Scaffold(
        key: _scaffoldKey,
        body: CustomScrollView(
          slivers: <Widget>[
            DATitleBig(
              prefix: 'Intelisis',
              title: '',
            ),
          ],
        ),
        backgroundColor: Color.fromRGBO(244, 243, 243, 1),
      ),
    );
  }

  Future<void> loadSesion() async {
    try {
      // Descargamos data inicial
      DAUsuarioProvider usrProv = DAUsuarioProvider();
      DASessionProvider _eProv = DASessionProvider();

      AppCfgModel appConfig =
          appCfgModelFromJson(await _eProv.getAppConfig(AppConfig.licence));
      await usrProv.setAppConfig(appConfig);
      //await _eProv.setAgente();
      await getPerfil();
      await getDesarrollos();
      await getFacturarA();

      setState(() {
        _isLoading = false;
      });

      gotoHome(null);
    } catch (e) {
      print(e.toString());
      DAToast(context, e.toString(), useFlutterToast: true);
      AppConfig.logOut();
      setState(() {});
    }
  }

  getPerfil() async {
    DARequestModel _req = ApiRequest.perfil();
    dynamic _res = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    DatosPerfil.fromJson(_res);
  }

  getDesarrollos() async {
    DARequestModel _req = ApiRequest.getDesarrollos();
    dynamic _res = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    print(_res);

    DatosDesarrollos.fromJson(_res[1], _res[2]);
  }

  getFacturarA() async {
    DARequestModel _req = ApiRequest.getFacturarA();
    dynamic _res = await ApiRequest.execAPI(_req.uriReq, _req.bodyReq);

    DatosFacturarA.fromJson(_res);
  }

  Future<void> gotoHome(Object? value) async {
    await Navigator.pushNamed(
      context,
      AppConfig.homeLayout,
      arguments: AppConfig.homeLayoutArgs(context),
    ).then(gotoHome);
  }
}
