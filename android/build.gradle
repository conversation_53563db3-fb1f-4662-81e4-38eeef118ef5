allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

subprojects {
    afterEvaluate { project ->
        if (project.name == 'geolocator_android' &&
                (project.plugins.hasPlugin("com.android.application") ||
                        project.plugins.hasPlugin("com.android.library"))) {
            project.android {
                compileSdkVersion 35
                buildToolsVersion "35.0.0"
                defaultConfig {
                    minSdkVersion 30
                }
            }
        }
        if (project.name == 'easy_pdf_viewer' &&
                (project.plugins.hasPlugin("com.android.application") ||
                        project.plugins.hasPlugin("com.android.library"))) {
            project.android {
                compileSdkVersion 34
                buildToolsVersion "34.0.0"
            }
        }
    }
}

subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
