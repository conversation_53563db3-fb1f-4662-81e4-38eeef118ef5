{"project_info": {"project_number": "540841514436", "firebase_url": "https://intelisis-tuakfe.firebaseio.com", "project_id": "intelisis-tuakfe", "storage_bucket": "intelisis-tuakfe.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:540841514436:android:39dfdaa00c849c24440091", "android_client_info": {"package_name": "com.Intelisis.autorizaciones_flutter"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:540841514436:android:c02f15d4ade319b6440091", "android_client_info": {"package_name": "com.Intelisis.serviciosclientes"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:540841514436:android:5e5b9ba0e8152c42440091", "android_client_info": {"package_name": "com.Intelisis.tecnicosFlutter"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:540841514436:android:ad7dd067034d7af7440091", "android_client_info": {"package_name": "com.intelisis.autorizacionmovil"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:540841514436:android:a03c1e968b202cb5440091", "android_client_info": {"package_name": "com.intelisis.chataimovil"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:540841514436:android:6d01546def36b3d9440091", "android_client_info": {"package_name": "com.intelisis.pagosmovil"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:540841514436:android:01a36896dc717fce440091", "android_client_info": {"package_name": "com.intelisis.soporteclientesmovil"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:540841514436:android:7903ccddf6f7b492440091", "android_client_info": {"package_name": "com.intelisis.soportetecnicosmovil"}}, "oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDYKbg_Q8mEfcMFRTfiCwuu8XkmrnjwFYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "540841514436-gepu51ofts04ubei0h8ho8qr8fe4erfs.apps.googleusercontent.com", "client_type": 3}, {"client_id": "540841514436-21jgvp537she4el277nb3m3iiipi2f0b.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.intelisis.soporteclientesmovil"}}]}}}], "configuration_version": "1"}